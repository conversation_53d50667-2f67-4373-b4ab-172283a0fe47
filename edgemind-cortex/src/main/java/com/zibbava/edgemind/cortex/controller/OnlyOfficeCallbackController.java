package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.onlyoffice.model.documenteditor.Callback;
import com.zibbava.edgemind.cortex.service.OnlyOfficeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * ONLYOFFICE 回调处理控制器
 * 用于处理文档编辑后的状态回调
 */
@RestController
@RequestMapping("/api/onlyoffice/callback")
@RequiredArgsConstructor
@Slf4j
public class OnlyOfficeCallbackController {

    private final OnlyOfficeService onlyOfficeService;

    /**
     * 处理ONLYOFFICE编辑器回调
     * 当文档被编辑并保存时，ONLYOFFICE Document Server会调用此接口
     * 
     * @param nodeId 文件节点ID
     * @param callback 回调数据对象
     * @return 处理结果
     */
    @SaIgnore // 忽略权限验证，允许ONLYOFFICE服务器直接访问
    @PostMapping("/{nodeId}")
    public ResponseEntity<String> handleCallback(
            @PathVariable String nodeId,
            @RequestBody Callback callback) {
        
        log.info("收到ONLYOFFICE回调: nodeId={}", nodeId);
        
        boolean success = onlyOfficeService.processEditCallback(nodeId, callback);
        
        if (success) {
            return ResponseEntity.ok("{\"error\":0}"); // 成功响应
        } else {
            return ResponseEntity.ok("{\"error\":1}"); // 错误响应
        }
    }
} 