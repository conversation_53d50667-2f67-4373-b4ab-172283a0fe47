package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统设置实体类
 */
@Data
@TableName("sys_settings")
public class SystemSetting {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 设置键名
     */
    @TableField("setting_key")
    private String settingKey;

    /**
     * 设置值
     */
    @TableField("setting_value")
    private String settingValue;

    /**
     * 设置描述
     */
    @TableField("description")
    private String description;

    /**
     * 设置分组
     */
    @TableField("setting_group")
    private String settingGroup;

    /**
     * 设置类型
     */
    @TableField("setting_type")
    private String settingType;

    /**
     * 是否系统内置
     */
    @TableField("is_system")
    private Boolean isSystem;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private Long updateBy;

    // ----- 枚举类型 -----

    /**
     * 设置类型枚举
     */
    public enum SettingType {
        STRING("字符串"),
        NUMBER("数字"),
        BOOLEAN("布尔值"),
        JSON("JSON"),
        TEXT("文本"),
        PASSWORD("密码"),
        EMAIL("邮箱"),
        URL("链接"),
        FILE("文件"),
        IMAGE("图片");

        private final String description;

        SettingType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 设置分组枚举
     */
    public enum SettingGroup {
        SYSTEM("系统设置"),
        SECURITY("安全设置"),
        EMAIL("邮件设置"),
        SMS("短信设置"),
        STORAGE("存储设置"),
        AI("AI设置"),
        APPEARANCE("外观设置"),
        NOTIFICATION("通知设置"),
        BACKUP("备份设置"),
        LOG("日志设置"),
        OTHER("其他设置");

        private final String description;

        SettingGroup(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
