package com.zibbava.edgemind.cortex.scheduler;

import com.zibbava.edgemind.cortex.common.constants.SettingKeys;
import com.zibbava.edgemind.cortex.dto.LicenseCheckResult;
import com.zibbava.edgemind.cortex.service.LicenseService;
import com.zibbava.edgemind.cortex.service.SystemSettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 许可证定期验证调度器
 * 每10分钟验证一次许可证，失败10次判断失败，一次成功即恢复状态
 */
@Component
@EnableScheduling
@RequiredArgsConstructor
@Slf4j
public class LicenseVerificationScheduler {

    private final LicenseService licenseService;
    private final SystemSettingsService systemSettingsService;

    // 连续失败次数
    private final AtomicInteger consecutiveFailures = new AtomicInteger(0);
    
    // 最大连续失败次数（超过此数值才重置授权状态）
    private static final int MAX_CONSECUTIVE_FAILURES = 10;

    /**
     * 定期验证许可证
     * 每10分钟执行一次
     */
    @Scheduled(fixedRate = 600000) // 每10分钟验证一次
    public void verifyLicense() {
        log.debug("📋 开始执行定期许可证验证");
        
        try {
            // 检查授权状态（包括硬件指纹验证）
            LicenseCheckResult checkResult = licenseService.checkLicenseStatus();
            
            if (checkResult.isLicensed()) {
                // 验证成功
                handleVerificationSuccess();
            } else {
                // 验证失败
                handleVerificationFailure(checkResult);
            }
            
        } catch (Exception e) {
            log.error("❌ 定期许可证验证出错", e);
            handleVerificationError(e);
        }
    }

    /**
     * 处理验证成功的情况
     */
    private void handleVerificationSuccess() {
        int previousFailures = consecutiveFailures.getAndSet(0);
        
        if (previousFailures > 0) {
            log.info("✅ 许可证验证恢复正常（之前连续失败{}次）", previousFailures);
            
            // 如果之前有失败记录，检查并恢复授权状态
            restoreLicenseStatusIfNeeded();
        } else {
            log.debug("✅ 定期许可证验证成功");
        }
    }

    /**
     * 处理验证失败的情况
     */
    private void handleVerificationFailure(LicenseCheckResult checkResult) {
        int failureCount = consecutiveFailures.incrementAndGet();
        
        String failReason = checkResult.getFailReason();
        String failMessage = checkResult.getFailMessage();
        
        log.warn("⚠ 许可证验证失败 ({}/{}次): {} - {}", 
                failureCount, MAX_CONSECUTIVE_FAILURES, failReason, failMessage);
        
        // 只有在连续失败次数达到阈值时才重置授权状态
        if (failureCount >= MAX_CONSECUTIVE_FAILURES) {
            log.error("❌ 连续验证失败{}次，重置授权状态", failureCount);
            resetLicenseStatus(failMessage);
        } else {
            log.info("🕐 暂时保持授权状态，将在10分钟后重试 (剩余{}次机会)", 
                    MAX_CONSECUTIVE_FAILURES - failureCount);
        }
    }

    /**
     * 处理验证错误的情况
     */
    private void handleVerificationError(Exception e) {
        int failureCount = consecutiveFailures.incrementAndGet();
        
        log.error("❌ 许可证验证系统错误 ({}/{}次): {}", 
                failureCount, MAX_CONSECUTIVE_FAILURES, e.getMessage());
        
        // 系统错误也计入失败次数
        if (failureCount >= MAX_CONSECUTIVE_FAILURES) {
            log.error("❌ 连续系统错误{}次，重置授权状态", failureCount);
            resetLicenseStatus("系统错误: " + e.getMessage());
        }
    }

    /**
     * 检查并恢复授权状态（如果需要）
     */
    private void restoreLicenseStatusIfNeeded() {
        try {
            // 获取当前授权状态
            String currentStatus = systemSettingsService.getSettingValue(SettingKeys.License.STATUS);
            
            // 如果当前状态为0（未授权），则恢复为1（已授权）
            if ("0".equals(currentStatus)) {
                systemSettingsService.updateSettingValue(
                        SettingKeys.License.STATUS, "1", "授权状态");
                
                // 清除缓存，确保下次查询能获取最新状态
                licenseService.clearCache();
                
                log.info("🔄 授权状态已恢复: 验证成功后自动恢复授权状态");
            } else {
                log.debug("📋 当前授权状态正常，无需恢复");
            }
        } catch (Exception e) {
            log.error("❌ 恢复授权状态时出错", e);
        }
    }

    /**
     * 重置授权状态
     */
    private void resetLicenseStatus(String reason) {
        try {
            systemSettingsService.updateSettingValue(
                    SettingKeys.License.STATUS, "0", "授权状态");
            
            // 清除缓存，确保下次查询能获取最新状态
            licenseService.clearCache();
            
            log.error("🚫 授权状态已重置: {}", reason);
        } catch (Exception e) {
            log.error("❌ 重置授权状态时出错", e);
        }
    }
}
