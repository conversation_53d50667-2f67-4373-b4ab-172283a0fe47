/**
 * 深度思考切换组件
 * 通用组件，可在普通对话和知识库对话中使用
 */

/**
 * 深度思考切换按钮类
 */
class DeepThinkingToggle {
    constructor(buttonElement) {
        this.button = buttonElement;
        this.isEnabled = true; // 默认开启
        this.isThinking = false; // 是否正在思考中
        
        this.init();
    }
    
    /**
     * 初始化组件
     */
    init() {
        // 设置初始状态
        this.updateButtonState();
        
        // 绑定点击事件
        this.button.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggle();
        });
        
        // 初始化 Bootstrap tooltip
        if (this.button.hasAttribute('data-bs-toggle')) {
            new bootstrap.Tooltip(this.button);
        }
        

    }
    
    /**
     * 切换深度思考状态
     */
    toggle() {
        // 手动隐藏tooltip
        const tooltipInstance = bootstrap.Tooltip.getInstance(this.button);
        if (tooltipInstance) {
            tooltipInstance.hide();
        }
        
        // 移除思考中状态的限制，让按钮随时可以切换
        this.isEnabled = !this.isEnabled;
        this.updateButtonState();
        
        // 触发状态变化事件
        this.dispatchStateChangeEvent();
        

    }
    
    /**
     * 设置深度思考状态
     */
    setEnabled(enabled) {
        // 手动隐藏tooltip
        const tooltipInstance = bootstrap.Tooltip.getInstance(this.button);
        if (tooltipInstance) {
            tooltipInstance.hide();
        }
        
        // 移除思考中状态的限制，让状态随时可以设置
        this.isEnabled = enabled;
        this.updateButtonState();
        this.dispatchStateChangeEvent();
    }
    
    /**
     * 获取当前状态
     */
    getEnabled() {
        return this.isEnabled;
    }
    
    /**
     * 设置思考中状态（仅用于视觉指示，不影响按钮功能）
     */
    setThinking(thinking) {
        this.isThinking = thinking;
        this.updateButtonState();
        

    }
    
    /**
     * 更新按钮状态
     */
    updateButtonState() {
        // 移除所有状态类
        this.button.classList.remove('active', 'thinking');
        
        // 不再禁用按钮，保持始终可点击
        this.button.disabled = false;
        
        // 添加对应状态类
        if (this.isEnabled) {
            this.button.classList.add('active');
        }
        
        if (this.isThinking) {
            this.button.classList.add('thinking');
        }
        
        // 更新 tooltip 文本
        let tooltipText;
        if (this.isThinking) {
            tooltipText = this.isEnabled ? '深度思考' : '深度思考';
        } else {
            tooltipText = this.isEnabled ? '深度思考' : '深度思考';
        }
        
        this.button.setAttribute('title', tooltipText);
        this.button.setAttribute('data-bs-original-title', tooltipText);
        
        // 如果 tooltip 已初始化，更新其内容
        const tooltipInstance = bootstrap.Tooltip.getInstance(this.button);
        if (tooltipInstance) {
            tooltipInstance.setContent({ '.tooltip-inner': tooltipText });
        }
    }
    
    /**
     * 触发状态变化事件
     */
    dispatchStateChangeEvent() {
        const event = new CustomEvent('deepThinkingStateChange', {
            detail: {
                enabled: this.isEnabled,
                button: this.button
            },
            bubbles: true
        });
        
        this.button.dispatchEvent(event);
    }
}

/**
 * 深度思考管理器
 */
class DeepThinkingManager {
    constructor() {
        this.toggles = new Map();
        this.globalState = true; // 全局默认状态
    }
    
    /**
     * 注册深度思考切换按钮
     */
    register(buttonElement, id = null) {
        const toggleId = id || this.generateId(buttonElement);
        
        if (this.toggles.has(toggleId)) {
            console.warn(`[深度思考管理器] 切换按钮 ${toggleId} 已存在`);
            return this.toggles.get(toggleId);
        }
        
        const toggle = new DeepThinkingToggle(buttonElement);
        toggle.setEnabled(this.globalState);
        
        this.toggles.set(toggleId, toggle);
        
        // 监听状态变化
        buttonElement.addEventListener('deepThinkingStateChange', (e) => {
            this.onStateChange(toggleId, e.detail.enabled);
        });
        

        return toggle;
    }
    
    /**
     * 获取切换按钮
     */
    get(id) {
        return this.toggles.get(id);
    }
    
    /**
     * 设置全局状态（影响所有切换按钮）
     */
    setGlobalState(enabled) {
        this.globalState = enabled;
        
        this.toggles.forEach((toggle) => {
            toggle.setEnabled(enabled);
        });
        

    }
    
    /**
     * 获取指定ID的状态
     */
    getState(id) {
        const toggle = this.toggles.get(id);
        return toggle ? toggle.getEnabled() : this.globalState;
    }
    
    /**
     * 设置思考中状态
     */
    setThinking(id, thinking) {
        const toggle = this.toggles.get(id);
        if (toggle) {
            toggle.setThinking(thinking);
        }
    }
    
    /**
     * 自动发现并注册页面中的深度思考按钮
     */
    autoDiscover() {
        const buttons = document.querySelectorAll('.deep-thinking-toggle');
        let count = 0;
        
        buttons.forEach((button) => {
            const id = button.dataset.deepThinkingId || button.id || null;
            this.register(button, id);
            count++;
        });
        

        return count;
    }
    
    /**
     * 生成唯一ID
     */
    generateId(element) {
        if (element.id) {
            return element.id;
        }
        
        if (element.dataset.deepThinkingId) {
            return element.dataset.deepThinkingId;
        }
        
        // 基于元素在DOM中的位置生成ID
        const elements = document.querySelectorAll('.deep-thinking-toggle');
        const index = Array.from(elements).indexOf(element);
        return `deep-thinking-${index}`;
    }
    
    /**
     * 状态变化回调
     */
    onStateChange(id, enabled) {

        
        // 可以在这里添加其他逻辑，比如同步到本地存储
        this.saveStateToLocalStorage(id, enabled);
    }
    
    /**
     * 保存状态到本地存储
     */
    saveStateToLocalStorage(id, enabled) {
        try {
            const states = JSON.parse(localStorage.getItem('deepThinkingStates') || '{}');
            states[id] = enabled;
            localStorage.setItem('deepThinkingStates', JSON.stringify(states));
        } catch (e) {
            console.warn('[深度思考管理器] 保存到本地存储失败:', e);
        }
    }
    
    /**
     * 从本地存储加载状态
     */
    loadStateFromLocalStorage(id) {
        try {
            const states = JSON.parse(localStorage.getItem('deepThinkingStates') || '{}');
            return states.hasOwnProperty(id) ? states[id] : this.globalState;
        } catch (e) {
            console.warn('[深度思考管理器] 从本地存储加载失败:', e);
            return this.globalState;
        }
    }
    
    /**
     * 恢复所有按钮的状态
     */
    restoreStates() {
        this.toggles.forEach((toggle, id) => {
            const savedState = this.loadStateFromLocalStorage(id);
            toggle.setEnabled(savedState);
        });
        

    }
}

// 创建全局管理器实例
const deepThinkingManager = new DeepThinkingManager();

// 将管理器暴露到全局window对象
window.deepThinkingManager = deepThinkingManager;

// 页面加载完成后自动发现并初始化
document.addEventListener('DOMContentLoaded', () => {

    deepThinkingManager.autoDiscover();
    deepThinkingManager.restoreStates();
});

// 导出
export {
    DeepThinkingToggle,
    DeepThinkingManager,
    deepThinkingManager
};