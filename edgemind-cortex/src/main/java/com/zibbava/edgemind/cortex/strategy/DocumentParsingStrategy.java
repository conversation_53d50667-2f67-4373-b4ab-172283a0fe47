package com.zibbava.edgemind.cortex.strategy;

import dev.langchain4j.data.document.Document;

/**
 * 文档解析策略接口
 * 定义不同类型文档的解析方法
 */
public interface DocumentParsingStrategy {
    
    /**
     * 解析文档内容
     * @param fileContent 文件内容字节数组
     * @param fileName 文件名（用于判断文件类型）
     * @return 解析后的Document对象
     * @throws Exception 解析异常
     */
    Document parseDocument(byte[] fileContent, String fileName) throws Exception;
    
    /**
     * 判断是否支持该文件类型
     * @param fileName 文件名
     * @return 是否支持
     */
    boolean supports(String fileName);
}