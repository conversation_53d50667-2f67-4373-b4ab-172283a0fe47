package com.zibbava.edgemind.cortex.service;

/**
 * 异步文档索引服务接口。
 * 定义了触发文档向量化处理和向量数据清理的操作。
 * 实现类应使用 Spring 的 @Async 注解来执行这些耗时操作，避免阻塞主线程。
 */
public interface DocumentIndexingService {

    /**
     * 触发指定文档的异步索引流程。
     * 此方法会读取文档内容，进行分块、向量化，并将结果存入向量数据库。
     * 同时会更新数据库中对应文档的向量化状态。
     *
     * @param documentId 文档实体 ID (对应 kb_knowledge_documents.document_id)。
     */
    void startIndexing(String documentId);

    /**
     * 触发清理与指定文件节点关联的所有向量数据的异步流程。
     * 当文件节点被删除时调用此方法，以保持向量数据库与主数据库同步。
     *
     * @param nodeId 被删除的文件节点 ID (对应 kb_knowledge_nodes.node_id)。
     */
    void deleteVectors(String nodeId);

    /**
     * 清空所有向量数据和分片记录
     * 在同步知识库或重置知识库时使用
     */
    void clearAllVectors();
}