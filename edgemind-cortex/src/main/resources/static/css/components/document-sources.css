/* 文档来源显示样式 - 极简风格 */
.document-sources-container {
    margin: 1rem 0;
    padding: 0;
    background: transparent;
    border: none;
    font-size: 0.9rem;
}

.sources-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-weight: 500;
    color: #6c757d;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.sources-header i {
    margin-right: 6px;
    color: #6c757d;
    font-size: 0.9rem;
}

.sources-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 0;
    padding: 0;
}

.source-item {
    display: inline-block;
}

.source-link {
    display: inline;
    color: #007bff;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 400;
    cursor: pointer;
    margin-right: 0.5rem;
}

/* 移除文件图标以保持极简风格 */

.source-link:hover {
    text-decoration: underline;
}

.source-link:active {
    color: #0056b3;
}

/* 响应式设计 */
@media (max-width: 576px) {
    .document-sources-container {
        margin: 8px 0;
        padding: 10px 12px;
    }
    
    .sources-list {
        gap: 6px;
    }
    
    .source-link {
        padding: 5px 10px;
        font-size: 0.8rem;
        max-width: 150px;
    }
}

/* 深色主题支持（如果需要） */
@media (prefers-color-scheme: dark) {
    .document-sources-container {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .sources-header {
        color: #e2e8f0;
    }
    
    .sources-header i {
        color: #a0aec0;
    }
    
    .source-link {
        color: #60a5fa;
    }
    
    .source-link:hover {
        text-decoration: underline;
    }
    
    .source-link:active {
        color: #3b82f6;
    }
}

/* 工具提示样式 */
.source-link[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 8px;
    max-width: 400px;
    word-wrap: break-word;
    white-space: normal;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    /* 移除动画效果 */
}

.source-link[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    margin-bottom: 2px;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 响应式工具提示 */
@media (max-width: 576px) {
    .source-link[title]:hover::after {
        max-width: 280px;
        font-size: 0.75rem;
        padding: 6px 10px;
    }
}