package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.common.enums.VectorStatus;
import com.zibbava.edgemind.cortex.common.exception.AccessDeniedException;
import com.zibbava.edgemind.cortex.common.exception.ResourceNotFoundException;
import com.zibbava.edgemind.cortex.dto.knowledgebase.KnowledgeNodeDto;
import com.zibbava.edgemind.cortex.dto.knowledgebase.NodeCreateRequest;
import com.zibbava.edgemind.cortex.dto.knowledgebase.NodeUpdateRequest;
import com.zibbava.edgemind.cortex.entity.KnowledgeDocument;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 知识库管理服务接口。
 * 定义了对知识空间、知识库节点（文件夹/文件）以及关联文档内容的管理操作。
 */
public interface KnowledgeBaseService {

    /**
     * 获取当前用户可访问的所有知识空间列表。
     * 包括所有团队空间以及用户自己的私人空间。
     *
     * @param userId 当前登录用户的 ID。
     * @return 可访问的知识空间实体列表。
     */
    List<KnowledgeSpace> getAccessibleSpaces(Long userId);

    /**
     * 获取或创建知识空间。
     * 根据类型（私人/团队）检查是否已存在相应的知识空间，如果不存在则创建。
     * 对于私人空间，每个用户只能有一个；对于团队空间，系统中只有一个公共团队空间。
     *
     * @param userId 用户ID
     * @param isPrivate 是否为私人空间 (true=私人, false=团队)
     * @return 获取到的或新创建的知识空间实体
     */
    KnowledgeSpace getOrCreateKnowledgeSpace(Long userId, boolean isPrivate);

    /**
     * 获取指定知识空间的完整节点树结构。
     * 返回的 DTO 列表包含了层级关系。
     *
     * @param spaceId 要查询的知识空间 ID。
     * @return 包含根节点及其所有子节点的 DTO 列表。
     * @throws AccessDeniedException 如果当前用户无权访问该空间。
     * @throws ResourceNotFoundException 如果空间不存在。
     */
    List<KnowledgeNodeDto> getSpaceTree(String spaceId);

    /**
     * 创建一个新的知识库节点（文件夹或文件）。
     * 如果创建的是文件节点，会自动创建关联的 {@link KnowledgeDocument} 记录。
     *
     * @param request 包含节点信息的创建请求对象。
     * @return 创建成功后的节点信息 DTO。
     * @throws AccessDeniedException 如果用户无权在目标空间或父节点下创建。
     * @throws ResourceNotFoundException 如果指定的父节点不存在。
     * @throws IllegalArgumentException 如果请求参数不合法（例如，父节点不是文件夹）。
     */
    KnowledgeNodeDto createNode(NodeCreateRequest request);

    /**
     * 更新现有知识库节点的属性。
     * 支持重命名操作和移动节点到新的父节点下。
     *
     * @param nodeId  要更新的节点 ID。
     * @param request 包含更新信息的请求对象（新名称和可选的新父节点ID）。
     * @return 更新成功后的节点信息 DTO。
     * @throws ResourceNotFoundException 如果节点不存在。
     * @throws AccessDeniedException   如果用户无权修改该节点。
     * @throws IllegalArgumentException 如果请求参数不合法。
     */
    KnowledgeNodeDto updateNode(String nodeId, NodeUpdateRequest request);

    /**
     * 删除指定的知识库节点。
     * 如果删除的是文件夹，将递归删除其下的所有子节点、关联的文档记录以及对应的向量数据。
     * 删除操作会触发异步任务来清理向量库。
     *
     * @param nodeId 要删除的节点 ID。
     * @throws ResourceNotFoundException 如果节点不存在。
     * @throws AccessDeniedException   如果用户无权删除该节点。
     */
    void deleteNode(String nodeId);

    /**
     * 上传文档内容到指定的文件节点，或者更新现有文档内容。
     * 会比较文件内容的哈希值，如果内容未变则不执行更新和重新索引。
     * 成功上传或更新后，会触发异步的文档索引任务。
     *
     * @param nodeId 文件节点的 ID (必须是 type=FILE 的节点)。
     * @param file   通过 Multipart 请求上传的文件。
     * @return 更新后的文档实体信息 {@link KnowledgeDocument}。
     * @throws ResourceNotFoundException 如果节点不存在或不是文件类型。
     * @throws AccessDeniedException   如果用户无权访问该文件节点。
     * @throws IOException 如果文件读写或保存过程中发生错误。
     * @throws IllegalArgumentException 如果上传的文件为空。
     */
    KnowledgeDocument uploadDocument(String nodeId, MultipartFile file, boolean skipIfExists) throws IOException;

    /**
     * 检查当前登录用户是否有权访问指定的知识空间。
     * 公开空间对所有登录用户开放，私人空间仅限所有者访问。
     *
     * @param spaceId 要检查的知识空间 ID。
     * @param userId  当前登录用户的 ID。
     * @return 如果有权访问则返回 true。
     * @throws ResourceNotFoundException 如果空间不存在。
     * @throws AccessDeniedException   如果用户无权访问。
     */
    boolean checkAccess(String spaceId, Long userId);

    /**
     * 获取指定文件夹节点下（递归）所有文件类型子节点的 ID 列表。
     * 主要供内部 RAG 检索时确定过滤范围使用。
     *
     * @param folderNodeId 文件夹节点的 ID。
     * @return 该文件夹下所有文件节点的 ID 列表；如果文件夹不存在或内部无文件，则返回空列表。
     */
    List<String> getAllFileNodeIdsInFolder(String folderNodeId);

    /**
     * 根据 ID 查找单个知识库节点实体。
     * 主要供内部服务（如 RAG 服务）获取节点信息使用。
     *
     * @param nodeId 节点 ID。
     * @return 查找到的 {@link KnowledgeNode} 实体；如果不存在则返回 null。
     */
    KnowledgeNode findNodeById(String nodeId);

    /**
     * 根据节点 ID 查找关联的文档记录。
     *
     * @param nodeId 文件节点的 ID。
     * @return 关联的 {@link KnowledgeDocument} 实体；如果不存在或节点非文件类型则返回 null。
     */
    KnowledgeDocument findDocumentByNodeId(String nodeId);

    /**
     * 重新触发指定文档节点的向量化处理。
     * 会将文档状态设置为 PENDING 并调用索引服务。
     *
     * @param nodeId 要重新解析的文件节点 ID。
     * @return 更新后的文档实体信息 {@link KnowledgeDocument}。
     * @throws ResourceNotFoundException 如果节点或关联文档不存在，或节点非文件类型。
     * @throws AccessDeniedException   如果用户无权访问该文件节点。
     */
    KnowledgeDocument reparseDocument(String nodeId);

    /**
     * 获取指定文件节点的文档向量化状态。
     *
     * @param nodeId 文件节点的 ID。
     * @return 文档的 VectorStatus；如果节点不存在、不是文件或没有关联文档，则可能抛出异常或返回特定状态。
     */
    VectorStatus getDocumentVectorStatus(String nodeId);

}