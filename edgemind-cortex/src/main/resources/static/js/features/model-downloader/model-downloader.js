/**
 * 模型中心
 * 用于展示和下载Ollama模型，并根据本地硬件配置提供推荐
 */

/**
 * 获取兼容性颜色相关的类名（用于文本和背景）
 * @param {number} score 兼容性分数
 * @returns {string} 简化颜色类名 (e.g., 'perfect', 'good')
 */
function getCompatibilityClassSimple(score) {
    if (score >= 98) return 'perfect';    // 100-98
    else if (score >= 90) return 'excellent';  // 97-90
    else if (score >= 80) return 'great';      // 89-80
    else if (score >= 70) return 'good';       // 79-70
    else if (score >= 60) return 'decent';     // 69-60
    else if (score >= 50) return 'moderate';   // 59-50
    else if (score >= 40) return 'fair';       // 49-40
    else if (score >= 30) return 'limited';    // 39-30
    else if (score >= 15) return 'poor';       // 29-15
    else if (score >= 5) return 'terrible';   // 14-5
    else return 'impossible';  // 4-0
}


// getRotationDegree and getCompatibilityRating are no longer needed for the card display

document.addEventListener('DOMContentLoaded', function() {
    const modelGrid = document.getElementById('model-grid');
    const loadingContainer = document.getElementById('loading-container');
    const noModelsMessage = document.getElementById('no-models-message');
    const systemInfoContainer = document.getElementById('system-info');
    const searchInput = document.getElementById('search-model');
    const sizeFilter = document.getElementById('size-filter');
    const showRecommendedCheckbox = document.getElementById('show-recommended');
    const refreshBtn = document.getElementById('refresh-btn');

    const modelDetailsModalEl = document.getElementById('model-details-modal');
    const modelDetailsModal = new bootstrap.Modal(modelDetailsModalEl);
    const modalModelName = document.getElementById('modal-model-name');
    const modalModelSize = document.getElementById('modal-model-size');
    const modalModelType = document.getElementById('modal-model-type');
    const modalModelCompatibility = document.getElementById('modal-model-compatibility');
    const modalMinRam = document.getElementById('modal-min-ram');
    const modalGpuReq = document.getElementById('modal-gpu-req');
    const modalDiskSpace = document.getElementById('modal-disk-space');
    const modalModelDescription = document.getElementById('modal-model-description');
    const downloadModelBtn = document.getElementById('download-model-btn');
    const downloadProgressContainer = document.getElementById('download-progress-container');
    const downloadProgressBar = document.getElementById('download-progress-bar');
    const downloadStatus = document.getElementById('download-status');

    let allModels = [];
    let currentModelDetails = null;
    let systemSpecs = null;

    initPage();

    function initPage() {
        bindEvents();
        // 先加载系统信息，然后再获取模型列表
        loadSystemInfo().then(() => {

            fetchModels();
        }).catch(error => {
            console.error('Failed to load system info, fetching models anyway:', error);
            fetchModels();
        });
    }

    // calculateCompatibilityScore and parseModelSizeToParams remain the same
    function calculateCompatibilityScore(model) {

        if (!systemSpecs) {

            return 50; // Default if no system specs
        }
        if (model.size === undefined || model.size === null) {

            return 50; // Default for models with undefined size
        }

        let gpuAvailable = false;
        let gpuMemory = 0;
        let ramAvailable = 8; // Default RAM

        if (systemSpecs && systemSpecs.gpu) {
            gpuAvailable = systemSpecs.gpu.available === true;
            if (systemSpecs.gpu.memory) {
                gpuMemory = parseFloat(systemSpecs.gpu.memory) || 0;
            }
        }

        if (systemSpecs && systemSpecs.memory && systemSpecs.memory.available) {
            ramAvailable = parseFloat(systemSpecs.memory.available) || 0;
        }



        const modelParams = parseModelSizeToParams(model); // Gets estimated B params

        if (isNaN(modelParams) || modelParams <= 0) {

            return 30; // Default for invalid param sizes
        }

        let score = 10; // Base score, can't go lower than this

        // Helper function for smooth scoring
        // value: current model param size
        // optimalLower, optimalUpper: ideal param range for 100% score
        // lowerBound, upperBound: min/max params considered, score outside this drops cottura 10% or very low
        // lowUtilPenaltyFactor: how much to penalize models smaller than optimalLower
        // highDemandPenaltyFactor: how much to penalize models larger than optimalUpper
        function getSmoothScore(value, optimalLower, optimalUpper, lowerBound, upperBound, lowUtilPenaltyFactor = 0.5, highDemandPenaltyFactor = 2.0) {
            let currentScore;
            if (value >= optimalLower && value <= optimalUpper) {
                currentScore = 100; // Max score for optimal range
            } else if (value < optimalLower) {
                if (value < lowerBound) return 10; // Below absolute lower bound
                // Penalize for being smaller than optimal
                // Score decreases from 100 as it moves away from optimalLower
                const diff = optimalLower - value;
                currentScore = 100 - (diff / (optimalLower - lowerBound + 1e-6)) * (100 - 60) * lowUtilPenaltyFactor; // Penalize down to ~60% for very small
                currentScore = Math.max(50, currentScore); // Small models are still usable
            } else { // value > optimalUpper
                if (value > upperBound) return 10; // Above absolute upper bound
                // Penalize for being larger than optimal
                // Score decreases from 100 as it moves away from optimalUpper
                const diff = value - optimalUpper;
                currentScore = 100 - (diff / (upperBound - optimalUpper + 1e-6)) * (100 - 10) * highDemandPenaltyFactor; // Penalize more sharply
            }
            return Math.max(10, Math.min(100, currentScore));
        }



        if (gpuAvailable && gpuMemory > 1.0) { // GPU Mode (min 1GB VRAM to be considered viable for GPU, aligned with backend)

            // 重新定义推荐度分层：适合80-100%，偏小50-70%，偏大10-50%
            let optimalLower, optimalUpper, smallLower, smallUpper, largeLower, largeUpper;

            if (gpuMemory >= 22) { // Backend: 22GB+ VRAM: 13B to 30B
                optimalLower = 13; optimalUpper = 30;  // 80-100%
                smallLower = 7; smallUpper = 12;       // 50-70%
                largeLower = 31; largeUpper = 70;      // 10-50%
            } else if (gpuMemory >= 14) { // Backend: 14GB-21GB VRAM: 7B to 22B
                optimalLower = 7; optimalUpper = 22;   // 80-100%
                smallLower = 3; smallUpper = 6;        // 50-70%
                largeLower = 23; largeUpper = 45;      // 10-50%
            } else if (gpuMemory >= 9) {  // Backend: 9GB-13GB VRAM: 7B to 9B (e.g., 12GB VRAM)
                optimalLower = 7; optimalUpper = 9;    // 100% 完美适配
                smallLower = 4; smallUpper = 6;        // 90-95% 优秀适配 (小)
                largeLower = 10; largeUpper = 12;      // 90-95% 优秀适配 (大)
            } else if (gpuMemory >= 7) {  // Backend: 7GB-8GB VRAM: 3B to 8B
                optimalLower = 3; optimalUpper = 8;    // 80-100%
                smallLower = 1; smallUpper = 2;        // 50-70%
                largeLower = 9; largeUpper = 20;       // 10-50%
            } else if (gpuMemory >= 5) {  // Backend: 5GB-6GB VRAM: 1.5B to 4B
                optimalLower = 1.5; optimalUpper = 4;  // 80-100%
                smallLower = 0.7; smallUpper = 1.4;    // 50-70%
                largeLower = 5; largeUpper = 15;       // 10-50%
            } else { // Backend: 2GB-4GB VRAM: 0.7B to 1.5B
                optimalLower = 0.7; optimalUpper = 1.5; // 80-100%
                smallLower = 0.5; smallUpper = 0.6;     // 50-70%
                largeLower = 2; largeUpper = 8;         // 10-50%
            }
            
            // 针对12GB显存优化的推荐度计算
            if (gpuMemory >= 9 && gpuMemory <= 13) {
                // 12GB显存专用算法
                if (modelParams >= 7 && modelParams <= 9) {
                    // 完美适配区间：7B-9B → 100%
                    score = 100;
                } else if ((modelParams >= 4 && modelParams < 7) || (modelParams > 9 && modelParams <= 12)) {
                    // 优秀适配区间：4B-6B, 10B-12B → 90-95%
                    if (modelParams >= 4 && modelParams < 7) {
                        score = Math.round(90 + (modelParams - 4) * 5 / 3); // 90% 到 95%
                    } else {
                        score = Math.round(95 - (modelParams - 9) * 5 / 3); // 95% 到 90%
                    }
                } else if (modelParams >= 13 && modelParams <= 15) {
                    // 良好适配区间：13B-15B → 75-85%
                    score = Math.round(85 - (modelParams - 13) * 10 / 2); // 85% 到 75%
                } else if (modelParams >= 16 && modelParams <= 22) {
                    // 勉强适配区间：16B-22B → 50-70%
                    score = Math.round(70 - (modelParams - 16) * 20 / 6); // 70% 到 50%
                } else if (modelParams >= 1 && modelParams < 4) {
                    // 小模型区间：1B-3B → 60-80%
                    score = Math.round(60 + (modelParams - 1) * 20 / 3); // 60% 到 80%
                } else if (modelParams < 1) {
                    // 超小模型：<1B → 40-60%
                    score = Math.round(40 + modelParams * 20); // 40% 到 60%
                } else {
                    // 不适配区间：23B+ → 10-40%
                    const excess = modelParams - 22;
                    score = Math.round(Math.max(10, 40 - excess * 2)); // 40% 递减到 10%
                }
            } else {
                // 其他显存配置使用原算法
                const optimalCenter = (optimalLower + optimalUpper) / 2;
                const optimalRange = optimalUpper - optimalLower;
                
                if (modelParams >= optimalLower && modelParams <= optimalUpper) {
                    const distanceFromCenter = Math.abs(modelParams - optimalCenter);
                    const normalizedDistance = distanceFromCenter / (optimalRange / 2);
                    score = Math.round(100 - normalizedDistance * 15);
                } else if (modelParams < optimalLower) {
                    const distance = optimalLower - modelParams;
                    if (distance <= optimalRange) {
                        const normalizedDistance = distance / optimalRange;
                        score = Math.round(85 - normalizedDistance * 25);
                    } else if (distance <= optimalRange * 2) {
                        const normalizedDistance = (distance - optimalRange) / optimalRange;
                        score = Math.round(60 - normalizedDistance * 30);
                    } else {
                        const normalizedDistance = Math.min((distance - optimalRange * 2) / (optimalRange * 2), 1);
                        score = Math.round(30 - normalizedDistance * 20);
                    }
                } else {
                    const distance = modelParams - optimalUpper;
                    const memoryFactor = Math.max(0.5, gpuMemory / 24);
                    
                    if (distance <= optimalRange * memoryFactor) {
                        const normalizedDistance = distance / (optimalRange * memoryFactor);
                        score = Math.round(85 - normalizedDistance * 35);
                    } else if (distance <= optimalRange * memoryFactor * 2) {
                        const normalizedDistance = (distance - optimalRange * memoryFactor) / (optimalRange * memoryFactor);
                        score = Math.round(50 - normalizedDistance * 25);
                    } else {
                        const normalizedDistance = Math.min((distance - optimalRange * memoryFactor * 2) / (optimalRange * memoryFactor * 2), 1);
                        score = Math.round(25 - normalizedDistance * 15);
                    }
                }
            }
            
            score = Math.max(10, Math.min(100, score)); // 确保在10-100范围内

        } else { // CPU Mode or very low VRAM (<2GB)

            // CPU模式也采用分层计算：适合80-100%，偏小50-70%，偏大10-50%
            let optimalLower, optimalUpper, smallLower, smallUpper, largeLower, largeUpper;
            
            if (ramAvailable >= 48) { // Backend: 7B to 13B (CPU)
                optimalLower = 7; optimalUpper = 13;   // 80-100%
                smallLower = 3; smallUpper = 6;        // 50-70%
                largeLower = 14; largeUpper = 30;      // 10-50%
            } else if (ramAvailable >= 28) { // Backend: 3B to 7B (CPU)
                optimalLower = 3; optimalUpper = 7;    // 80-100%
                smallLower = 1; smallUpper = 2;        // 50-70%
                largeLower = 8; largeUpper = 15;       // 10-50%
            } else if (ramAvailable >= 12) { // Backend: 1B to 3B (CPU)
                optimalLower = 1; optimalUpper = 3;    // 80-100%
                smallLower = 0.5; smallUpper = 0.9;    // 50-70%
                largeLower = 4; largeUpper = 7;        // 10-50%
            } else { // Backend: "运行当前AI模型可能较为吃力"
                optimalLower = 0.5; optimalUpper = 1;  // 80-100%
                smallLower = 0.3; smallUpper = 0.4;    // 50-70%
                largeLower = 1.5; largeUpper = 3;      // 10-50%
            }
            

            
            // CPU模式：基于距离的平滑渐变推荐度计算
            const optimalCenter = (optimalLower + optimalUpper) / 2;
            const optimalRange = optimalUpper - optimalLower;
            
            if (modelParams >= optimalLower && modelParams <= optimalUpper) {
                // 最优范围内：85-100%的平滑渐变
                const distanceFromCenter = Math.abs(modelParams - optimalCenter);
                const normalizedDistance = distanceFromCenter / (optimalRange / 2);
                score = Math.round(100 - normalizedDistance * 15); // 100% 到 85%
            } else if (modelParams < optimalLower) {
                // 小于最优范围：平滑衰减
                const distance = optimalLower - modelParams;
                if (distance <= optimalRange) {
                    // 接近最优范围：85% 到 65%
                    const normalizedDistance = distance / optimalRange;
                    score = Math.round(85 - normalizedDistance * 20);
                } else if (distance <= optimalRange * 2) {
                    // 中等距离：65% 到 35%
                    const normalizedDistance = (distance - optimalRange) / optimalRange;
                    score = Math.round(65 - normalizedDistance * 30);
                } else {
                    // 远距离：35% 到 15%
                    const normalizedDistance = Math.min((distance - optimalRange * 2) / (optimalRange * 2), 1);
                    score = Math.round(35 - normalizedDistance * 20);
                }
            } else {
                // 大于最优范围：根据内存限制平滑衰减
                const distance = modelParams - optimalUpper;
                const memoryFactor = Math.max(0.3, ramAvailable / 64); // 内存越大，对大模型越宽容
                
                if (distance <= optimalRange * memoryFactor) {
                    // 接近最优范围：85% 到 45%
                    const normalizedDistance = distance / (optimalRange * memoryFactor);
                    score = Math.round(85 - normalizedDistance * 40);
                } else if (distance <= optimalRange * memoryFactor * 2) {
                    // 中等距离：45% 到 20%
                    const normalizedDistance = (distance - optimalRange * memoryFactor) / (optimalRange * memoryFactor);
                    score = Math.round(45 - normalizedDistance * 25);
                } else {
                    // 远距离：20% 到 10%
                    const normalizedDistance = Math.min((distance - optimalRange * memoryFactor * 2) / (optimalRange * memoryFactor * 2), 1);
                    score = Math.round(20 - normalizedDistance * 10);
                }
            }
            
            score = Math.max(10, Math.min(100, score)); // 确保在10-100范围内
        }
        const finalScore = Math.max(10, Math.min(100, Math.round(score)));

        return finalScore; // Ensure score is 10-100
    }

    function parseModelSizeToParams(model) {
        const modelName = model.name ? model.name.toLowerCase() : '';
        const modelId = model.id ? model.id.toLowerCase() : '';
        const modelSize = parseFloat(model.size || 0);

        const paramRegex = /(\d+(\.\d+)?)b\b/i; // Matches 7b, 13b, 3.5b etc.
        const nameMatch = modelName.match(paramRegex);
        if (nameMatch && nameMatch[1]) return parseFloat(nameMatch[1]);

        const idMatch = modelId.match(paramRegex);
        if (idMatch && idMatch[1]) return parseFloat(idMatch[1]);

        // Guesstimation based on common GGUF sizes (approx. 0.6-0.8 GB per B params for Q4_K_M)
        if (modelSize <= 0.8) return 0.7; // ~700M params
        if (modelSize <= 1.8) return 1.5; // ~1.5B
        if (modelSize <= 2.8) return 3;   // ~3B
        if (modelSize <= 5.0) return 7;   // ~7B
        if (modelSize <= 8.0) return 10;  // ~10B
        if (modelSize <= 10.0) return 13; // ~13B
        if (modelSize <= 15.0) return 20; // ~20B
        if (modelSize <= 25.0) return 30; // ~30B
        if (modelSize <= 35.0) return 40; // ~40B
        if (modelSize <= 50.0) return 65; // ~65-70B
        if (modelSize > 50.0) return 70;  // Capped at 70B for guesstimation

        return Math.max(0.5, Math.round(modelSize / 0.7)); // Fallback, assumes ~0.7GB per B
    }

    function bindEvents() {
        refreshBtn.addEventListener('click', () => {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
            fetchModels().finally(() => {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="bi bi-arrow-repeat"></i>';
            });
        });
        searchInput.addEventListener('input', filterModels);
        sizeFilter.addEventListener('change', filterModels);
        showRecommendedCheckbox.addEventListener('change', filterModels);
        downloadModelBtn.addEventListener('click', startModelDownload);

        // Optional: Close modal on escape key even if not focused
        modelDetailsModalEl.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                modelDetailsModal.hide();
            }
        });
    }

    function loadSystemInfo() {
    
        return fetch('/wkg/api/models/system-info')
            .then(response => {
        
                return response.ok ? response.json() : Promise.reject(response.status);
            })
            .then(data => {
        
                if (data.code === 200 && data.data) {
                    systemSpecs = data.data;
            
                    updateSystemInfoUI(systemSpecs);
                    return systemSpecs; // 返回系统信息
                } else {
                    console.error('Invalid response format:', data);
                    throw new Error(data.message || data.msg || '获取系统信息格式不正确');
                }
            })
            .catch(error => {
                console.error('获取系统信息失败:', error);
                systemInfoContainer.innerHTML = `<div class="alert alert-warning p-2 text-center small">无法加载系统信息。</div>`;
                throw error; // 重新抛出错误，让调用者知道失败了
            });
    }

    function updateSystemInfoUI(specs) {
        const gpuMemory = (specs.gpu && specs.gpu.memory !== undefined) ? `${parseFloat(specs.gpu.memory).toFixed(1)}GB` : 'N/A';
        const cpuModel = specs.cpu.model ? specs.cpu.model.replace(/\(R\)/g, '®').replace(/\(TM\)/g, '™').split('@')[0].trim() : '未知CPU'; // Shorter CPU name

        systemInfoContainer.innerHTML = `
            <div class="system-info-item">
                <span class="system-info-key"><i class="bi bi-cpu"></i>处理器:</span>
                <span class="system-info-value">${cpuModel}</span>
            </div>
            <div class="system-info-item">
                <span class="system-info-key"><i class="bi bi-hdd-stack"></i>核心/线程:</span>
                <span class="system-info-value">${specs.cpu.cores || '-'}核 / ${specs.cpu.threads || '-'}线程</span>
            </div>
            <div class="system-info-item">
                <span class="system-info-key"><i class="bi bi-memory"></i>内存:</span>
                <span class="system-info-value">${specs.memory.total || '-'}GB (可用: ${specs.memory.available || '-'}GB)</span>
            </div>
            <div class="system-info-item">
                <span class="system-info-key"><i class="bi bi-gpu-card"></i>显卡:</span>
                <span class="system-info-value">${specs.gpu.model || '未检测到'}</span>
            </div>
            <div class="system-info-item">
                <span class="system-info-key"><i class="bi bi-sd-card"></i>显存:</span>
                <span class="system-info-value">${gpuMemory}</span>
            </div>
            <div class="system-info-item">
                <span class="system-info-key"><i class="bi bi-device-hdd"></i>可用存储:</span>
                <span class="system-info-value">${specs.storage.available || '-'}GB</span>
            </div>
            <div class="alert alert-info mt-3 small">
                <i class="bi bi-lightbulb me-1"></i>
                根据您的系统配置，我们推荐使用${specs.recommendedModelSize || '小型模型'}
            </div>
        `;
    }

    async function fetchModels() {
        loadingContainer.classList.remove('d-none');
        noModelsMessage.classList.add('d-none');
        modelGrid.innerHTML = '';

        try {
            const [availableRes, installedRes] = await Promise.all([
                fetch('/wkg/api/models/available'),
                fetch('/wkg/api/models/installed')
            ]);

            if (!availableRes.ok) throw new Error(`获取可用模型失败: ${availableRes.status}`);
            const availableData = await availableRes.json();
            if (availableData.code !== 200 || !Array.isArray(availableData.data)) {
                throw new Error('可用模型数据格式错误');
            }
            allModels = availableData.data;

            let installedIds = [];
            if (installedRes.ok) {
                const installedData = await installedRes.json();
                if (installedData.code === 200 && Array.isArray(installedData.data)) {
                    installedIds = installedData.data.map(model => model.id);
                }
            }

            allModels.forEach(model => {
                model.isLocal = installedIds.includes(model.id);
                model.requirements = model.requirements || { minRam: 'N/A', minVRam: 'N/A', diskSpace: 'N/A' };
                model.description = model.description || '暂无详细描述。'; // Default description
            });

            renderModels(allModels);

        } catch (error) {
            console.error('获取模型列表失败:', error);
            noModelsMessage.innerHTML = `<i class="bi bi-exclamation-triangle-fill me-1"></i>无法加载模型列表: ${error.message}`;
            noModelsMessage.classList.remove('d-none');
        } finally {
            loadingContainer.classList.add('d-none');
        }
    }

    function renderModels(models) {
        modelGrid.innerHTML = ''; // Clear previous models
        if (models.length === 0) {
            noModelsMessage.classList.remove('d-none');
            return;
        }
        noModelsMessage.classList.add('d-none');

        models.forEach(model => {
            model._compatibilityScore = calculateCompatibilityScore(model);
        });

        const sortedModels = [...models].sort((a, b) => {
            // Sorting logic: installed first, then by score, then by size
            if (a.isLocal && !b.isLocal) return -1;
            if (!a.isLocal && b.isLocal) return 1;
            const scoreDiff = b._compatibilityScore - a._compatibilityScore;
            if (scoreDiff !== 0) return scoreDiff;
            return (parseFloat(b.size) || 0) - (parseFloat(a.size) || 0);
        });

        sortedModels.forEach(model => {
            const cardEl = document.createElement('div');

            const compatibilityScore = model._compatibilityScore;
            const compatClass = getCompatibilityClassSimple(compatibilityScore);
            const modelSize = model.size ? `${parseFloat(model.size).toFixed(1)}GB` : '未知大小';

            let typeIcon = 'bi-box-seam';
            let typeText = model.type || '未知';
            if (model.type === 'llm') { typeIcon = 'bi-chat-left-text'; typeText = '语言模型'; }
            else if (model.type === 'embedding') { typeIcon = 'bi-arrows-fullscreen'; typeText = '嵌入模型'; }

            cardEl.innerHTML = `
                <div class="model-card ${model.isLocal ? 'is-installed-card' : ''}">
                    <div class="model-card-header">
                        <div class="model-name-container">
                            <h3 class="model-name">${model.name}</h3>
                        </div>
                        <span class="model-id">${model.id}</span>
                    </div>
                    <div class="model-card-body">
                        <div class="model-meta-info">
                            <span><i class="bi bi-hdd"></i>${modelSize}</span>
                            <span><i class="bi ${typeIcon}"></i>${typeText}</span>
                        </div>
                        <div class="model-compatibility-section">
                            <div class="compatibility-header">
                                <div class="compatibility-score text-compat-${compatClass}">${compatibilityScore}%</div>
                                <span class="compatibility-label">推荐度</span>
                            </div>
                            <div class="compatibility-bar-container">
                                <div class="compatibility-bar bg-compat-${compatClass}" style="width: ${compatibilityScore}%;"></div>
                            </div>
                        </div>
                        <p class="model-description">${model.description}</p>
                    </div>
                    <div class="model-card-footer">
                        ${model.isLocal ?
                `<button class="btn btn-sm btn-outline-danger delete-btn w-100"><i class="bi bi-trash3 me-1"></i>卸载模型</button>` :
                `<button class="btn btn-primary download-btn w-100"><i class="bi bi-download me-1"></i>获取模型</button>`}
                    </div>
                </div>
            `;

            // Event listeners remain the same
            const downloadBtn = cardEl.querySelector('.download-btn');
            if (downloadBtn) {
                downloadBtn.addEventListener('click', () => showModelDetails(model));
            }

            const deleteBtn = cardEl.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    confirmDeleteModel(model);
                });
            }
            // Card click to open details (if not installed and not clicking a button)
            // Users can still click the card to open details if it's not installed.
            // If it IS installed, they would typically click the "卸载模型" button.
            // You could choose to disable card click for installed models if desired.
            if (!model.isLocal) {
                cardEl.querySelector('.model-card').addEventListener('click', (event) => {
                    if (!event.target.closest('button')) {
                        showModelDetails(model);
                    }
                });
            }

            modelGrid.appendChild(cardEl);
        });
    }

    function confirmDeleteModel(model) {
        const modalId = 'delete-confirm-modal-' + Date.now();
        const confirmDialog = document.createElement('div');
        // Added 'modal-delete-confirm' for specific styling
        confirmDialog.className = 'modal fade modal-delete-confirm';
        confirmDialog.id = modalId;
        confirmDialog.setAttribute('tabindex', '-1');
        confirmDialog.setAttribute('aria-hidden', 'true');

        confirmDialog.innerHTML = `
            <div class="modal-dialog modal-dialog-centered modal-sm"> <!-- modal-sm for smaller confirm dialog -->
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">确认操作</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                    </div>
                    <div class="modal-body">
                        <p>确定要卸载模型 <strong>${model.name}</strong> 吗？</p>
                        <p class="text-danger"><i class="bi bi-exclamation-triangle-fill me-1"></i>此操作不可恢复。</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-sm btn-danger" id="confirm-delete-btn-${modalId}">确认卸载</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(confirmDialog);
        const modal = new bootstrap.Modal(confirmDialog);

        const deleteBtnElement = document.getElementById(`confirm-delete-btn-${modalId}`);

        function handleDeleteClick() {
            deleteBtnElement.disabled = true;
            deleteBtnElement.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
            deleteBtnElement.removeEventListener('click', handleDeleteClick);
            deleteModel(model.id, modal, confirmDialog, deleteBtnElement);
        }

        deleteBtnElement.addEventListener('click', handleDeleteClick);

        confirmDialog.addEventListener('shown.bs.modal', () => {
            deleteBtnElement.focus();
        });

        confirmDialog.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(confirmDialog);
        });

        modal.show();
    }

    function deleteModel(modelId, modalInstance, dialogEl, deleteBtnElement) {
        fetch('/wkg/api/models/delete', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ modelName: modelId })
        })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    modalInstance.hide();
                    fetchModels();
                } else {
                    throw new Error(data.msg || '卸载失败');
                }
            })
            .catch(error => {
                console.error('卸载模型时发生错误:', error);
                const modalBody = dialogEl.querySelector('.modal-body');
                let errorDiv = modalBody.querySelector('.alert-danger.mt-2'); // Specific selector
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.className = 'alert alert-danger mt-2'; // Added mt-2 for spacing
                    modalBody.appendChild(errorDiv); // Append error message
                }
                errorDiv.innerHTML = `<i class="bi bi-exclamation-triangle-fill me-1"></i>卸载失败: ${error.message}`;

                deleteBtnElement.disabled = false;
                deleteBtnElement.textContent = '重试卸载';
                // Re-attach listener for retry ONLY if the modal is still open (user hasn't dismissed it)
                if (bootstrap.Modal.getInstance(dialogEl)) {
                    deleteBtnElement.addEventListener('click', function handleRetry() {
                        deleteBtnElement.removeEventListener('click', handleRetry); // remove self
                        handleDeleteClick(); // Call the original handler logic
                    });
                }
            });
    }

    function showToast(title, message, type = 'info') {
        // 使用全局Toast组件显示消息
        const fullMessage = title ? `${title}: ${message}` : message;
        window.parent?.postMessage({
            type: 'SHOW_TOAST',
            payload: { message: fullMessage, type: type }
        }, '*');
    }

    function filterModels() {
        const searchText = searchInput.value.toLowerCase().trim();
        const sizeFilterValue = sizeFilter.value;
        const showRecommended = showRecommendedCheckbox.checked;

        const filtered = allModels.filter(model => {
            const nameMatch = model.name.toLowerCase().includes(searchText) || model.id.toLowerCase().includes(searchText);
            if (searchText && !nameMatch) return false;

            const modelSizeGB = parseFloat(model.size);
            if (!isNaN(modelSizeGB)) { // Only filter if modelSizeGB is a valid number
                if (sizeFilterValue === 'small' && modelSizeGB >= 5) return false;
                if (sizeFilterValue === 'medium' && (modelSizeGB < 5 || modelSizeGB > 10)) return false;
                if (sizeFilterValue === 'large' && modelSizeGB <= 10) return false;
            } else if (sizeFilterValue !== 'all') { // If size is unknown and filter is not 'all', hide it
                return false;
            }

            if (showRecommended) {
                const score = model._compatibilityScore !== undefined ? model._compatibilityScore : calculateCompatibilityScore(model);
                // Define "recommended" as score >= 70 (good or better)
                if (score < 70) return false;
            }
            return true;
        });
        renderModels(filtered);
    }

    function showModelDetails(model) {
        currentModelDetails = model;
        const compatibilityScore = model._compatibilityScore !== undefined ? model._compatibilityScore : calculateCompatibilityScore(model);
        const compatClass = getCompatibilityClassSimple(compatibilityScore);

        modalModelName.textContent = model.name;
        modalModelSize.textContent = model.size ? `${parseFloat(model.size).toFixed(1)}GB` : 'N/A';

        let typeText = model.type || '未知';
        if (model.type === 'llm') typeText = '语言模型';
        else if (model.type === 'embedding') typeText = '嵌入模型';
        modalModelType.textContent = typeText;

        modalModelCompatibility.innerHTML = `<span class="fw-bold text-compat-${compatClass}">${compatibilityScore}%</span>`;

        modalMinRam.textContent = model.requirements.minRam || 'N/A';
        modalGpuReq.textContent = model.requirements.minVRam || 'N/A';
        modalDiskSpace.textContent = model.requirements.diskSpace || 'N/A';
        modalModelDescription.textContent = model.description || '暂无详细描述。';

        // Reset progress bar and status text every time the modal is shown
        downloadProgressContainer.classList.add('d-none'); // Hide initially
        downloadProgressBar.style.width = '0%';
        downloadProgressBar.setAttribute('aria-valuenow', '0');
        // Reset to default progress bar classes, removing any success/danger/warning states
        downloadProgressBar.className = 'progress-bar';
        downloadStatus.textContent = '准备下载中...'; // Initial status text

        downloadModelBtn.disabled = model.isLocal;
        if (model.isLocal) {
            downloadModelBtn.textContent = '已安装';
        } else {
            downloadModelBtn.textContent = '下载模型';
        }

        modelDetailsModal.show();
    }

    function startModelDownload() {
        if (!currentModelDetails || currentModelDetails.isLocal) return;

        // Show progress container and set initial downloading states immediately when download starts
        downloadProgressContainer.classList.remove('d-none');
        downloadModelBtn.disabled = true;
        downloadModelBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> 请求中...';
        downloadStatus.textContent = '正在准备下载...'; // Status before SSE connection
        // Ensure progress bar is animated and striped from the start of the request
        downloadProgressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
        downloadProgressBar.style.width = '0%'; // Explicitly set to 0% width at start
        downloadProgressBar.setAttribute('aria-valuenow', '0');

        const requestData = {
            modelName: currentModelDetails.id,
            modelType: currentModelDetails.type || 'llm',
            useGpu: systemSpecs && systemSpecs.gpu && systemSpecs.gpu.available === true
        };

        fetch('/wkg/api/models/download', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestData)
        })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200 && data.data && data.data.taskId) {
                    monitorDownloadProgress(data.data.taskId);
                } else {
                    throw new Error(data.msg || '启动下载失败，未获取任务ID');
                }
            })
            .catch(error => {
                console.error('下载请求失败:', error);
                downloadStatus.textContent = `启动下载失败: ${error.message}`;
                downloadProgressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                downloadProgressBar.classList.add('bg-danger');
                downloadModelBtn.disabled = false;
                downloadModelBtn.textContent = '重试下载';
            });
    }

    function monitorDownloadProgress(taskId) {
        downloadModelBtn.textContent = '下载中...';
        let eventSource = null;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        let isDownloadCompleted = false;
        let progressStallTimer = null;
        const progressStallTimeout = 5 * 60 * 1000;
        const safetyTimeoutDuration = 3 * 60 * 60 * 1000;

        function createEventSource() {
            if (progressStallTimer) clearTimeout(progressStallTimer);
            eventSource = new EventSource(`/wkg/api/models/download/stream/${taskId}`);

            eventSource.onopen = () => {
                downloadStatus.textContent = '已连接，等待下载数据...';
                reconnectAttempts = 0;
            };

            eventSource.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    if (data.progress !== undefined) {
                        const progress = Math.min(100, Math.max(0, parseInt(data.progress, 10)));
                        downloadProgressBar.style.width = `${progress}%`;
                        downloadProgressBar.setAttribute('aria-valuenow', progress);

                        let statusText = data.status || (progress < 100 ? '下载中' : '处理中');
                        if (statusText.toLowerCase().includes('pulling manifest')) statusText = '获取清单';
                        else if (statusText.toLowerCase() === 'downloading') statusText = '下载中';
                        else if (statusText.toLowerCase() === 'verifying') statusText = '校验中';
                        else if (statusText.toLowerCase() === 'success' || statusText.toLowerCase() === 'completed') statusText = '完成';

                        downloadStatus.textContent = `${statusText}: ${progress}%`;

                        if (progressStallTimer) clearTimeout(progressStallTimer);
                        if (progress < 100 && !isDownloadCompleted) {
                            progressStallTimer = setTimeout(createEventSource, progressStallTimeout);
                        }
                    }

                    const statusLower = (data.status || '').toLowerCase();
                    if (statusLower === 'success' || statusLower === 'completed' || statusLower === 'done' || (data.progress && parseInt(data.progress, 10) >= 100 && statusLower !== 'failed')) {
                        isDownloadCompleted = true; // Set flag first
                        closeConnection(); // Close SSE connection as download part is done

                        // UI updates for download completion, before installation confirmation
                        downloadProgressBar.style.width = '100%';
                        downloadProgressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                        downloadProgressBar.classList.add('bg-success');
                        downloadStatus.textContent = '下载完成，正在安装和准备模型，请稍候...'; // New status
                        downloadModelBtn.disabled = true; // Keep button disabled
                        downloadModelBtn.textContent = '处理中...'; // New button text

                        // Wait for a few seconds to allow backend to install/register the model,
                        // then fetch the updated models list from the backend.
                        const installWaitDelay = 8000; // 8 seconds, adjust as needed

                        setTimeout(async () => {
                            try {
                                // Fetch updated models from backend, this will trigger ModelService.init() implicitly
                                await fetchModels(); 
                                // fetchModels() will call renderModels internally upon success,
                                // and also updates allModels array with fresh data including local status.
                                // So, no need to manually set isLocal or call renderModels(allModels) here.

                            } catch (error) {
                                console.error("获取模型列表失败 (安装后):", error);
                                showToast('列表更新失败', `无法确认 ${currentModelDetails.name} 的安装状态。请稍后手动刷新。`, 'warning');
                                // Fallback: hide modal anyway, user can manually check
                            }
                            // Hide modal regardless of fetchModels outcome after timeout
                            if (bootstrap.Modal.getInstance(modelDetailsModalEl)) {
                                modelDetailsModal.hide();
                            }
                        }, installWaitDelay);

                    } else if (statusLower === 'failed' || statusLower === 'error') {
                        isDownloadCompleted = true; // Set flag first
                        closeConnection();
                        downloadStatus.textContent = `下载失败: ${data.error || data.status || '未知错误'}`;
                        downloadProgressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                        downloadProgressBar.classList.add('bg-danger');
                        downloadModelBtn.disabled = false;
                        downloadModelBtn.textContent = '重试下载';
                    }
                } catch (error) {
                    console.error('解析SSE事件数据出错:', error, event.data);
                }
            };

            eventSource.onerror = (errorEvent) => {
                console.error('EventSource stream error:', errorEvent); // Log the actual error event

                if (isDownloadCompleted) {
                    closeConnection();
                    return;
                }

                if (eventSource) { // Ensure it exists before closing
                    eventSource.close();
                }

                if (reconnectAttempts < maxReconnectAttempts) {
                    reconnectAttempts++;
                    downloadStatus.textContent = `连接中断，尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})...`;
                    setTimeout(createEventSource, reconnectAttempts * 2000 + 1000);
                } else {
                    // Max retries reached
                    const currentProgress = parseInt(downloadProgressBar.getAttribute('aria-valuenow'), 10);
                    if (currentProgress === 100) {
                        // Optimistic completion if progress was 100%
                        downloadStatus.textContent = '下载已完成，最终状态确认超时。';
                        downloadProgressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                        downloadProgressBar.classList.add('bg-success');
                        downloadModelBtn.textContent = '已完成';
                        downloadModelBtn.disabled = true;

                        showToast('状态确认超时', `${currentModelDetails.name} 下载可能已完成，正在刷新列表...`, 'info');

                        // Optimistically mark as downloaded for immediate UI feedback
                        const optimisticModel = allModels.find(m => m.id === currentModelDetails.id);
                        if (optimisticModel) {
                            optimisticModel.isLocal = true;
                        }

                        // Attempt to hide modal and render optimistically updated list first
                        if (bootstrap.Modal.getInstance(modelDetailsModalEl)) {
                           modelDetailsModal.hide();
                        }
                        renderModels(allModels); // Render with optimistic update

                        // Then, try to fetch the authoritative list from the backend to confirm
                        fetchModels().catch(err => {
                            console.error("Failed to refresh models from backend after optimistic completion, UI might not be perfectly up-to-date:", err);
                            // UI was already updated optimistically, so just log the error.
                        });

                    } else {
                        // Standard failure if progress was not 100%
                        downloadStatus.textContent = '无法连接服务器。下载可能已中断或在后台进行。';
                        downloadProgressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                        downloadProgressBar.classList.add('bg-warning');
                        downloadModelBtn.disabled = false;
                        downloadModelBtn.textContent = '检查状态';
                        showToast('连接中断', `与 ${currentModelDetails.name} 的下载监控连接中断。`, 'warning');
                    }
                }
            };
        }

        function closeConnection() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            if (progressStallTimer) clearTimeout(progressStallTimer);
            if (safetyTimeout) clearTimeout(safetyTimeout);
        }

        createEventSource();
        const safetyTimeout = setTimeout(() => {
            if (!isDownloadCompleted && eventSource && eventSource.readyState !== EventSource.CLOSED) {
                closeConnection();
                downloadStatus.textContent = `下载超时。上次进度 ${downloadProgressBar.getAttribute('aria-valuenow')}%.`;
                downloadProgressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                downloadProgressBar.classList.add('bg-warning');
                downloadModelBtn.disabled = false;
                downloadModelBtn.textContent = '检查状态';
                showToast('下载超时', '长时间未获取到进度，请检查。', 'warning');
            }
        }, safetyTimeoutDuration);
    }
});