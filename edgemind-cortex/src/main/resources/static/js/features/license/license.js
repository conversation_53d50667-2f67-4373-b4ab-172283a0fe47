/**
 * license.js - 系统授权页面功能
 */
document.addEventListener('DOMContentLoaded', function() {


    // 初始化授权页面
    initLicensePage();

    // 注册事件监听器
    registerEventListeners();

    // 检查URL参数中是否有错误信息
    checkErrorParams();
});

// API 基础路径
const API_BASE_URL = '/wkg/api';

/**
 * 初始化授权页面
 */
function initLicensePage() {
    // 获取硬件指纹
    fetchHardwareFingerprint();

    // 获取授权状态
    fetchLicenseStatus();
}

/**
 * 注册事件监听器
 */
function registerEventListeners() {
    // 复制硬件指纹按钮
    const copyFingerprintBtn = document.getElementById('copyFingerprintBtn');
    if (copyFingerprintBtn) {
        copyFingerprintBtn.addEventListener('click', function() {
            const hardwareFingerprint = document.getElementById('hardwareFingerprint').textContent;
            copyToClipboard(hardwareFingerprint);
            showToast('硬件指纹已复制到剪贴板', 'success');
        });
    }

    // 复制系统标识按钮
    const copyIdentifierBtn = document.getElementById('copyIdentifierBtn');
    if (copyIdentifierBtn) {
        copyIdentifierBtn.addEventListener('click', function() {
            const systemIdentifier = document.getElementById('systemIdentifier').textContent;
            copyToClipboard(systemIdentifier);
            showToast('系统标识已复制到剪贴板', 'success');
        });
    }

    // 激活按钮
    const activateBtn = document.getElementById('activateBtn');
    if (activateBtn) {
        activateBtn.addEventListener('click', function() {
            activateLicense();
        });
    }

    // 许可证密钥输入框回车事件
    const licenseKeyInput = document.getElementById('licenseKey');
    if (licenseKeyInput) {
        licenseKeyInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                activateLicense();
            }
        });
    }
}

/**
 * 获取硬件指纹
 */
function fetchHardwareFingerprint() {
    fetch(`${API_BASE_URL}/license/fingerprint`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.code === 200 && data.data) {

                // 显示硬件指纹
                const fingerprintElement = document.getElementById('hardwareFingerprint');
                if (fingerprintElement) {
                    fingerprintElement.textContent = data.data;
                }
            } else {
                console.error('获取硬件指纹失败:', data.message);
                showToast('获取硬件指纹失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取硬件指纹失败:', error);
            showToast('获取硬件指纹失败，请稍后再试', 'error');
        });
}

/**
 * 获取授权状态
 */
function fetchLicenseStatus() {
    fetch(`${API_BASE_URL}/license/status`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.code === 200 && data.data) {

                // 显示授权状态
                displayLicenseStatus(data.data);
            } else {
                console.error('获取授权状态失败:', data.message);
                showToast('获取授权状态失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取授权状态失败:', error);
            showToast('获取授权状态失败，请稍后再试', 'error');
        });
}

/**
 * 显示授权状态
 * @param {Object} status - 授权状态数据
 */
function displayLicenseStatus(status) {
    // 显示系统标识
    const systemIdentifierElement = document.getElementById('systemIdentifier');
    if (systemIdentifierElement && status.systemIdentifier) {
        systemIdentifierElement.textContent = status.systemIdentifier;
    }

    // 显示授权状态
    const statusElement = document.getElementById('licenseStatus');
    if (statusElement) {
        if (status.status === 1) {
            if (status.isExpired) {
                statusElement.textContent = '已过期';
                statusElement.className = 'text-warning fw-medium';
            } else {
                statusElement.textContent = '已授权';
                statusElement.className = 'text-success fw-medium';
            }
        } else {
            statusElement.textContent = '未授权';
            statusElement.className = 'text-danger fw-medium';
        }
    }

    // 显示授权类型
    const typeElement = document.getElementById('licenseType');
    if (typeElement) {
        let typeName = '试用版';
        switch (status.licenseType) {
            case 'standard':
                typeName = '标准版';
                break;
            case 'professional':
                typeName = '专业版';
                break;
            case 'enterprise':
                typeName = '企业版';
                break;
        }
        typeElement.textContent = typeName;
    }

    // 显示激活时间
    const activatedTimeElement = document.getElementById('activatedTime');
    if (activatedTimeElement) {
        if (status.activatedTime) {
            const date = new Date(status.activatedTime);
            activatedTimeElement.textContent = date.toLocaleString();
        } else {
            activatedTimeElement.textContent = '未激活';
        }
    }

    // 显示过期时间
    const expireTimeElement = document.getElementById('expireTime');
    if (expireTimeElement) {
        if (status.expireTime) {
            const date = new Date(status.expireTime);
            expireTimeElement.textContent = date.toLocaleString();

            // 如果有剩余天数，显示剩余天数
            if (status.remainingDays !== null && status.remainingDays >= 0) {
                expireTimeElement.textContent += ` (剩余 ${status.remainingDays} 天)`;
            }
        } else if (status.status === 1) {
            expireTimeElement.textContent = '永不过期';
        } else {
            expireTimeElement.textContent = '未激活';
        }
    }

    // 保持激活按钮始终可用，允许重新激活
    const activateBtn = document.getElementById('activateBtn');
    if (activateBtn) {
        activateBtn.disabled = false;
        if (status.status === 1 && !status.isExpired) {
            activateBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i> 重新激活';
        } else {
            activateBtn.innerHTML = '<i class="bi bi-key me-1"></i> 激活系统';
        }
    }
}

/**
 * 激活许可证
 */
function activateLicense() {
    const licenseKey = document.getElementById('licenseKey').value.trim();

    if (!licenseKey) {
        showToast('请输入授权密钥', 'warning');
        return;
    }

    // 隐藏之前的提示信息
    hideActivationError();
    hideActivationSuccess();
    
    // 禁用激活按钮
    const activateBtn = document.getElementById('activateBtn');
    if (activateBtn) {
        activateBtn.disabled = true;
        activateBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> 激活中...';
    }

    // 发送激活请求
    const formData = new URLSearchParams();
    formData.append('licenseKey', licenseKey);

    fetch(`${API_BASE_URL}/license/activate`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            // 首先尝试解析JSON格式的错误信息
            return response.json().then(errorData => {
                const errorMessage = errorData.message || '激活失败，请稍后再试';
                throw new Error(errorMessage);
            }).catch(() => {
                // 如果解析失败，则抛出通用错误
                throw new Error(`激活失败，服务器返回状态: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.code === 200) {
            // 隐藏错误提示，显示成功提示
            hideActivationError();
            showActivationSuccess();
            showToast('许可证激活成功！', 'success');
            // 清空输入框
            document.getElementById('licenseKey').value = '';
            // 重新获取授权状态以更新页面显示
            fetchLicenseStatus();

        } else {
            // 简化错误提示，避免暴露安全细节
            throw new Error('激活失败，请检查许可证密钥是否正确');
        }
    })
    .catch(error => {
        console.error('激活许可证失败:', error);
        
        // 简化错误信息
        let displayMessage = '激活失败，请检查许可证密钥是否正确';
        
        // 如果是网络错误，提供简单的网络提示
        if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
            displayMessage = '网络连接异常，请稍后重试';
        } else if (error.message.includes('服务器返回状态')) {
            displayMessage = '服务器暂时不可用，请稍后重试';
        }
        
        // 隐藏成功提示，显示错误提示
        hideActivationSuccess();
        showActivationError(displayMessage);
        showToast('激活失败', 'error');
        
        // 激活失败时恢复按钮状态
        const activateBtn = document.getElementById('activateBtn');
        if (activateBtn) {
            activateBtn.disabled = false;
            activateBtn.innerHTML = '<i class="bi bi-key me-1"></i> 激活系统';
        }
    });
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 */
function copyToClipboard(text) {
    // 创建一个临时的文本区域
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed'; // 防止滚动到视图中
    document.body.appendChild(textarea);
    textarea.select();

    try {
        // 执行复制命令
        document.execCommand('copy');
    } catch (err) {
        console.error('复制失败:', err);
    }

    // 删除临时文本区域
    document.body.removeChild(textarea);
}

/**
 * 显示消息提示
 * @param {string} message - 提示消息
 * @param {string} type - 消息类型：'success', 'error', 'warning', 'info'
 */
function showToast(message, type = 'info') {
    // 使用全局Toast组件显示消息
    window.parent?.postMessage({
        type: 'SHOW_TOAST',
        payload: { message: message, type: type }
    }, '*');
}

// 测试弹窗效果的函数
window.testToast = function(type) {
    const messages = {
        info: '这是一条信息提示，用于向用户传达一般性信息。',
        success: '操作成功完成！数据已保存并生效。',
        warning: '请注意：此操作可能会影响系统设置，建议谨慎操作。',
        error: '操作失败：网络连接异常，请检查网络设置后重试。'
    };
    
    const message = messages[type] || messages.info;
    showToast(message, type);
};

/**
 * 检查URL参数中的错误信息
 */
function checkErrorParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const error = urlParams.get('error');
    
    if (error) {
        let errorMessage = '激活失败，请检查许可证密钥是否正确';
        
        // 简化错误提示，避免暴露安全细节
        if (error === 'network_error') {
            errorMessage = '网络连接异常，请稍后重试';
        } else if (error === 'server_error' || error === 'service_unavailable') {
            errorMessage = '服务器暂时不可用，请稍后重试';
        }
        
        showActivationError(errorMessage);
        
        // 清除URL参数
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
    }
}

/**
 * 显示激活错误提示
 * @param {string} message - 错误消息
 */
function showActivationError(message) {
    const errorAlert = document.getElementById('activationErrorAlert');
    const errorMessage = document.getElementById('activationErrorMessage');
    
    if (errorAlert && errorMessage) {
        errorMessage.textContent = message;
        errorAlert.classList.remove('d-none');
        
        // 滚动到错误提示位置
        errorAlert.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

/**
 * 隐藏激活错误提示
 */
function hideActivationError() {
    const errorAlert = document.getElementById('activationErrorAlert');
    if (errorAlert) {
        errorAlert.classList.add('d-none');
    }
}

/**
 * 显示激活成功提示
 */
function showActivationSuccess() {
    const successAlert = document.getElementById('activationSuccessAlert');
    if (successAlert) {
        successAlert.classList.remove('d-none');
        
        // 滚动到成功提示位置
        successAlert.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // 3秒后自动隐藏成功提示
        setTimeout(() => {
            hideActivationSuccess();
        }, 3000);
    }
}

/**
 * 隐藏激活成功提示
 */
function hideActivationSuccess() {
    const successAlert = document.getElementById('activationSuccessAlert');
    if (successAlert) {
        successAlert.classList.add('d-none');
    }
}
