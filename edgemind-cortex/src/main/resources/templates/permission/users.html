<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理</title>
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/features/permission/permission.css">
</head>
<body>
    <div class="container-fluid p-4">
        <h2 class="mb-4 text-primary">用户管理</h2>
        
        <!-- 搜索与操作工具栏 -->
        <div class="row toolbar-row">
            <div class="col-md-8">
                <div class="d-flex gap-2">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="bi bi-plus-lg"></i> 新增用户
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="搜索用户名/姓名/部门">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 用户列表表格 -->
        <div class="card">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="">
                            <tr>
                                <th width="50" class="ps-3"><input type="checkbox" class="form-check-input"></th>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>部门</th>
                                <th>角色</th>
                                <th>邮箱</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th width="180">操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody">
                            <tr>
                                <td colspan="9" class="text-center p-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- 分页组件 -->
        <div class="d-flex justify-content-between align-items-center mt-3" id="paginationContainer">
            <div class="text-muted small">
                Loading pagination...
            </div>
        </div>
    </div>
    
    <!-- 新增用户模态框 -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addUserModalLabel">新增用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="addUsername" class="form-label">用户名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="addUsername" name="username" required>
                            </div>
                            <div class="col-md-6">
                                <label for="addNickname" class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="addNickname" name="nickname" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="addPassword" class="form-label">密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="addPassword" name="password" required>
                            </div>
                            <div class="col-md-6">
                                <label for="addConfirmPassword" class="form-label">确认密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="addConfirmPassword" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="addDeptId" class="form-label">部门 <span class="text-danger">*</span></label>
                                <select class="form-select" id="addDeptId" name="deptId" required>
                                    <option value="">请选择部门</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="addRoleIds" class="form-label">角色 <span class="text-danger">*</span></label>
                                <select class="form-select" id="addRoleIds" name="roleIds" multiple size="3" required>
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="addEmail" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="addEmail" name="email">
                            </div>
                            <div class="col-md-6">
                                <label for="addPhone" class="form-label">手机号</label>
                                <input type="tel" class="form-control" id="addPhone" name="phone">
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">状态</label>
                                <div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="status" id="addStatusEnabled" value="1" checked>
                                        <label class="form-check-label" for="addStatusEnabled">启用</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="status" id="addStatusDisabled" value="0">
                                        <label class="form-check-label" for="addStatusDisabled">禁用</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="addRemark" class="form-label">备注</label>
                            <textarea class="form-control" id="addRemark" name="remark" rows="2"></textarea>
                        </div>
                        <input type="hidden" id="editUserId" name="id">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="addUserForm" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 编辑用户模态框 (结构与新增类似) -->
    <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editUserModalLabel">编辑用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" id="editUserIdField" name="id">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="editUsername" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="editUsername" name="username" readonly disabled>
                            </div>
                            <div class="col-md-6">
                                <label for="editNickname" class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="editNickname" name="nickname" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="editDeptId" class="form-label">部门 <span class="text-danger">*</span></label>
                                <select class="form-select" id="editDeptId" name="deptId" required>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">状态</label>
                                <div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="status" id="editStatusEnabled" value="1">
                                        <label class="form-check-label" for="editStatusEnabled">启用</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="status" id="editStatusDisabled" value="0">
                                        <label class="form-check-label" for="editStatusDisabled">禁用</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="editEmail" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="editEmail" name="email">
                            </div>
                            <div class="col-md-6">
                                <label for="editPhone" class="form-label">手机号</label>
                                <input type="tel" class="form-control" id="editPhone" name="phone">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="editRemark" class="form-label">备注</label>
                            <textarea class="form-control" id="editRemark" name="remark" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="editUserForm" class="btn btn-primary">保存更改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Assign Roles Modal -->
    <div class="modal fade" id="assignRoleModal" tabindex="-1" aria-labelledby="assignRoleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignRoleModalLabel">分配角色 - <span id="assignRoleUserName" class="text-primary fw-bold"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="assignRoleForm">
                        <input type="hidden" id="assignRoleUserId" name="userId">
                        <div id="assignRoleCheckboxContainer">
                            <div class="text-center text-muted p-3">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading roles...</span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="assignRoleForm" class="btn btn-primary">保存分配</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reset Password Modal -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="resetPasswordModalLabel">重置密码 - <span id="resetPasswordUserName" class="text-primary fw-bold"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="resetPasswordForm">
                        <input type="hidden" id="resetPasswordUserId" name="userId">
                        <div class="mb-3">
                            <label for="resetNewPassword" class="form-label">新密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="resetNewPassword" name="newPassword" required minlength="6">
                             <div class="form-text">密码至少包含 6 个字符。</div>
                        </div>
                        <div class="mb-3">
                            <label for="resetConfirmPassword" class="form-label">确认新密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="resetConfirmPassword" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="resetPasswordForm" class="btn btn-primary">确认重置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container (should be placed in layout.html ideally) -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toastPlacement">
        <!-- Toasts will appear here -->
    </div>
    
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script type="module" src="/wkg/js/features/permission/users.js"></script>
</body>
</html> 