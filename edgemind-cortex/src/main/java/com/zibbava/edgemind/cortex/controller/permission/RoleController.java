package com.zibbava.edgemind.cortex.controller.permission;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.cortex.entity.Role;
import com.zibbava.edgemind.cortex.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/permission/roles") // 统一前缀
public class RoleController {

    @Autowired
    private RoleService roleService;

    /**
     * 分页查询角色列表
     */
    @GetMapping
    @SaCheckPermission("role:manage:list")
    public ResponseEntity<IPage<Role>> getRolePage(@RequestParam(defaultValue = "1") int current,
                                                   @RequestParam(defaultValue = "10") int size,
                                                   @RequestParam(required = false) String roleName,
                                                   @RequestParam(required = false) String roleCode) {
        Page<Role> page = new Page<>(current, size);
        // 使用 Mybatis Plus 的分页查询 (ServiceImpl 已提供)
        // TODO: 可以添加基于 roleName, roleCode 的条件查询
        IPage<Role> rolePage = roleService.page(page);
        return ResponseEntity.ok(rolePage);
    }

     /**
     * 获取所有角色列表 (用于下拉选择等)
     */
    @GetMapping("/get/all")
    @SaCheckPermission("role:manage:list") // 或者更宽松的权限，如用户管理分配角色时也需要
    public ResponseEntity<List<Role>> getAllRoles() {
        List<Role> list = roleService.list();
        return ResponseEntity.ok(list);
    }

    /**
     * 新增角色
     */
    @PostMapping
    @SaCheckPermission("role:manage:create")
    public ResponseEntity<Void> addRole(@RequestBody Role role) {
        // TODO: 校验 roleCode 是否唯一
        roleService.save(role);
        return ResponseEntity.ok().build();
    }

    /**
     * 修改角色
     */
    @PutMapping("/{id}")
    @SaCheckPermission("role:manage:update")
    public ResponseEntity<Void> updateRole(@PathVariable Long id, @RequestBody Role role) {
        role.setId(id);
        // TODO: 校验 roleCode 是否唯一 (如果允许修改)
        roleService.updateById(role);
        return ResponseEntity.ok().build();
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission("role:manage:delete")
    public ResponseEntity<Void> deleteRole(@PathVariable Long id) {
        // TODO: 检查角色是否被用户关联，是否允许删除
        roleService.removeById(id);
        // 同时需要删除 sys_role_permission 和 sys_user_role 中的关联数据
        return ResponseEntity.ok().build();
    }

    /**
     * 获取角色拥有的权限ID列表
     */
    @GetMapping("/{roleId}/permissions")
    @SaCheckPermission("role:manage:assign_permission")
    public ResponseEntity<List<Long>> getRolePermissionIds(@PathVariable Long roleId) {
        List<Long> permissionIds = roleService.getPermissionIdsByRoleId(roleId);
        return ResponseEntity.ok(permissionIds);
    }

    /**
     * 给角色分配权限
     */
    @PutMapping("/{roleId}/permissions")
    @SaCheckPermission("role:manage:assign_permission")
    public ResponseEntity<Void> assignPermissionsToRole(@PathVariable Long roleId, @RequestBody List<Long> permissionIds) {
        roleService.assignPermissionsToRole(roleId, permissionIds);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取单个角色详情 (如果需要)
     */
    @GetMapping("/{id}")
    @SaCheckPermission("role:manage:list")
    public ResponseEntity<Role> getRoleById(@PathVariable Long id) {
        Role role = roleService.getById(id);
        if (role != null) {
            // TODO: 可能需要同时加载权限信息填充到 role.permissions
            return ResponseEntity.ok(role);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
} 