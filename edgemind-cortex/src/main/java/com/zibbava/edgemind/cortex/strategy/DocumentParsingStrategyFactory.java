package com.zibbava.edgemind.cortex.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 文档解析策略工厂
 * 根据文件类型选择合适的解析策略
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DocumentParsingStrategyFactory {
    
    private final List<DocumentParsingStrategy> strategies;
    
    /**
     * 根据文件名获取合适的解析策略
     * @param fileName 文件名
     * @return 解析策略
     * @throws IllegalArgumentException 如果没有找到合适的策略
     */
    public DocumentParsingStrategy getStrategy(String fileName) {
        log.debug("为文件 {} 选择解析策略", fileName);
        
        for (DocumentParsingStrategy strategy : strategies) {
            if (strategy.supports(fileName)) {
                log.info("为文件 {} 选择策略: {}", fileName, strategy.getClass().getSimpleName());
                return strategy;
            }
        }
        
        throw new IllegalArgumentException("未找到支持文件类型的解析策略: " + fileName);
    }
}