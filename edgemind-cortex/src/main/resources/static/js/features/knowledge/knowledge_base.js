// ===========================================
// 全局变量和状态
// ===========================================
const API_BASE_URL = '/wkg/api'; // 后端 API 基础路径

// 不再使用ES模块导入，改为从window对象获取函数
// import { initializeKnowledgeChat, updateKnowledgeChatNode } from './knowledge-chat.js';

let currentSpaceId = null; // 当前选中的知识空间 ID (需要初始化)
let currentNodeId = null; // 当前选中的节点 ID
let currentNodeName = null; // 当前选中的节点名称
let currentNodeType = null; // 当前选中的节点类型 ('FOLDER' or 'FILE')
let currentTreeNodeElement = null; // 当前选中的树节点 DOM 元素
let currentChatContextNodeId = null; // 当前聊天上下文关联的节点 ID
let currentSseReader = null; // 当前活动的 SSE 读取器 (用于 Fetch API)
let knowledgeType = null; // 当前知识库类型 ('team' or 'private')
let shouldInitSpace = false; // 是否需要初始化知识空间
let onlyofficeEditor = null; // ONLYOFFICE 编辑器实例

// DOM 元素引用 (在 DOMContentLoaded 后获取)
let docTreeContainer = null;
let chatMessagesContainer = null;
let aiChatInput = null;
let aiChatSendButton = null;
let mainViewTitle = null;
let documentView = null;
let documentEdit = null;
let documentEditTextArea = null;
let documentAreaWrapper = null; // 添加这个引用
let docActionButtons = null; // 文件操作按钮容器
let editButton = null;
let saveButton = null;
let cancelButton = null;
let knowledgeScopeTitle = null; // 知识空间标题元素
let createNodeModal = null;
let createNodeForm = null;
let createNodeParentIdInput = null;
let createNodeSpaceIdInput = null;
let createNodeNameInput = null;
let createNodeTypeSelect = null;
let createNodeParentInfo = null; // 添加父节点信息容器引用
let createNodeParentNameLabel = null;
let renameNodeModal = null;
let renameNodeForm = null;
let renameNodeIdInput = null;
let renameNodeNameInput = null;
let pageContentArea = null;
let aiChatSidebarCenter = null;
let closeChatSidebarCenterButton = null;
let chatContextTitleElement = null;
let fileUploadSection = null;
let fileUploadInput = null;
let uploadButton = null;
let fileUploadModal = null;
let fileUploadForm = null;
let uploadNodeIdInput = null;
let uploadFileInput = null;
let uploadTargetName = null;
let updateNodeNameCheckbox = null;
let onlyofficeViewer = null; // ONLYOFFICE 查看器容器
let deleteNodeModal = null;
let deleteConfirmMessage = null;
let deleteWarningSection = null;
let deleteNodeIdInput = null;
let deleteNodeNameInput = null;
let deleteNodeTypeInput = null;
let confirmDeleteButton = null;
let documentVectorStatusBadge = null; // 新增：文档向量状态徽章
let reparseButton = null; // 新增：重新解析按钮
let currentPollingIntervalId = null; // 新增：当前状态轮询的 Interval ID
let lastPolledNodeId = null; // 新增：最后轮询的节点ID
let reparseConfirmModal = null; // 新增：重新解析确认模态框
let reparseConfirmNodeNameElement = null; // 新增：模态框中显示节点名称的元素
let confirmReparseButton = null; // 新增：重新解析确认按钮

// ===========================================
// API 请求助手函数
// ===========================================
async function fetchApi(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            // 'Authorization': 'Bearer ' + getToken(), // 如果使用 Token 认证
            // 可以在这里添加 Sa-Token header (如果需要前端传递)
        }
    };
    const mergedOptions = { ...defaultOptions, ...options };
    if (mergedOptions.body && typeof mergedOptions.body === 'object') {
        mergedOptions.body = JSON.stringify(mergedOptions.body);
    }

    try {
        const response = await fetch(`${API_BASE_URL}${url}`, mergedOptions);

        // 处理204无内容响应
        if (response.status === 204) {
             return null;
        }

        // 尝试解析 JSON，如果失败则返回文本
        let responseData;
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.indexOf("application/json") !== -1) {
            responseData = await response.json();
        } else {
            responseData = await response.text();
            return responseData; // 非JSON响应直接返回
        }

        // 处理包装在Result中的响应
        if (responseData && typeof responseData === 'object') {
            // 检查是否是Result格式的响应 (包含code和message字段)
            if ('code' in responseData && 'message' in responseData) {
                // 处理成功响应
                if (responseData.code === 200) {
                    return responseData.data; // 只返回data部分
                }
                // 处理错误响应
                else {
                    console.error('API业务错误:', responseData.code, responseData.message);

                    // 根据不同错误码显示不同类型的提示
                    let toastType = 'error';

                    // 4xx错误 - 一般是用户输入或客户端问题
                    if (responseData.code >= 400 && responseData.code < 500) {
                        // 401未授权
                        if (responseData.code === 401) {
                            showToast('会话已过期，请重新登录', 'warning');
                            // 可能需要重定向到登录页面
                            // window.location.href = '/login';
                            return;
                        }
                        // 403禁止访问
                        else if (responseData.code === 403) {
                            showToast('无权访问该资源', 'warning');
                        }
                        // 404资源不存在
                        else if (responseData.code === 404) {
                            showToast('请求的资源不存在: ' + responseData.message, 'warning');
                        }
                        // 409冲突（例如名称重复）
                        else if (responseData.code === 409) {
                            showToast('操作冲突: ' + responseData.message, 'warning');
                        }
                        // 其他客户端错误
                        else {
                            showToast(responseData.message, 'warning');
                        }
                        toastType = 'warning';
                    }

                    throw new Error(responseData.message);
                }
            }

            // 兼容模式：非Result格式但响应成功
            if (response.ok) {
                return responseData;
            }
            // 兼容模式：非Result格式但响应失败
            else {
                const errorMessage = responseData.error || responseData.message || `HTTP错误: ${response.status}`;
                console.error('API错误:', response.status, responseData);
                showToast(errorMessage, 'error');
                throw new Error(errorMessage);
            }
        }

        // 如果responseData不是对象，直接返回
        return responseData;
    } catch (error) {
        // 只在控制台输出错误，不再显示提示，避免重复提示
        console.error('Fetch API失败:', error);

        // 添加标记到错误对象，表示错误已经被处理过
        error.errorHandled = true;

        throw error;
    }
}

// ===========================================
// UI 更新函数
// ===========================================

// 使用统一的全局 Toast 组件显示提示消息
function showToast(message, type = 'info') {
    // 通过 postMessage 发送到父窗口的 layout.js 中处理
    // layout.js 中已集成了全局的 Toast 组件
    window.parent.postMessage({
        type: 'SHOW_TOAST',
        payload: { message, type }
    }, '*');
}

// 渲染文档树
function renderTree(nodes, parentElement) {
    // 清空父元素
    if (parentElement) {
        parentElement.innerHTML = '';
    }

    if (!nodes || nodes.length === 0 || !parentElement) {
        if (parentElement) {
            parentElement.innerHTML = '<li class="text-muted p-2">此空间为空</li>';
        }
        return;
    }

    nodes.forEach(node => {
        const li = document.createElement('li');
        li.classList.add(node.type === 'FOLDER' ? 'folder-node' : 'file-node');
        li.dataset.nodeId = node.nodeId;
        li.dataset.nodeName = node.name;
        li.dataset.nodeType = node.type;
        li.dataset.spaceId = node.spaceId;

        // 创建节点外层容器（与侧边栏等宽）
        const nodeOuterContainer = document.createElement('div');
        nodeOuterContainer.classList.add('node-outer-container');

        // 创建节点容器
        const containerDiv = document.createElement('div');
        containerDiv.classList.add('nav-link-container');

        // 创建节点链接
        const link = document.createElement('a');
        link.href = '#';
        link.classList.add('node-link');
        // 根据节点类型选择图标
        // 注意：我们直接使用 Bootstrap Icons 的标准类名
        const iconClass = node.type === 'FOLDER' ? 'bi-folder' : 'bi-file-earmark-text';

        // 如果是文件夹，添加两个图标（折叠和展开状态），通过CSS控制显示/隐藏
        if (node.type === 'FOLDER') {
            link.innerHTML = `
                <i class="bi bi-folder"></i>
                <i class="bi bi-folder-fill"></i>
                <span class="node-name-text">${escapeHtml(node.name)}</span>
            `;
        } else {
            // 文件节点只需要一个图标
            link.innerHTML = `
                <i class="bi ${iconClass}"></i>
                <span class="node-name-text">${escapeHtml(node.name)}</span>
            `;
        }

        // 节点点击事件
        link.onclick = (e) => {
            e.preventDefault();
            selectNode(containerDiv, node.type, node.name, node.nodeId, node.spaceId);
            if (node.type === 'FOLDER') {
                toggleFolder(li);
            }
        };

        // 创建节点操作区域
        const actionsDiv = document.createElement('div');
        actionsDiv.classList.add('node-actions');
        // 【修改点 1】移除下面这行代码。让 CSS 来控制初始的隐藏状态 (通过 opacity 和 visibility)
        // actionsDiv.style.display = 'none';

        // 添加聊天按钮
        const chatButton = document.createElement('button');
        chatButton.classList.add('btn', 'btn-sm', 'node-chat-btn');
        chatButton.title = `与${node.type === 'FOLDER' ? '文件夹' : '文档'}对话`;
        chatButton.innerHTML = '<i class="bi bi-chat-dots"></i>';
        chatButton.onclick = (e) => {
            e.stopPropagation();
            updateChatContextAndShowSidebar(node.nodeId, node.name, node.type);
        };
        actionsDiv.appendChild(chatButton);

        // 添加更多选项按钮（三个点）
        const moreButton = document.createElement('button');
        moreButton.classList.add('btn', 'btn-sm', 'node-more-btn');
        moreButton.title = '更多选项';
        moreButton.setAttribute('data-bs-toggle', 'dropdown');
        moreButton.setAttribute('data-bs-auto-close', 'true'); // 或者 'outside' 根据你的需求
        moreButton.setAttribute('aria-expanded', 'false');
        moreButton.innerHTML = '<i class="bi bi-three-dots-vertical"></i>';
        actionsDiv.appendChild(moreButton);

        // 创建下拉菜单
        const dropdown = document.createElement('ul');
        dropdown.classList.add('dropdown-menu', 'dropdown-menu-end', 'node-dropdown');

        // 添加菜单项
        dropdown.innerHTML = `
            <li><a class="dropdown-item rename-node-item" href="#"><i class="bi bi-pencil-square me-2"></i>重命名</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item delete-node-item text-danger" href="#"><i class="bi bi-trash me-2"></i>删除</a></li>
        `;
        actionsDiv.appendChild(dropdown);

        // 为重命名项添加事件监听器
        const renameItem = dropdown.querySelector('.rename-node-item');
        renameItem.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            openRenameModal(node.nodeId, node.name);
        });

        // 为删除项添加事件监听器
        const deleteItem = dropdown.querySelector('.delete-node-item');
        deleteItem.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            confirmDeleteNode(node.nodeId, node.name, node.type);
        });

        // 组装节点结构
        containerDiv.appendChild(link);
        nodeOuterContainer.appendChild(containerDiv);
        nodeOuterContainer.appendChild(actionsDiv);
        li.appendChild(nodeOuterContainer);

        // 创建Bootstrap下拉菜单实例
        const dropdownInstance = new bootstrap.Dropdown(moreButton, {
            popperConfig: {
                // strategy: 'absolute', // Bootstrap 默认可能是 absolute
                strategy: 'fixed',    // 【修改点1】使用 'fixed' 定位策略，使其能溢出父容器
                placement: 'bottom-end', // 【修改点2】定位到按钮的右下角为基准
                                         // 如果想让菜单完全在按钮右边，可以尝试 'right-start'
                                         // 然后用 offset 的第二个参数调整垂直位置
                modifiers: [
                    {
                        name: 'offset',
                        options: {
                            // [skidding, distance]
                            // 对于 'bottom-end':
                            // skidding: 正值向右移动菜单 (沿着按钮的水平线)
                            // distance: 正值向下移动菜单 (远离按钮)
                            offset: [85, -25], // 【修改点3】例如：向右移动 10px, 向下移动 5px。请根据实际效果调整。
                                             // 如果用 'right-start'，offset: [5, 10] 表示向下5px, 向右10px
                        },
                    },
                    {
                        name: 'preventOverflow',
                        options: {
                            boundary: 'viewport', // 【修改点4】确保菜单在视口内，而不是被父元素裁剪
                            padding: 8,          // 可选：菜单距离视口边缘的最小间距
                        },
                    },
                    {
                        name: 'flip', // 翻转行为
                        options: {
                            // fallbackPlacements: ['bottom-start', 'top-end', 'top-start'], // 定义备选位置
                            // 如果不希望它翻转到上面，可以限制 fallbackPlacements
                            // 或者完全禁用 flip (enabled: false)，但这可能导致菜单在空间不足时被截断
                            enabled: true, // 通常保持启用是好的
                        },
                    }
                ],
            },
        });

        // 【修改点 2】移除下面这个 mouseout 事件监听器块
        // 它试图通过 JavaScript 직접控制 actionsDiv 的 display，这与新的 CSS 类方法冲突
        /*
        const mouseoutHandler = nodeOuterContainer._mouseoutHandler;
        if (mouseoutHandler) {
            nodeOuterContainer.removeEventListener('mouseout', mouseoutHandler);
        }
        nodeOuterContainer.addEventListener('mouseout', (e) => {
            const relatedTarget = e.relatedTarget;
            const isMenuOpen = dropdown.classList.contains('show');

            if (!isMenuOpen && !nodeOuterContainer.contains(relatedTarget) && !dropdown.contains(relatedTarget)) {
                actionsDiv.style.display = 'none';
            }
        });
        */

        // 【修改点 3】移除下面这个 'shown.bs.dropdown' 事件监听器
        // 它也试图直接控制 actionsDiv 的 display
        /*
        moreButton.addEventListener('shown.bs.dropdown', () => {
            actionsDiv.style.display = 'flex';
        });
        */

        // 【修改点 4】添加新的事件监听器来处理下拉菜单的显示和隐藏
        moreButton.addEventListener('show.bs.dropdown', function () {
            const NOuterContainer = this.closest('.node-outer-container');
            if (NOuterContainer) {
                NOuterContainer.classList.add('has-open-dropdown');
            }
            if (docTreeContainer) {
                const allNodeOuterContainers = docTreeContainer.querySelectorAll('.node-outer-container');
                allNodeOuterContainers.forEach(container => {
                    if (container !== NOuterContainer && container.classList.contains('has-open-dropdown')) {
                        container.classList.remove('has-open-dropdown');
                    }
                });
            }
        });

        moreButton.addEventListener('hidden.bs.dropdown', function () {
            const NOuterContainer = this.closest('.node-outer-container');
            if (NOuterContainer) {
                NOuterContainer.classList.remove('has-open-dropdown');
            }
        });


        // 防止菜单项点击事件冒泡 (这部分逻辑是好的，保持它)
        dropdown.querySelectorAll('.dropdown-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡到下拉菜单本身，这可以防止因 `data-bs-auto-close="true"` 导致的意外关闭
                // 执行操作后手动关闭菜单 (如果需要，你的代码中已经有这个逻辑)
                setTimeout(() => { // 使用 setTimeout 确保操作先执行
                    if (dropdownInstance) { // 确保 dropdownInstance 存在
                        dropdownInstance.hide();
                    }
                }, 50); // 短暂延迟
            });
        });

        // 添加到父元素
        parentElement.appendChild(li);

        // 如果是文件夹且有子节点，递归创建子节点
        if (node.type === 'FOLDER' && node.children && node.children.length > 0) {
            const childrenContainer = document.createElement('ul');
            childrenContainer.classList.add('node-children');
            li.appendChild(childrenContainer);
            renderTree(node.children, childrenContainer);
        }

    });
    if (nodes && nodes.length > 0) {
    }
}

// 更新主视图区域
function updateMainView(type, name, nodeId) {
    if (!mainViewTitle) return; // DOM未加载则退出
    mainViewTitle.textContent = name ? escapeHtml(name) : '选择节点';

    // 默认隐藏 ONLYOFFICE 容器，显示默认内容区域
    if (onlyofficeViewer) onlyofficeViewer.style.display = 'none';
    if (documentArea) documentArea.classList.remove('d-none');

    documentView.classList.remove('d-none');
    documentEdit.classList.add('d-none');
    if (documentAreaWrapper) {
         documentAreaWrapper.classList.remove('editing');
    }
    if(docActionButtons) docActionButtons.style.display = 'none';
    if(editButton) editButton.classList.add('d-none'); // 编辑功能与 ONLYOFFICE 冲突，暂时隐藏
    if(uploadButton) uploadButton.classList.add('d-none'); // 上传也通过 ONLYOFFICE 处理，暂时隐藏
    if(saveButton) saveButton.classList.add('d-none');
    if(cancelButton) cancelButton.classList.add('d-none');

    // 清除轮询，因为视图正在改变
    clearStatusPolling();

    // 销毁旧的 ONLYOFFICE 实例
    if (onlyofficeEditor) {
        onlyofficeEditor.destroyEditor();
        onlyofficeEditor = null;
    }

    if (type === 'FILE') {
        // 显示 ONLYOFFICE 容器，隐藏默认内容区域
        if (onlyofficeViewer) onlyofficeViewer.style.display = 'block';
        if (documentArea) documentArea.classList.add('d-none');

        loadDocumentWithOnlyoffice(nodeId, name);
        // 文件操作按钮（上传、编辑等）与ONLYOFFICE逻辑冲突，暂时禁用
        // if(docActionButtons) docActionButtons.style.display = 'inline-block';
        // if(editButton) editButton.classList.remove('d-none');
        // if(uploadButton) uploadButton.classList.remove('d-none');
    } else if (type === 'FOLDER') {
        documentView.innerHTML = `<p class="text-muted p-3">已选择文件夹: ${escapeHtml(name)}</p>`;
        // 确保文件夹视图下 ONLYOFFICE 也是隐藏的
        if (onlyofficeViewer) onlyofficeViewer.style.display = 'none';
        if (documentArea) documentArea.classList.remove('d-none');

    } else {
        // 确保初始状态下 ONLYOFFICE 也是隐藏的
        if (onlyofficeViewer) onlyofficeViewer.style.display = 'none';
        if (documentArea) documentArea.classList.remove('d-none');
    }

    // 清除文档状态徽章
    updateDocumentStatusBadge(null);
}

// 新增：更新文档状态徽章的函数
function updateDocumentStatusBadge(status) {
    if (!documentVectorStatusBadge) return;
    const showReparseButton = reparseButton && currentNodeType === 'FILE';

    if (status) {
        let badgeClass = 'bg-secondary';
        let statusText = '未知状态';

        switch (status) {
            case 'PENDING':
                badgeClass = 'bg-warning text-dark';
                statusText = '解析中';
                break;
            case 'PROCESSING':
                badgeClass = 'bg-info text-dark';
                statusText = '处理中';
                break;
            case 'INDEXED':
                badgeClass = 'bg-success';
                statusText = '已解析';
                break;
            case 'FAILED':
                badgeClass = 'bg-danger';
                statusText = '解析失败';
                break;
            default:
                statusText = status; // 如果有未定义的状态，直接显示
        }
        documentVectorStatusBadge.innerHTML = `<span class="badge ${badgeClass} fs-sm">${escapeHtml(statusText)}</span>`;
        documentVectorStatusBadge.style.display = 'inline-block';

        // 重新解析按钮始终显示和启用（当选中文件节点时）
        if (showReparseButton) {
            reparseButton.style.display = 'inline-block';
            reparseButton.disabled = false;
        }

    } else {
        documentVectorStatusBadge.innerHTML = '';
        documentVectorStatusBadge.style.display = 'none';
        // 即使没有状态信息，重新解析按钮也应该显示（当选中文件节点时）
        if (showReparseButton) {
            reparseButton.style.display = 'inline-block';
            reparseButton.disabled = false;
        }
    }
}

// 使用 ONLYOFFICE 加载文档
async function loadDocumentWithOnlyoffice(nodeId, name) {
    if (!onlyofficeViewer) {
        console.error("ONLYOFFICE viewer container not found.");
        showToast("无法加载文档查看器", "error");
        return;
    }

    // 显示加载状态
    onlyofficeViewer.innerHTML = '<div class="d-flex justify-content-center align-items-center h-100"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><span class="ms-2">正在加载文档...</span></div>';

    try {
        // 调用后端获取 ONLYOFFICE 配置
        const responseData = await fetchApi(`/knowledgebase/nodes/${nodeId}/onlyoffice-config`);

        if (responseData && responseData.onlyOfficeConfig && responseData.onlyOfficeConfig.document) {
            initializeOnlyofficeViewer(responseData.onlyOfficeConfig); // 传递实际的 OO 配置对象
            updateDocumentStatusBadge(responseData.vectorStatus); // 从响应数据中获取 vectorStatus
        } else {
            throw new Error("从服务器获取的 ONLYOFFICE 配置无效或不完整");
        }
    } catch (error) {
        console.error(`加载文档 ${name} (ID: ${nodeId}) 的 ONLYOFFICE 配置失败:`, error);
        showToast(`加载文档失败: ${error.message}`, "error");
        if (onlyofficeViewer) {
            onlyofficeViewer.innerHTML = `<div class="alert alert-danger m-3" role="alert">加载文档失败: ${escapeHtml(error.message)}</div>`;
        }
        updateDocumentStatusBadge(null); // 加载失败时也清除状态
    }
}

// 初始化 ONLYOFFICE 查看器
function initializeOnlyofficeViewer(config) {
    if (!onlyofficeViewer) {
        console.error("Cannot initialize ONLYOFFICE: viewer container not found.");
        return;
    }
     if (typeof DocsAPI === 'undefined') {
        console.error("ONLYOFFICE API script not loaded.");
        showToast("文档查看器 API 加载失败，请检查 ONLYOFFICE 服务连接", "error");
        onlyofficeViewer.innerHTML = `<div class="alert alert-danger m-3" role="alert">文档查看器 API 加载失败，请检查 ONLYOFFICE 服务连接。</div>`;
        return;
    }

    // 销毁可能存在的旧实例
    if (onlyofficeEditor) {
        try {
            onlyofficeEditor.destroyEditor();
        } catch (e) {
            console.warn("Error destroying previous Onlyoffice editor instance:", e);
        }
        onlyofficeEditor = null;
    }

    // 清空容器内容，以防万一
    onlyofficeViewer.innerHTML = '';

    // 检查是否存在JWT token (从自定义tokenValue属性中获取)
    const jwtToken = config.token;
    if (jwtToken) {
        // 如果存在token，设置到config的token属性中
        config.token = jwtToken;
    }

    // 确保配置中设置了中文语言
    if (!config.editorConfig) {
        config.editorConfig = {};
    }

    // 设置语言为中文
    config.editorConfig.lang = "zh-CN";

    // 设置区域为中文
    config.editorConfig.region = "zh-CN";

    // 设置自定义配置
    if (!config.editorConfig.customization) {
        config.editorConfig.customization = {};
    }

    // 设置为纯预览模式，隐藏所有工具栏和按钮
    config.editorConfig.customization.toolbarNoTabs = true; // 隐藏顶部工具栏标签
    config.editorConfig.customization.statusBar = false; // 隐藏底部状态栏
    config.editorConfig.customization.hideRightMenu = true; // 隐藏右侧菜单
    config.editorConfig.customization.chat = false; // 禁用聊天
    config.editorConfig.customization.comments = false; // 禁用评论
    config.editorConfig.customization.compactToolbar = true; // 使用紧凑工具栏
    config.editorConfig.customization.feedback = false; // 禁用反馈按钮
    config.editorConfig.customization.help = false; // 禁用帮助按钮

    // 以下设置是JavaScript API特有的，可以进一步优化预览体验
    config.editorConfig.customization.hideRulers = true; // 隐藏标尺
    config.editorConfig.customization.macros = false; // 禁用宏
    config.editorConfig.customization.plugins = false; // 禁用插件
    config.editorConfig.customization.review = false; // 禁用审阅

    // 设置文档显示模式，设置初始缩放以适合页面
    config.editorConfig.customization.zoom = -2; // 设置为适应宽度显示
    config.editorConfig.customization.showReviewChanges = false; // 隐藏审阅更改

    documentVectorStatusBadge = document.getElementById('documentVectorStatusBadge'); // 获取徽章元素
    reparseButton = document.getElementById('reparseButton'); // 获取重新解析按钮

    try {
        onlyofficeEditor = new DocsAPI.DocEditor("onlyofficeViewer", config);
    } catch (error) {
        console.error("Error creating Onlyoffice editor instance:", error);
        showToast(`初始化文档查看器失败: ${error.message}`, "error");
        onlyofficeViewer.innerHTML = `<div class="alert alert-danger m-3" role="alert">初始化文档查看器失败: ${escapeHtml(error.message)}</div>`;
    }
}

// 加载（伪）文档内容 - 这个函数现在不再被直接调用来显示文件内容
function loadDocumentContent(nodeId, name) {
     const mockContent = `# ${escapeHtml(name.replace(/\.(md|docx|pdf|txt)$/i, ''))}\n\n这是文档 **${escapeHtml(name)}** 的内容占位符。\n\n实际应用中，这里会显示从后端获取的真实文件内容。\n节点 ID: ${nodeId}`;

     // 简单显示纯文本内容，对于 Markdown 可以考虑使用库渲染
     documentView.innerHTML = `<pre class="p-3"><code>${escapeHtml(mockContent)}</code></pre>`;
     if(documentEditTextArea) documentEditTextArea.value = mockContent;

     if (documentAreaWrapper) documentAreaWrapper.scrollTop = 0;
}

// 更新聊天侧边栏上下文并显示侧边栏
function updateChatContextAndShowSidebar(nodeId, name, type) {
    currentChatContextNodeId = nodeId;
    const contextTypeLabel = type === 'FOLDER' ? '文件夹' : '文档';
    if (chatContextTitleElement) {
        chatContextTitleElement.textContent = `[${contextTypeLabel}] ${escapeHtml(name)}`;
    }
    if (aiChatInput) {
        aiChatInput.placeholder = `基于 [${escapeHtml(name)}] 提问...`;
        aiChatInput.disabled = false;
    }
    if(aiChatSendButton) aiChatSendButton.disabled = false;

    // 清空旧消息
    if(chatMessagesContainer) chatMessagesContainer.innerHTML = '<div class="ai-message"><span>你可以开始提问了。</span></div>';

    // 激活聊天侧边栏
    showChatSidebar();

    // 确保知识库聊天组件也更新了节点ID
    if (typeof window.updateKnowledgeChatNode === 'function') {
        window.updateKnowledgeChatNode(nodeId, name, type);
    } else {
        console.error("知识库聊天组件的更新函数不可用");
    }
}

// 添加聊天消息到界面
function addChatMessage(sender, message, isHtml = false) {
    if (!chatMessagesContainer) return null;

    const messageDiv = document.createElement('div');
    messageDiv.classList.add(sender === 'user' ? 'user-message' : 'ai-message');

    const messageSpan = document.createElement('span');
    if (isHtml) {
         messageSpan.innerHTML = message; // 直接插入 HTML
    } else {
         const formattedMessage = escapeHtml(message).replace(/__NEWLINE__/g, '<br>');
         messageSpan.innerHTML = formattedMessage;
    }

    messageDiv.appendChild(messageSpan);
    chatMessagesContainer.appendChild(messageDiv);

    chatMessagesContainer.scrollTop = chatMessagesContainer.scrollHeight;
    return messageSpan; // 返回 span 元素以便追加流式内容
}


// ===========================================
// 事件处理函数
// ===========================================

// 展开/折叠文件夹
function toggleFolder(listItemElement) { // 参数改为 li 元素
    if (listItemElement && listItemElement.classList.contains('folder-node')) {
        listItemElement.classList.toggle('expanded');
    }
}

// 选择节点（文件或文件夹）
function selectNode(containerElement, type, name, nodeId, spaceId) {
    // 清除之前选中的节点
    const previousActiveNodes = document.querySelectorAll('.file-node.active, .folder-node.active');
    previousActiveNodes.forEach(node => node.classList.remove('active'));

    // 找到当前容器的父级li元素（file-node或folder-node）
    const parentNode = containerElement.closest('.file-node, .folder-node');
    if (parentNode) {
        parentNode.classList.add('active');
        currentTreeNodeElement = parentNode;
    } else {
        // 兼容性处理，以防父节点不存在
        if (currentTreeNodeElement) {
            currentTreeNodeElement.classList.remove('active');
        }
        containerElement.classList.add('active');
        currentTreeNodeElement = containerElement;
    }

    // 清除旧节点的轮询
    if (lastPolledNodeId && lastPolledNodeId !== nodeId) {
        clearStatusPolling();
    }

    currentNodeId = nodeId;
    currentNodeName = name;
    currentNodeType = type;
    currentSpaceId = spaceId;

    updateMainView(type, name, nodeId);

    // 调用知识库聊天组件的更新函数
    if (typeof window.updateKnowledgeChatNode === 'function') {
        window.updateKnowledgeChatNode(nodeId, name, type);
    } else {
        console.error("知识库聊天组件的更新函数不可用");
    }

    if (pageContentArea.classList.contains('chat-active') && currentChatContextNodeId !== nodeId) {
        updateChatContextAndShowSidebar(nodeId, name, type);
    }
}

// 显示聊天侧边栏
function showChatSidebar() {
    if (!pageContentArea || !aiChatSidebarCenter) {
        console.error('页面内容区域或聊天侧边栏元素未找到');
        return;
    }

    // 使用CSS类激活聊天侧边栏，而不是直接设置样式
    pageContentArea.classList.add('chat-active');
    aiChatSidebarCenter.classList.add('active');

    // 为了确保兼容性，同时设置样式
    aiChatSidebarCenter.style.width = '320px';
    aiChatSidebarCenter.style.padding = '1rem';
    aiChatSidebarCenter.style.border = '1px solid #dee2e6';
    aiChatSidebarCenter.style.overflow = 'hidden';
}

// 关闭聊天侧边栏
function closeChatSidebar() {
    if (!pageContentArea || !aiChatSidebarCenter) {
        console.error('页面内容区域或聊天侧边栏元素未找到');
        return;
    }

    pageContentArea.classList.remove('chat-active');
    aiChatSidebarCenter.classList.remove('active');

    // 同时设置样式，确保兼容性
    aiChatSidebarCenter.style.width = '0';
    aiChatSidebarCenter.style.padding = '0';
    aiChatSidebarCenter.style.border = 'none';
    aiChatSidebarCenter.style.overflow = 'hidden';

    currentChatContextNodeId = null;
    if(chatContextTitleElement) chatContextTitleElement.textContent = '[文档/文件夹]';
}

// 发送聊天消息
async function sendChatMessage() {
    if (!aiChatInput || !currentChatContextNodeId) return;
    const query = aiChatInput.value.trim();
    if (!query) return;

    addChatMessage('user', query);
    aiChatInput.value = '';
    aiChatInput.style.height = 'auto';
    aiChatInput.disabled = true;
    if(aiChatSendButton) aiChatSendButton.disabled = true;

    const aiMessageSpan = addChatMessage('ai', '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>', true);

    closeSseConnection(); // Ensure previous connection is closed

    const url = `${API_BASE_URL}/knowledgechat/stream`;
    const requestBody = {
        query: query,
        contextNodeId: currentChatContextNodeId
    };

    await fetchSsePost(url, requestBody, aiMessageSpan);
}

// 使用 Fetch API 处理 SSE POST 请求
async function fetchSsePost(url, body, aiMessageElement) {
     let accumulatedResponse = '';
     if (aiMessageElement) aiMessageElement.innerHTML = ''; // Clear spinner
     let reader = null; // Declare reader outside try block

     try {
         const response = await fetch(url, {
             method: 'POST',
             headers: { 'Content-Type': 'application/json' },
             body: JSON.stringify(body)
         });

         if (!response.ok) {
             let errorText = `服务器返回错误: ${response.status}`;
             try {
                const errorJson = await response.json();
                errorText = errorJson.error || errorJson.message || errorText;
             } catch(e) {
                 // Ignore if response is not JSON
             }
             throw new Error(errorText);
         }
         if (!response.body) {
             throw new Error('响应体为空');
         }

         reader = response.body.pipeThrough(new TextDecoderStream()).getReader();
         currentSseReader = reader; // Store the reader to allow cancellation

         while (true) {
             const { value, done } = await reader.read();
             if (done) {
                 break;
             }

             const lines = value.split('\n');
             for (const line of lines) {
                 if (line.startsWith('data:')) {
                     const data = line.substring(5).trim();
                      accumulatedResponse += data.replace(/__NEWLINE__/g, '\n');
                      if(aiMessageElement) aiMessageElement.innerHTML = escapeHtml(accumulatedResponse).replace(/\n/g, '<br>');
                      if(chatMessagesContainer) chatMessagesContainer.scrollTop = chatMessagesContainer.scrollHeight;
                 } else if (line.startsWith('event: error')) {
                      const errorDataLine = lines.find(l => l.startsWith('data:'));
                      const errorMsg = errorDataLine ? errorDataLine.substring(5).trim() : '未知错误';
                      console.error('SSE error event received:', errorMsg);

                      // 根据不同错误码显示不同类型的提示
                      if(aiMessageElement) aiMessageElement.innerHTML = `<span class="text-danger">错误: ${escapeHtml(errorMsg)}</span>`;
                      closeSseConnection();
                      return;
                 } else if (line.startsWith('event: info')) {
                      const infoDataLine = lines.find(l => l.startsWith('data:'));
                      const infoMsg = infoDataLine ? infoDataLine.substring(5).trim() : '';
                      console.info('SSE info event received:', infoMsg);
                      addChatMessage('ai', `<span class="text-muted">[信息] ${escapeHtml(infoMsg)}</span>`, true);
                 }
             }
         }

     } catch (error) {
         console.error("Error during fetch SSE:", error);
         if(aiMessageElement) aiMessageElement.innerHTML = `<span class="text-danger">与 AI 服务通信失败: ${escapeHtml(error.message)}</span>`;
     } finally {
          if(aiChatInput) aiChatInput.disabled = false;
          if(aiChatSendButton) aiChatSendButton.disabled = false;
          // Do not close connection here if reader exists, let closeSseConnection handle it
          if (reader && currentSseReader === reader) {
             // Only clear if this specific fetch request finished or errored,
             // and wasn't already closed by a new request starting.
             currentSseReader = null;
          } else if (reader) {
              // If reader exists but isn't the current one, try to cancel it just in case.
              reader.cancel().catch(e => console.warn("Error cancelling obsolete reader:", e));
          }
     }
}

// 关闭 SSE 连接 (现在是关闭 Fetch Stream Reader)
function closeSseConnection() {
    if (currentSseReader) {
        currentSseReader.cancel()
            .then(() => {})
            .catch(e => console.warn("Error cancelling SSE reader:", e));
        currentSseReader = null;
    }
}

// 打开新建节点模态框
function openCreateModal(event) {
    event.stopPropagation(); // Prevent triggering selectNode if called from button inside node
    let parentId = null;
    let parentName = knowledgeScopeTitle ? knowledgeScopeTitle.textContent : '知识空间';
    let spaceId = currentSpaceId;

    // Check if called from a specific folder node's context menu/button (if implemented)
    // For now, assume clicking the top 'New' button, or a folder's add button (needs implementation)
    // If called on a selected folder:
    if (currentTreeNodeElement && currentNodeType === 'FOLDER') {
         parentId = currentNodeId;
         parentName = currentNodeName;
    }

    if (!spaceId) {
        showToast('请先选择一个知识空间', 'warning');
        return;
    }

     if(createNodeParentIdInput) createNodeParentIdInput.value = parentId || '';
     if(createNodeSpaceIdInput) createNodeSpaceIdInput.value = spaceId;
     if(createNodeParentNameLabel) createNodeParentNameLabel.textContent = escapeHtml(parentName);
     if(createNodeNameInput) createNodeNameInput.value = '';
    if(createNodeTypeSelect) {
        createNodeTypeSelect.value = 'FOLDER';
        // 确保文件上传区域状态与类型选择一致
        if(fileUploadSection) fileUploadSection.style.display = 'none';
        if(fileUploadInput) fileUploadInput.value = '';
    }
     if(createNodeParentInfo) createNodeParentInfo.style.display = parentId ? 'block' : 'none'; // Show parent info only if parent exists

     const modal = bootstrap.Modal.getOrCreateInstance(createNodeModal);
     modal.show();
}

// 处理新建节点表单提交
async function handleCreateNodeSubmit(event) {
     event.preventDefault();


    // 防止重复提交
    if (window.isNodeSubmitting) {
        return;
    }

    window.isNodeSubmitting = true;

    // 确保能够获取到表单元素
    if (!createNodeParentIdInput || !createNodeSpaceIdInput || !createNodeNameInput || !createNodeTypeSelect || !createNodeModal) {
        console.error("表单元素未找到，无法提交");
        showToast("系统错误：表单元素未找到", "error");
        window.isNodeSubmitting = false;
        return;
    }

     const parentId = createNodeParentIdInput.value || null;
     const spaceId = createNodeSpaceIdInput.value;
     const name = createNodeNameInput.value.trim();
     const type = createNodeTypeSelect.value;
    const fileInput = document.getElementById('fileUploadInput');
    const hasFile = fileInput && fileInput.files && fileInput.files.length > 0;

    if (!name || !type || !spaceId) {
        showToast("请填写完整的节点信息", "warning");
        window.isNodeSubmitting = false;
        return;
    }

    // 检查文件大小（如果有文件）
    if (hasFile) {
        const file = fileInput.files[0];
        const maxSizeBytes = 30 * 1024 * 1024; // 30MB
        
        if (file.size > maxSizeBytes) {
            const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
            showToast(`文件 "${file.name}" 大小为 ${fileSizeMB}MB，超过了最大限制 30MB。请选择较小的文件。`, "error");
            window.isNodeSubmitting = false;
            return;
        }
    }

    // 禁用提交按钮，防止重复提交
    const submitButton = createNodeForm.querySelector('button[type="submit"]');
    if (submitButton) submitButton.disabled = true;

    try {
        let newNode;

        // 根据是否有文件上传，选择不同的处理逻辑
        if (type === 'FILE' && hasFile) {
            // 先创建文件节点，再上传文件内容
     const requestBody = { spaceId, parentNodeId: parentId, name, type };

            newNode = await fetchApi('/knowledgebase/nodes', { method: 'POST', body: requestBody });

            // 上传文件到新节点
            await uploadFileToNode(newNode.nodeId, fileInput.files[0]);
        } else {
            // 普通节点创建（文件夹或无文件的文件节点）
            const requestBody = { spaceId, parentNodeId: parentId, name, type };

            newNode = await fetchApi('/knowledgebase/nodes', { method: 'POST', body: requestBody });
        }

        // 尝试关闭模态框
        try {
            const modalInstance = bootstrap.Modal.getInstance(createNodeModal);
            if (modalInstance) {
                modalInstance.hide();
            }
        } catch (modalError) {
            console.warn("无法使用Bootstrap API关闭模态框:", modalError);
            // 备用方案
            if (createNodeModal && createNodeModal.classList.contains('show')) {
                createNodeModal.classList.remove('show');
                createNodeModal.style.display = 'none';
                document.body.classList.remove('modal-open');
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());
            }
        }


        // 重新加载文档树
        await loadDocumentTree(spaceId);
    } catch (error) {
        console.error("创建节点失败:", error);
        // 只有当错误没有被处理过时才显示提示
        if (!error.errorHandled) {
            showToast(`创建节点失败: ${error.message}`, 'error');
            error.errorHandled = true;
        }
    } finally {
        // 重新启用提交按钮
        if (submitButton) submitButton.disabled = false;

        // 重置提交标记
        setTimeout(() => {
            window.isNodeSubmitting = false;
        }, 500);
    }
}

// 上传文件到节点
async function uploadFileToNode(nodeId, file) {
    if (!nodeId || !file) {
        console.error("节点ID或文件对象为空，无法上传");
        return false;
    }


    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', file);

    try {
        // 设置上传请求选项
        const uploadOptions = {
            method: 'POST',
            headers: {
                // 注意：使用FormData时不要设置Content-Type，浏览器会自动设置正确的multipart/form-data和boundary
            },
            body: formData
        };

        // 发送上传请求
        const response = await fetch(`${API_BASE_URL}/knowledgebase/nodes/${nodeId}/upload`, uploadOptions);

        let responseData;
        try {
            responseData = await response.json();
        } catch (e) {
            // 如果不是JSON格式，尝试获取文本
            const textContent = await response.text();
            console.error('非JSON响应:', textContent);
            throw new Error('服务器返回非JSON格式的响应');
        }

        // 处理包装在Result中的响应
        if (responseData && typeof responseData === 'object') {
            // 检查是否是Result格式的响应
            if ('code' in responseData && 'message' in responseData) {
                // 处理成功响应
                if (responseData.code === 200) {
                    const data = responseData.data;
                    return true;
                }
                // 处理错误响应
                else {
                    console.error('文件上传业务错误:', responseData.code, responseData.message);

                    // 根据不同错误码显示不同类型的提示
                    if (responseData.code >= 400 && responseData.code < 500) {
                        showToast(`上传失败: ${responseData.message}`, 'warning');
                    } else {
                        showToast(`上传失败: ${responseData.message}`, 'error');
                    }
                    return false;
                }
            }

            // 兼容旧的返回格式
            if (response.ok) {
                // 旧格式可能有message和documentId字段
                if (responseData.message) {
                    showToast(`文件上传成功: ${responseData.message}`, 'success');
                    return true;
                }
                return true;
            } else {
                const errorMessage = responseData.error || responseData.message || `上传失败: HTTP ${response.status}`;
                console.error('文件上传失败:', response.status, responseData);
                showToast(errorMessage, 'error');
                return false;
            }
        }

        // 如果不是对象，或者无法识别的格式
        if (!response.ok) {
            showToast(`上传失败: HTTP ${response.status}`, 'error');
            return false;
        }

        // 默认成功
        showToast(`文件 "${file.name}" 上传成功`, 'success');
        return true;
    } catch (error) {
        console.error("文件上传失败:", error);
        // 只有当错误没有被处理过时才显示提示
        if (!error.errorHandled) {
            showToast(`文件上传失败: ${error.message}`, 'error');
            error.errorHandled = true;
        }
        return false;
    }
}

// 打开重命名模态框
function openRenameModal(nodeId, currentName) {
    if (!nodeId) return;
    if(renameNodeIdInput) renameNodeIdInput.value = nodeId;
    if(renameNodeNameInput) renameNodeNameInput.value = currentName;
    const modal = bootstrap.Modal.getOrCreateInstance(renameNodeModal);
    modal.show();
}

// 处理重命名表单提交
async function handleRenameNodeSubmit(event) {
    event.preventDefault();
    const nodeId = renameNodeIdInput.value;
    const newName = renameNodeNameInput.value.trim();

    if (!newName || !nodeId) return;

    const requestBody = { name: newName };

    try {
        // 禁用提交按钮，防止重复提交
        const submitButton = renameNodeForm.querySelector('button[type="submit"]');
        if (submitButton) submitButton.disabled = true;

        const updatedNode = await fetchApi(`/knowledgebase/nodes/${nodeId}`, {
            method: 'PUT',
            body: requestBody
        });

        showToast(`节点已重命名为 "${updatedNode.name}"`, 'success');

        // 尝试关闭模态框
        try {
            const modalInstance = bootstrap.Modal.getInstance(renameNodeModal);
            if (modalInstance) {
                modalInstance.hide();
            }
        } catch (modalError) {
            console.warn("无法使用Bootstrap API关闭重命名模态框:", modalError);
            // 备用方案
            if (renameNodeModal && renameNodeModal.classList.contains('show')) {
                renameNodeModal.classList.remove('show');
                renameNodeModal.style.display = 'none';
                document.body.classList.remove('modal-open');
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());
            }
        }

        // 重新加载文档树
        await loadDocumentTree(currentSpaceId);
    } catch (error) {
        console.error("重命名节点失败:", error);
        // 只有当错误没有被处理过时才显示提示
        if (!error.errorHandled) {
            showToast(`重命名节点失败: ${error.message}`, 'error');
            error.errorHandled = true;
        }
    } finally {
        // 重新启用提交按钮
        const submitButton = renameNodeForm.querySelector('button[type="submit"]');
        if (submitButton) submitButton.disabled = false;
    }
}

// 确认删除节点
function confirmDeleteNode(nodeId, nodeName, nodeType) {
    if (!nodeId) return;

    // 获取模态框元素
    const deleteModal = document.getElementById('nodeDeleteModal');
    const deleteConfirmMessage = document.getElementById('deleteConfirmMessage');
    const deleteWarningSection = document.getElementById('deleteWarningSection');
    const deleteNodeIdInput = document.getElementById('deleteNodeIdInput');
    const deleteNodeNameInput = document.getElementById('deleteNodeNameInput');
    const deleteNodeTypeInput = document.getElementById('deleteNodeTypeInput');

    if (!deleteModal || !deleteConfirmMessage || !deleteWarningSection ||
        !deleteNodeIdInput || !deleteNodeNameInput || !deleteNodeTypeInput) {
        console.error("删除确认模态框元素未找到");
        // 如果模态框元素不存在，回退到原生confirm
        const typeLabel = nodeType === 'FOLDER' ? '文件夹' : '文件';
        let confirmMessage = `确定要删除${typeLabel} "${escapeHtml(nodeName)}" 吗？`;
        if (nodeType === 'FOLDER') {
            confirmMessage += '\n\n注意: 如果文件夹包含子项目，将无法删除。您需要先清空文件夹内容。';
        } else {
            confirmMessage += '\n\n此操作不可恢复。';
        }

        if (confirm(confirmMessage)) {
            deleteNode(nodeId, nodeName, nodeType);
        }
        return;
    }

    // 设置模态框内容
    const typeLabel = nodeType === 'FOLDER' ? '文件夹' : '文件';
    deleteConfirmMessage.textContent = `确定要删除${typeLabel} "${nodeName}" 吗？`;

    // 设置警告信息
    if (nodeType === 'FOLDER') {
        deleteWarningSection.textContent = '注意: 如果文件夹包含子项目，将无法删除。您需要先清空文件夹内容。';
    } else {
        deleteWarningSection.textContent = '此操作不可恢复。';
    }

    // 设置隐藏字段
    deleteNodeIdInput.value = nodeId;
    deleteNodeNameInput.value = nodeName;
    deleteNodeTypeInput.value = nodeType;

    // 显示模态框
    const modal = bootstrap.Modal.getOrCreateInstance(deleteModal);
    modal.show();
}

// 执行删除节点
async function deleteNode(nodeId, nodeName, nodeType) {
    try {
        await fetchApi(`/knowledgebase/nodes/${nodeId}`, { method: 'DELETE' });

        if (currentNodeId === nodeId) {
             currentNodeId = null;
             currentNodeName = null;
             currentNodeType = null;
             currentTreeNodeElement = null;
             updateMainView(null, '选择节点', null);
             closeChatSidebar();
        }
        loadDocumentTree(currentSpaceId);
    } catch (error) {
        console.error('删除节点失败:', error);

        // 针对特定的业务错误，提供更清晰的提示
        if (error.message) {
            // 非空文件夹错误
            if (error.message.includes('非空文件夹') ||
                error.message.includes('不能删除非空文件夹') ||
                error.message.includes('包含子项目')) {
                showToast(`无法删除非空文件夹 "${nodeName}"，请先删除其中的内容`, 'warning');
            }
            // 权限错误
            else if (error.message.includes('权限') || error.message.includes('无权')) {
                showToast(`您没有权限删除 "${nodeName}"`, 'warning');
            }
            // 找不到资源
            else if (error.message.includes('不存在') || error.message.includes('找不到')) {
                showToast(`找不到要删除的${nodeType === 'FOLDER' ? '文件夹' : '文件'}: "${nodeName}"，可能已被删除`, 'warning');
                // 刷新树，以反映最新状态
                loadDocumentTree(currentSpaceId);
            }
            // 其他错误
            else {
                showToast(`删除失败: ${error.message}`, 'error');
            }
        } else {
            showToast(`删除失败: 未知错误`, 'error');
        }
    }
}

// 动态调整聊天输入框高度
function autoResizeChatInput() {
    if (!aiChatInput) return;
    aiChatInput.style.height = 'auto';
    // 设置一个最大高度，例如 150px
    const maxHeight = 150;
    const scrollHeight = aiChatInput.scrollHeight;
    aiChatInput.style.height = Math.min(scrollHeight, maxHeight) + 'px';
     // 如果内容超过最大高度，显示滚动条
     aiChatInput.style.overflowY = scrollHeight > maxHeight ? 'scroll' : 'hidden';
}

// ===========================================
// 初始化和事件绑定
// ===========================================
document.addEventListener('DOMContentLoaded', async function() {

    // 初始化DOM元素引用
    initializeElements();

    // 解析URL参数
    const urlParams = new URLSearchParams(window.location.search);

    // 获取知识库类型和初始化状态
    knowledgeType = document.getElementById('knowledgeType')?.value || urlParams.get('type') || 'private';
    shouldInitSpace = document.getElementById('initSpace')?.value === 'true' || urlParams.get('initSpace') === 'true';


    try {
        // 初始化知识库树
        await initializeKnowledgeTree();

        // 初始化事件监听器
        setupEventListeners();

        // 初始化重构后的知识库聊天组件 (确认该函数存在)
        if (typeof window.initializeKnowledgeChat === 'function') {
            // 延迟调用初始化，确保DOM已完全加载
            setTimeout(() => {
                try {
                    window.initializeKnowledgeChat();
                } catch (error) {
                    console.error("Knowledge Base: 知识库聊天组件初始化出错:", error);
                }
            }, 1000);
        } else {
            console.warn("Knowledge Base: 知识库聊天组件初始化函数不可用");
        }
    } catch (error) {
        console.error("Knowledge Base 初始化失败:", error);
        showToast("页面初始化失败，请刷新页面重试", "error");
    }
});

// ===========================================
// 初始化函数
// ===========================================
async function initializeKnowledgeSpace(type) {
    try {
        // 验证type参数是有效字符串
        if (!type || typeof type !== 'string' || (type !== 'team' && type !== 'private')) {
            console.error("知识空间类型无效:", type);
            showToast("知识空间类型无效，请重新选择", "error");
            return false;
        }


        // 调用后端API确保知识空间存在
        const space = await fetchApi(`/knowledgebase/spaces/ensure?type=${type}`);

        if (space) {
            currentSpaceId = space.spaceId;
            if(knowledgeScopeTitle) knowledgeScopeTitle.textContent = escapeHtml(space.name);

            // 加载知识空间的文档树
             await loadDocumentTree(currentSpaceId);


            // 返回成功，不再显示通知，避免干扰用户
            return true;
         } else {
            throw new Error("初始化知识空间失败，未返回有效数据");
         }
     } catch (error) {
        console.error("初始化知识空间失败:", error);

        // 根据错误类型提供更友好的提示
        let errorMessage = "初始化知识空间失败";
        if (error.message) {
            if (error.message.includes('权限') || error.message.includes('无权')) {
                errorMessage = `您没有权限访问${type === 'team' ? '团队' : '私人'}知识库`;
            } else if (error.message.includes('用户')) {
                errorMessage = "用户身份验证失败，请重新登录";
            } else if (error.message.includes('不存在')) {
                errorMessage = `${type === 'team' ? '团队' : '私人'}知识库不存在或无法创建`;
            } else {
                errorMessage = error.message;
            }
        }

        showToast(errorMessage, "error");

        // 显示空内容或错误状态
        if (docTreeContainer) {
            docTreeContainer.innerHTML = '<li class="text-danger p-2">初始化知识库失败，请刷新页面重试</li>';
        }
        if (knowledgeScopeTitle) {
            knowledgeScopeTitle.textContent = '知识库加载失败';
        }

        throw error; // 重新抛出错误以便上层处理
     }
}

// 加载指定空间的文档树
async function loadDocumentTree(spaceId) {
    if (!spaceId) {
        if (docTreeContainer) {
            docTreeContainer.innerHTML = '<li class="text-muted p-2">请先选择一个知识空间</li>';
        }
        return;
    }

    if (docTreeContainer) {
        docTreeContainer.innerHTML = '<li class="text-muted p-2"><div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>加载中...</li>';
    }

    try {
        const treeData = await fetchApi(`/knowledgebase/spaces/${spaceId}/tree`);

        if (treeData && treeData.length > 0) {
        renderTree(treeData, docTreeContainer);
        } else {
            if (docTreeContainer) {
                docTreeContainer.innerHTML = '<li class="text-muted p-2">此空间暂无内容，请点击"新建"按钮添加</li>';
            }
        }

        // 清除当前选中状态
        currentNodeId = null;
        currentNodeName = null;
        currentNodeType = null;
        currentTreeNodeElement = null;
        updateMainView(null, '选择节点', null);
    } catch (error) {
        console.error("加载文档树失败:", error);

        // 根据错误类型提供更友好的提示
        let errorMessage = "加载文档树失败";
        if (error.message) {
            if (error.message.includes('权限') || error.message.includes('无权')) {
                errorMessage = "您没有权限访问此知识库";
            } else if (error.message.includes('用户')) {
                errorMessage = "用户身份验证失败，请重新登录";
            } else if (error.message.includes('不存在')) {
                errorMessage = "所选知识库不存在";
            } else {
                errorMessage = error.message;
            }
        }

        if (docTreeContainer) {
            docTreeContainer.innerHTML = `<li class="text-danger p-2">${errorMessage}</li>`;
        }

    }
}

// ===========================================
// 工具函数
// ===========================================
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') return unsafe;
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// 打开文件上传模态框
function openUploadModal() {
    if (!currentNodeId || currentNodeType !== 'FILE') {
        showToast('请先选择一个文件节点', 'warning');
        return;
    }

    if (uploadNodeIdInput) uploadNodeIdInput.value = currentNodeId;
    if (uploadTargetName) uploadTargetName.textContent = escapeHtml(currentNodeName);
    if (uploadFileInput) uploadFileInput.value = '';

    const modal = bootstrap.Modal.getOrCreateInstance(fileUploadModal);
    modal.show();
}

// 处理文件上传表单提交
async function handleFileUploadSubmit(event) {
    event.preventDefault();

    if (!uploadNodeIdInput || !uploadFileInput) {
        console.error("文件上传表单元素未找到");
        showToast("系统错误：表单元素未找到", "error");
        return;
    }

    const nodeId = uploadNodeIdInput.value;
    const file = uploadFileInput.files && uploadFileInput.files[0];

    if (!nodeId || !file) {
        showToast("请选择要上传的文件", "warning");
        return;
    }

    // 检查文件大小
    const maxSizeBytes = 30 * 1024 * 1024; // 30MB
    if (file.size > maxSizeBytes) {
        const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
        showToast(`文件 "${file.name}" 大小为 ${fileSizeMB}MB，超过了最大限制 30MB。请选择较小的文件。`, "error");
        return;
    }

    // 获取是否需要更新节点名称
    const shouldUpdateNodeName = updateNodeNameCheckbox && updateNodeNameCheckbox.checked;

    // 禁用提交按钮
    const submitButton = fileUploadForm.querySelector('button[type="submit"]');
    if (submitButton) submitButton.disabled = true;

    try {
        // 上传文件
        const success = await uploadFileToNode(nodeId, file);

        if (success) {
            // 如果选择了更新节点名称
            if (shouldUpdateNodeName) {
                // 使用完整文件名（包含扩展名）作为节点名称
                const fileName = file.name;

                // 更新节点名称
                await updateNodeName(nodeId, fileName);
            }

            // 关闭模态框
            try {
                const modalInstance = bootstrap.Modal.getInstance(fileUploadModal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            } catch (modalError) {
                console.warn("无法使用Bootstrap API关闭模态框:", modalError);
                if (fileUploadModal && fileUploadModal.classList.contains('show')) {
                    fileUploadModal.classList.remove('show');
                    fileUploadModal.style.display = 'none';
                    document.body.classList.remove('modal-open');
                    const backdrops = document.querySelectorAll('.modal-backdrop');
                    backdrops.forEach(backdrop => backdrop.remove());
                }
            }

            // 重新加载文档树以反映名称变更
            await loadDocumentTree(currentSpaceId);
        }
    } catch (error) {
        console.error("处理文件上传失败:", error);
        // 只有当错误没有被处理过时才显示提示
        if (!error.errorHandled) {
            showToast(`处理文件上传失败: ${error.message}`, 'error');
            error.errorHandled = true;
        }
    } finally {
        // 重新启用提交按钮
        if (submitButton) submitButton.disabled = false;
    }
}

// 更新节点名称
async function updateNodeName(nodeId, newName) {
    if (!nodeId || !newName) {
        return false;
    }

    try {
        const requestBody = { name: newName };
        const updatedNode = await fetchApi(`/knowledgebase/nodes/${nodeId}`, {
            method: 'PUT',
            body: requestBody
        });

        // 如果正在查看的是被更新的节点，更新当前节点名称
        if (currentNodeId === nodeId) {
            currentNodeName = updatedNode.name;
            if (mainViewTitle) {
                mainViewTitle.textContent = escapeHtml(updatedNode.name);
            }
        }

        return true;
    } catch (error) {
        console.error("更新节点名称失败:", error);
        // 只有当错误没有被处理过时才显示提示
        if (!error.errorHandled) {
            showToast(`更新节点名称失败: ${error.message}`, 'error');
            error.errorHandled = true;
        }
        return false;
    }
}

// 初始化DOM元素引用
function initializeElements() {
    // 获取DOM元素引用
    docTreeContainer = document.getElementById('docTreeContainer');
    mainViewTitle = document.getElementById('mainViewTitle');
    documentView = document.getElementById('documentView');
    documentEdit = document.getElementById('documentEdit');
    documentEditTextArea = document.getElementById('documentEditTextArea');
    documentAreaWrapper = document.getElementById('documentAreaWrapper');
    docActionButtons = document.getElementById('docActionButtons');
    editButton = document.getElementById('editButton');
    saveButton = document.getElementById('saveButton');
    cancelButton = document.getElementById('cancelButton');
    knowledgeScopeTitle = document.getElementById('knowledgeScopeTitle');

    // 创建节点模态框元素
    createNodeModal = document.getElementById('nodeCreateModal');
    createNodeForm = document.getElementById('nodeCreateForm');
    createNodeParentIdInput = document.getElementById('createNodeParentIdInput');
    createNodeSpaceIdInput = document.getElementById('createNodeSpaceIdInput');
    createNodeNameInput = document.getElementById('createNodeNameInput');
    createNodeTypeSelect = document.getElementById('createNodeTypeSelect');
    createNodeParentInfo = document.getElementById('createNodeParentInfo');
    createNodeParentNameLabel = document.getElementById('createNodeParentNameLabel');
    fileUploadSection = document.getElementById('fileUploadSection');
    fileUploadInput = document.getElementById('fileUploadInput');

    // 重命名节点模态框元素
    renameNodeModal = document.getElementById('nodeRenameModal');
    renameNodeForm = document.getElementById('nodeRenameForm');
    renameNodeIdInput = document.getElementById('renameNodeIdInput');
    renameNodeNameInput = document.getElementById('renameNodeNameInput');

    // 删除确认模态框元素
    deleteNodeModal = document.getElementById('nodeDeleteModal');
    deleteConfirmMessage = document.getElementById('deleteConfirmMessage');
    deleteWarningSection = document.getElementById('deleteWarningSection');
    deleteNodeIdInput = document.getElementById('deleteNodeIdInput');
    deleteNodeNameInput = document.getElementById('deleteNodeNameInput');
    deleteNodeTypeInput = document.getElementById('deleteNodeTypeInput');
    confirmDeleteButton = document.getElementById('confirmDeleteButton');

    // 文件上传模态框元素
    uploadButton = document.getElementById('uploadButton');
    fileUploadModal = document.getElementById('fileUploadModal');
    fileUploadForm = document.getElementById('fileUploadForm');
    uploadNodeIdInput = document.getElementById('uploadNodeIdInput');
    uploadFileInput = document.getElementById('uploadFileInput');
    uploadTargetName = document.getElementById('uploadTargetName');
    updateNodeNameCheckbox = document.getElementById('updateNodeNameCheckbox');

    // 主页面布局元素
    pageContentArea = document.getElementById('pageContentArea');
    aiChatSidebarCenter = document.getElementById('aiChatSidebarCenter');
    closeChatSidebarCenterButton = document.getElementById('closeChatSidebarCenterButton');
    chatContextTitleElement = document.getElementById('chatContextTitleCenter');
    onlyofficeViewer = document.getElementById('onlyofficeViewer'); // 获取 ONLYOFFICE 容器

    // 不再获取旧的聊天组件元素，避免与新的组件化聊天冲突
    // chatMessagesContainer = document.getElementById('chatMessagesContainer');
    // aiChatInput = document.getElementById('aiChatInputCenter');
    // aiChatSendButton = document.getElementById('aiChatSendButtonCenter');
    documentVectorStatusBadge = document.getElementById('documentVectorStatusBadge'); // 获取徽章元素
    reparseButton = document.getElementById('reparseButton'); // 获取重新解析按钮

    // 新增：重新解析确认模态框相关元素
    reparseConfirmModal = document.getElementById('reparseConfirmModal');
    reparseConfirmNodeNameElement = document.getElementById('reparseConfirmNodeName');
    confirmReparseButton = document.getElementById('confirmReparseButton');
}

// 设置事件监听器
function setupEventListeners() {
    // 监听节点类型选择变化，控制文件上传字段的显示/隐藏
    if (createNodeTypeSelect) {
        createNodeTypeSelect.addEventListener('change', function() {
            if (fileUploadSection) {
                fileUploadSection.style.display = this.value === 'FILE' ? 'block' : 'none';
                // 当切换到文件夹类型时，清空文件选择
                if (this.value === 'FOLDER' && fileUploadInput) {
                    fileUploadInput.value = '';
                }
            }
        });
    }

    // 监听文件上传输入框变化，自动填充文件名到节点名称
    if (fileUploadInput) {
        fileUploadInput.addEventListener('change', function() {
            if (createNodeNameInput && this.files && this.files.length > 0) {
                const fileName = this.files[0].name;
                // 使用完整文件名（包含扩展名）作为节点名称
                createNodeNameInput.value = fileName;
            }
        });
    }

    // 关闭聊天侧边栏按钮
    if (closeChatSidebarCenterButton) {
        closeChatSidebarCenterButton.addEventListener('click', closeChatSidebar);
    }

    // 注释掉这行，因为在node-create-modal.js中已经添加了监听器
    // if (createNodeForm) createNodeForm.addEventListener('submit', handleCreateNodeSubmit);
    if (renameNodeForm) renameNodeForm.addEventListener('submit', handleRenameNodeSubmit);

    // 绑定顶部的"新建"按钮
    const topCreateButton = document.querySelector('[data-bs-target="#nodeCreateModal"]');
    if (topCreateButton) {
        topCreateButton.addEventListener('click', openCreateModal);
    }

    // 编辑/保存/取消按钮
    if (editButton && saveButton && cancelButton && documentView && documentEdit && documentAreaWrapper) {
        editButton.addEventListener('click', () => {
            documentView.classList.add('d-none');
            documentEdit.classList.remove('d-none');
            documentAreaWrapper.classList.add('editing');
            editButton.classList.add('d-none');
            saveButton.classList.remove('d-none');
            cancelButton.classList.remove('d-none');
        });
        saveButton.addEventListener('click', () => {
            alert('保存逻辑未实现'); // TODO: Implement save
            documentView.classList.remove('d-none');
            documentEdit.classList.add('d-none');
            documentAreaWrapper.classList.remove('editing');
            editButton.classList.remove('d-none');
            saveButton.classList.add('d-none');
            cancelButton.classList.add('d-none');
        });
        cancelButton.addEventListener('click', () => {
            documentView.classList.remove('d-none');
            documentEdit.classList.add('d-none');
            documentAreaWrapper.classList.remove('editing');
            editButton.classList.remove('d-none');
            saveButton.classList.add('d-none');
            cancelButton.classList.add('d-none');
        });
    }

    // 绑定上传按钮
    if (uploadButton) {
        uploadButton.addEventListener('click', openUploadModal);
    }

    // 绑定文件上传表单提交
    if (fileUploadForm) {
        fileUploadForm.addEventListener('submit', handleFileUploadSubmit);
    }

    // 监听上传文件输入框变化
    if (uploadFileInput) {
        uploadFileInput.addEventListener('change', function() {
        });
    }

    // 监听删除确认按钮点击
    const confirmDeleteButton = document.getElementById('confirmDeleteButton');
    if (confirmDeleteButton) {
        confirmDeleteButton.addEventListener('click', function() {
            const nodeId = document.getElementById('deleteNodeIdInput').value;
            const nodeName = document.getElementById('deleteNodeNameInput').value;
            const nodeType = document.getElementById('deleteNodeTypeInput').value;

            if (nodeId) {
                // 隐藏模态框
                const deleteModal = document.getElementById('nodeDeleteModal');
                const modal = bootstrap.Modal.getInstance(deleteModal);
                if (modal) modal.hide();

                // 执行删除
                deleteNode(nodeId, nodeName, nodeType);
            }
        });
    }

    // 重新解析按钮事件 (打开确认模态框)
    if (reparseButton) {
        reparseButton.addEventListener('click', handleReparseDocument);
    }

    // 新增：重新解析确认模态框的确认按钮事件
    if (confirmReparseButton) {
        confirmReparseButton.addEventListener('click', executeReparseConfirmed);
    }
}

// 初始化知识库树
async function initializeKnowledgeTree() {
    // 如果需要初始化知识空间
    if (shouldInitSpace && knowledgeType) {
        try {
            await initializeKnowledgeSpace(knowledgeType);
        } catch (error) {
            console.error("初始化知识空间失败:", error);
            showToast("初始化知识空间失败，请刷新页面重试", "error");
            throw error;
        }
    } else {
        // 显示空提示，不再自动加载所有空间
        if (docTreeContainer) {
            docTreeContainer.innerHTML = '<li class="text-muted p-2">请从左侧导航选择知识库类型</li>';
        }
        if (knowledgeScopeTitle) {
            knowledgeScopeTitle.textContent = '请选择知识库';
        }
    }
}

// 修改：处理重新解析文档的函数 - 现在只负责打开确认模态框
async function handleReparseDocument() {
    if (!currentNodeId || currentNodeType !== 'FILE') {
        showToast('没有选中的文件节点可供重新解析', 'warning');
        return;
    }

    if (reparseConfirmNodeNameElement) {
        reparseConfirmNodeNameElement.textContent = escapeHtml(currentNodeName); // Set node name in modal
    }

    // Get or create modal instance and show it
    const modal = bootstrap.Modal.getOrCreateInstance(reparseConfirmModal);
    modal.show();
}

// 新增：执行实际重新解析操作的函数 (由模态框确认按钮调用)
async function executeReparseConfirmed() {
    

    if (!currentNodeId || currentNodeType !== 'FILE') {
        console.warn("executeReparseConfirmed called without a valid current node. CurrentNodeId:", currentNodeId, "Type:", currentNodeType);
        const earlyModalInstance = bootstrap.Modal.getInstance(reparseConfirmModal);
        if (earlyModalInstance) {
            earlyModalInstance.hide();
        }
        // showToast('无法执行重新解析：未选择有效的文件节点。', 'error'); // 可选的提示
        return;
    }

    // 步骤1: 关闭模态框
    
    const modalInstance = bootstrap.Modal.getInstance(reparseConfirmModal);
    if (modalInstance) {
        
        try {
            modalInstance.hide();
            
        } catch (e) {
            console.error("Error during modalInstance.hide():", e); // 日志5
        }
    } else {
        console.warn("Could not get modal instance. Modal may not close."); // 日志6
    }

    // 步骤2: 清除可能存在的旧轮询
    clearStatusPolling();

    if (reparseButton) reparseButton.disabled = true;
    const originalButtonTextSpan = reparseButton.querySelector('span');
    const originalButtonText = originalButtonTextSpan ? originalButtonTextSpan.textContent : '重新解析';
    if (originalButtonTextSpan) {
        originalButtonTextSpan.textContent = '处理中...';
    }

    try {
        const response = await fetchApi(`/knowledgebase/nodes/${currentNodeId}/reparse`, { method: 'POST' });
        if (response && response.status) {
            updateDocumentStatusBadge(response.status);
            if (response.status === 'PENDING' || response.status === 'PROCESSING') {
                startStatusPolling(currentNodeId);
            } else {
                 if (reparseButton) reparseButton.disabled = false;
            }
        } else {
            throw new Error('重新解析响应格式不正确');
        }
    } catch (error) {
        console.error('重新解析失败:', error);
        if (reparseButton) reparseButton.disabled = false;
        if (currentNodeId) {
            fetchApi(`/knowledgebase/nodes/${currentNodeId}/document-status`)
                .then(statusResponse => {
                    if (statusResponse && statusResponse.status) {
                        updateDocumentStatusBadge(statusResponse.status);
                    }
                })
                .catch(fetchError => {
                    console.warn("获取状态失败后，再次获取状态也失败：", fetchError);
                    updateDocumentStatusBadge(null);
                });
        }
    } finally {
        if (reparseButton && originalButtonTextSpan) {
            originalButtonTextSpan.textContent = originalButtonText;
        }
    }
}

// 新增：开始状态轮询
function startStatusPolling(nodeId) {
    if (!nodeId) return;
    clearStatusPolling(); // 清除任何已存在的轮询

    lastPolledNodeId = nodeId;
    currentPollingIntervalId = setInterval(async () => {
        if (lastPolledNodeId !== nodeId) { // 如果选中的节点已改变，则停止此轮询
            clearStatusPolling();
            return;
        }
        try {
            
            const statusResponse = await fetchApi(`/knowledgebase/nodes/${nodeId}/document-status`);
            if (statusResponse && statusResponse.status) {
                updateDocumentStatusBadge(statusResponse.status);
                if (statusResponse.status !== 'PENDING' && statusResponse.status !== 'PROCESSING') {
                    clearStatusPolling();
                    // 确保按钮状态在轮询结束后正确更新 - 重新解析按钮始终可用
                    if (reparseButton && currentNodeType === 'FILE') {
                         reparseButton.style.display = 'inline-block';
                         reparseButton.disabled = false;
                    }
                }
            } else {
                console.warn('轮询状态响应无效:', statusResponse);
                // Optionally, stop polling on invalid response to prevent infinite loops on certain errors
                // clearStatusPolling(); 
            }
        } catch (error) {
            console.error(`轮询文档状态失败 (nodeId: ${nodeId}):`, error);
            // 通常，如果API调用失败（例如网络错误），我们会继续轮询
            // 但如果错误是 404 (节点没了) 或 403 (无权限)，则应该停止
            if (error.message && (error.message.includes('404') || error.message.includes('403'))) {
                clearStatusPolling();
                showToast(`无法获取文档状态，轮询已停止: ${error.message}`, 'error');
            }
        }
    }, 5000); // 每5秒轮询一次
}

// 新增：清除状态轮询
function clearStatusPolling() {
    if (currentPollingIntervalId) {
        clearInterval(currentPollingIntervalId);
        currentPollingIntervalId = null;
        lastPolledNodeId = null;
        
    }
}