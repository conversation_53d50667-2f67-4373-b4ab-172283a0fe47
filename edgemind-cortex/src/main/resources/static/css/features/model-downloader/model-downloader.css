/* :root variables remain the same as previous version */
:root {
    --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

    --primary-color: #4a90e2;
    --primary-color-rgb: 74, 144, 226;
    --secondary-color: #909db0;
    --success-color: #2ecc71;
    --warning-color: #f1c40f;
    --danger-color: #e74c3c;
    --info-color: #5dade2;

    --body-bg: #f8f9fc;
    --card-bg: #ffffff;
    --card-header-bg: #fdfdff;
    --border-color: #e9edf2;
    --text-primary: #2c3e50;
    --text-secondary: #566573;
    --text-muted: #7f8c9a;

    --scrollbar-track: #f0f2f5;
    --scrollbar-thumb: #d5dbe1;
    --scrollbar-thumb-hover: #b8c1ca;

    --bs-border-radius: 0.375rem;
    --bs-border-radius-lg: 0.5rem;

    --compat-perfect: #1abc9c;
    --compat-excellent: #27ae60;
    --compat-great: #2ecc71;
    --compat-good: #82c977;
    --compat-decent: #3498db;
    --compat-moderate: #5dade2;
    --compat-fair: #f1c40f;
    --compat-limited: #e67e22;
    --compat-poor: #d35400;
    --compat-terrible: #e74c3c;
    --compat-impossible: #c0392b;
}


body {
    font-family: var(--font-family-sans-serif);
    background-color: var(--body-bg);
    color: var(--text-primary);
    font-size: 0.925rem;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary);
    font-weight: 500;
}

h6.modal-section-title { /* For modal subheadings */
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.6rem;
    text-transform: uppercase; /* Optional: for a bit more style */
    letter-spacing: 0.03em;
}


.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--bs-border-radius-lg);
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.card.shadow-sm {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03) !important;
}

.card-header {
    background-color: var(--card-header-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 0.85rem 1.25rem;
}

.card-title {
    font-weight: 600;
    font-size: 1.05rem;
}

/* Forms (no changes from previous) */
.form-control, .form-select {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--bs-border-radius);
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.15rem rgba(var(--primary-color-rgb), 0.15);
    background-color: var(--card-bg);
}

.form-check-input {
    border-color: var(--border-color);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.3rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* Buttons (no changes from previous) */
.btn {
    border-radius: var(--bs-border-radius);
    font-weight: 500;
    padding: 0.45rem 0.9rem;
    font-size: 0.875rem;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #3a7cc0;
    border-color: #3a7cc0;
}

.btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--border-color);
}

.btn-outline-secondary:hover {
    color: var(--text-primary);
    background-color: #eef1f5;
    border-color: var(--border-color);
}

.btn-sm {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
}


/* Layout columns & Search bar (no changes from previous) */
.sidebar-column { /* Sidebar specific styles if needed */
}

.main-content-column { /* Main content specific styles if needed */
}

.search-bar {
    width: 280px;
    max-width: 100%;
}

.search-bar .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.search-bar .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}


/* Model Card Redesign */
.model-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--bs-border-radius-lg);
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.model-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 18px rgba(var(--primary-color-rgb), 0.07) !important;
}

.model-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.9rem 1.1rem;
    border-bottom: 1px solid var(--border-color); /* Border can be back on header */
}

.model-card-header .model-name-container { /* Or just directly style .model-name if no container needed */
    flex-grow: 1;
    margin-right: 0.5rem;
}

.model-name {
    font-size: 1.15rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0; /* Remove bottom margin if ID is next to it */
    line-height: 1.3;
}

.model-id {
    font-family: var(--font-family-monospace);
    font-size: 0.7rem;
    color: var(--text-muted);
    background-color: var(--body-bg);
    padding: 2px 6px;
    border-radius: var(--bs-border-radius-sm);
    white-space: nowrap;
    /* margin-left: 0; -- No longer needed if in its own container */
    /* align-self: flex-start; -- Handled by parent */
}

.model-card-body {
    padding: 0.9rem 1.1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.model-meta-info {
    display: flex;
    gap: 0.8rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.8rem;
}

.model-meta-info i {
    margin-right: 0.3rem;
    color: var(--text-muted);
}

.model-compatibility-section {
    margin-top: 0.5rem;
    margin-bottom: 0.8rem;
}

.compatibility-header { /* New wrapper for score and label */
    display: flex;
    align-items: baseline; /* Align score and label nicely */
    margin-bottom: 0.3rem; /* Space before bar */
}

.compatibility-score {
    font-size: 1.5rem; /* Prominent score */
    font-weight: 600;
    line-height: 1;
    /* Color will be set by JS via .text-compat-* class */
}

.compatibility-label {
    font-size: 0.7rem;
    color: var(--text-muted);
    margin-left: 0.4rem; /* Space from score */
    font-weight: 500;
}

.compatibility-bar-container {
    height: 5px; /* Slimmer compatibility bar */
    background-color: #e9ecef;
    border-radius: 2.5px; /* Rounded to match height */
    overflow: hidden;
}

.compatibility-bar {
    height: 100%;
    width: 0%;
    border-radius: 2.5px;
    transition: width 0.5s ease-out, background-color 0.3s ease; /* Added background transition */
    /* Background color will be set by JS via specific .bg-compat-* class */
}

.model-description {
    font-size: 0.85rem;
    color: var(--text-secondary);
    line-height: 1.5;
    margin-top: auto;
    margin-bottom: 0.8rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    min-height: calc(1.5em * 3);
}

.model-card-footer {
    padding: 0.8rem 1.1rem;
    background-color: transparent;
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

.model-card-footer .btn {
    width: 100%;
}

.model-card-footer .d-flex .btn {
    width: auto;
}


#model-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(310px, 1fr)); /* Slightly narrower min */
    gap: 1.25rem;
}


/* Loader (no changes) */
.loader-sm {
    border: 3px solid var(--scrollbar-track);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1.2s linear infinite;
    margin: 0 auto;
}

.spinner-border.text-primary {
    width: 2.5rem;
    height: 2.5rem;
    border-width: .25em;
    color: var(--primary-color) !important;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* System Info section (no changes) */
.system-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.6rem 0;
    font-size: 0.9rem;
    border-bottom: 1px solid #f0f0f0;
}

.system-info-key {
    font-weight: 600;
    color: var(--text-primary);
    margin-right: 1rem;
    white-space: nowrap;
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.system-info-key i {
    margin-right: 0.6em;
    color: #007bff;
    font-size: 1.1em;
    width: 18px;
    text-align: center;
}

.system-info-value {
    font-weight: 500;
    color: var(--text-secondary);
    text-align: right;
    word-break: break-word;
    font-size: 0.9rem;
}

#system-info .alert {
    font-size: 0.85rem;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
    border: 1px solid rgba(255, 193, 7, 0.2);
    border-radius: 10px;
    color: #856404;
    color: var(--primary-color);
    border: 1px solid rgba(var(--primary-color-rgb), 0.1);
}

/* Modal specific styles - Info Section */
.modal-info-section {
    margin-bottom: 1rem; /* Space between sections on mobile */
}

.modal-info-item {
    display: flex;
    justify-content: space-between; /* Default for wide screens */
    align-items: flex-start; /* Align items to top if text wraps */
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.modal-info-key {
    color: var(--text-secondary);
    font-weight: 500;
    white-space: nowrap;
    padding-right: 1rem; /* Ensure space between key and value */
    flex-basis: 40%; /* Give key a basis */
    flex-shrink: 0; /* Prevent key from shrinking too much */
}

.modal-info-value {
    color: var(--text-primary);
    text-align: right; /* Align value to the right */
    word-break: break-word; /* Allow long values to wrap */
    flex-grow: 1; /* Allow value to take remaining space */
}

@media (max-width: 575.98px) {
    /* For extra small screens, stack key-value */
    .modal-info-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .modal-info-key {
        padding-right: 0;
        margin-bottom: 0.1rem; /* Small space when stacked */
        flex-basis: auto;
    }

    .modal-info-value {
        text-align: left;
    }
}


.modal-description-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.6;
    max-height: 150px;
    overflow-y: auto;
    padding: 10px;
    border-radius: var(--bs-border-radius);
    border: 1px solid var(--border-color);
    background-color: #fcfdff; /* Even lighter for description box */
}

.modal-progress .progress-bar {
    background-color: var(--primary-color);
    border-radius: 4px;
    transition: width 0.3s ease-in-out;
}

.modal-progress-status {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.alert-light {
    background-color: transparent;
    border: 1px dashed var(--border-color);
    color: var(--text-muted);
}

/* Compatibility Bar & Text Colors (no changes) */
.bg-compat-perfect {
    background-color: var(--compat-perfect);
}

.bg-compat-excellent {
    background-color: var(--compat-excellent);
}

.bg-compat-great {
    background-color: var(--compat-great);
}

.bg-compat-good {
    background-color: var(--compat-good);
}

.bg-compat-decent {
    background-color: var(--compat-decent);
}

.bg-compat-moderate {
    background-color: var(--compat-moderate);
}

.bg-compat-fair {
    background-color: var(--compat-fair);
}

.bg-compat-limited {
    background-color: var(--compat-limited);
}

.bg-compat-poor {
    background-color: var(--compat-poor);
}

.bg-compat-terrible {
    background-color: var(--compat-terrible);
}

.bg-compat-impossible {
    background-color: var(--compat-impossible);
}

.text-compat-perfect {
    color: var(--compat-perfect);
}

.text-compat-excellent {
    color: var(--compat-excellent);
}

.text-compat-great {
    color: var(--compat-great);
}

.text-compat-good {
    color: var(--compat-good);
}

.text-compat-decent {
    color: var(--compat-decent);
}

.text-compat-moderate {
    color: var(--compat-moderate);
}

.text-compat-fair {
    color: var(--compat-fair);
}

.text-compat-limited {
    color: var(--compat-limited);
}

.text-compat-poor {
    color: var(--compat-poor);
}

.text-compat-terrible {
    color: var(--compat-terrible);
}

.text-compat-impossible {
    color: var(--compat-impossible);
}


/* Responsive adjustments (minor changes to grid min width) */
@media (max-width: 991.98px) {
    #model-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }

    .card-title {
        font-size: 1rem;
    }

    .search-bar {
        width: 240px;
    }
}

@media (max-width: 767.98px) {
    #model-grid {
        grid-template-columns: 1fr;
    }

    .search-bar {
        width: 100% !important;
    }

    .card-header.d-flex.flex-wrap {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .card-header.d-flex.flex-wrap .card-title {
        margin-bottom: 0.5rem;
    }

    .model-card-header .model-name-container {
        margin-right: 0; /* No right margin when stacked */
        margin-bottom: 0.25rem; /* Space between name and id/badge container */
    }

    .model-id-badge-container {
        align-items: flex-start; /* Align ID and badge to left when stacked */
    }

    .model-installed-badge {
        top: 0.5rem; /* Adjust for mobile */
        right: 0.5rem; /* Adjust for mobile */
    }
}

/* Minimalist Delete Confirmation Modal */
.modal-delete-confirm .modal-content {
    border-radius: var(--bs-border-radius-lg);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.modal-delete-confirm .modal-header {
    border-bottom: none;
    padding: 1rem 1.25rem 0.5rem; /* Less padding */
}

.modal-delete-confirm .modal-header .btn-close {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
}

.modal-delete-confirm .modal-title {
    font-size: 1.1rem;
    font-weight: 600;
}

.modal-delete-confirm .modal-body {
    padding: 0.5rem 1.25rem 1rem; /* Adjust padding */
    font-size: 0.9rem;
}

.modal-delete-confirm .modal-body p {
    margin-bottom: 0.5rem;
}

.modal-delete-confirm .modal-body strong {
    font-weight: 500;
    color: var(--text-primary);
}

.modal-delete-confirm .modal-body .text-danger {
    font-size: 0.8rem;
    color: var(--danger-color) !important; /* Ensure danger color */
}

.modal-delete-confirm .modal-footer {
    border-top: none;
    padding: 0.5rem 1.25rem 1rem; /* Less padding */
    justify-content: flex-end; /* Align buttons to right */
}

.modal-delete-confirm .modal-footer .btn {
    min-width: 90px; /* Ensure buttons have decent width */
}