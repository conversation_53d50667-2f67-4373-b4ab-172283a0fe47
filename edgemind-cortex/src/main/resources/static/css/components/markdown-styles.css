/* Markdown 样式调整 */

/* 调整列表行高 */
.ai-message ul,
.ai-message ol,
.welcome-message ul,
.welcome-message ol {
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
}

.ai-message li,
.welcome-message li {
    margin-bottom: 0.5rem; /* 增加列表项之间的间距 */
    line-height: 1.6; /* 增加行高 */
}

/* 调整嵌套列表的间距 */
.ai-message li > ul,
.ai-message li > ol,
.welcome-message li > ul,
.welcome-message li > ol {
    margin-top: 0.5rem;
    margin-bottom: 0.25rem;
}

/* 调整段落间距 */
.ai-message p,
.welcome-message p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

/* 调整标题间距 */
.ai-message h1, .ai-message h2, .ai-message h3, 
.ai-message h4, .ai-message h5, .ai-message h6,
.welcome-message h1, .welcome-message h2, .welcome-message h3, 
.welcome-message h4, .welcome-message h5, .welcome-message h6 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

/* 调整代码块间距 */
.ai-message pre,
.welcome-message pre {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
