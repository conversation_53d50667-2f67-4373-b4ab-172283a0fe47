package com.zibbava.edgemind.cortex.aspect;

import cn.dev33.satoken.stp.StpUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zibbava.edgemind.cortex.annotation.OperationLog;
import com.zibbava.edgemind.cortex.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;

@Slf4j
@Aspect
@Component
public class OperationLogAspect {

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 定义切点：所有标注了@OperationLog注解的方法
     */
    @Pointcut("@annotation(com.zibbava.edgemind.cortex.annotation.OperationLog)")
    public void operationLogPointcut() {}

    /**
     * 环绕通知：记录操作日志
     */
    @Around("operationLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        Exception exception = null;

        try {
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            // 记录操作日志
            recordOperationLog(joinPoint, result, exception, executionTime);
        }
    }

    /**
     * 异常通知：记录异常操作日志
     */
    @AfterThrowing(pointcut = "operationLogPointcut()", throwing = "exception")
    public void afterThrowing(JoinPoint joinPoint, Exception exception) {
        // 在around方法中已经处理了异常情况，这里可以不做额外处理
        // 或者可以记录额外的异常信息
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(ProceedingJoinPoint joinPoint, Object result, Exception exception, long executionTime) {
        try {
            // 获取注解信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            OperationLog operationLogAnnotation = method.getAnnotation(OperationLog.class);

            if (operationLogAnnotation == null) {
                return;
            }

            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes != null ? attributes.getRequest() : null;

            // 获取用户信息
            Long userId = null;
            String username = null;
            try {
                if (StpUtil.isLogin()) {
                    userId = StpUtil.getLoginIdAsLong();
                    username = (String) StpUtil.getSession().get("username");
                }
            } catch (Exception e) {
                log.warn("获取用户信息失败", e);
            }

            // 构建操作描述
            String description = buildDescription(operationLogAnnotation.description(), joinPoint, result, exception);

            // 获取请求参数
            String requestParams = null;
            if (operationLogAnnotation.recordParams() && joinPoint.getArgs().length > 0) {
                try {
                    requestParams = objectMapper.writeValueAsString(joinPoint.getArgs());
                    // 限制参数长度
                    if (requestParams.length() > 2000) {
                        requestParams = requestParams.substring(0, 2000) + "...";
                    }
                } catch (Exception e) {
                    requestParams = "参数序列化失败: " + e.getMessage();
                }
            }

            // 获取响应结果
            String responseResult = null;
            if (operationLogAnnotation.recordResult() && result != null) {
                try {
                    responseResult = objectMapper.writeValueAsString(result);
                    // 限制结果长度
                    if (responseResult.length() > 2000) {
                        responseResult = responseResult.substring(0, 2000) + "...";
                    }
                } catch (Exception e) {
                    responseResult = "结果序列化失败: " + e.getMessage();
                }
            }

            // 获取错误信息
            String errorMessage = null;
            if (exception != null && operationLogAnnotation.recordException()) {
                errorMessage = exception.getMessage();
                if (errorMessage != null && errorMessage.length() > 500) {
                    errorMessage = errorMessage.substring(0, 500) + "...";
                }
            }

            // 获取请求信息
            String method = request != null ? request.getMethod() : null;
            String requestUrl = request != null ? request.getRequestURI() : null;
            String ipAddress = getClientIpAddress(request);
            String userAgent = request != null ? request.getHeader("User-Agent") : null;
            if (userAgent != null && userAgent.length() > 500) {
                userAgent = userAgent.substring(0, 500) + "...";
            }

            // 确定操作状态
            Integer status = exception == null ? 1 : 0;

            // 记录日志
            operationLogService.recordLog(
                userId, username, operationLogAnnotation.module(), operationLogAnnotation.operationType(),
                description, method, requestUrl, requestParams, responseResult, status,
                errorMessage, ipAddress, userAgent, executionTime
            );

        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }

    /**
     * 构建操作描述
     */
    private String buildDescription(String template, ProceedingJoinPoint joinPoint, Object result, Exception exception) {
        if (template == null || template.isEmpty()) {
            // 如果没有提供描述模板，使用方法名
            return joinPoint.getSignature().getName();
        }

        // 可以在这里实现更复杂的描述构建逻辑
        // 例如：替换模板中的占位符
        String description = template;
        
        // 替换常用占位符
        if (description.contains("{method}")) {
            description = description.replace("{method}", joinPoint.getSignature().getName());
        }
        
        if (description.contains("{class}")) {
            description = description.replace("{class}", joinPoint.getTarget().getClass().getSimpleName());
        }
        
        if (description.contains("{args}")) {
            description = description.replace("{args}", Arrays.toString(joinPoint.getArgs()));
        }
        
        if (exception != null && description.contains("{error}")) {
            description = description.replace("{error}", exception.getMessage());
        }

        return description;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return null;
        }

        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        String proxyClientIp = request.getHeader("Proxy-Client-IP");
        if (proxyClientIp != null && !proxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(proxyClientIp)) {
            return proxyClientIp;
        }

        String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
        if (wlProxyClientIp != null && !wlProxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(wlProxyClientIp)) {
            return wlProxyClientIp;
        }

        return request.getRemoteAddr();
    }
}
