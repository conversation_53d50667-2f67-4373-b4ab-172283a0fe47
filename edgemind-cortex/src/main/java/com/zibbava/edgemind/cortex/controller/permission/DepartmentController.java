package com.zibbava.edgemind.cortex.controller.permission;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.cortex.entity.Department;
import com.zibbava.edgemind.cortex.entity.User;
import com.zibbava.edgemind.cortex.service.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/permission/departments") // 统一前缀
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;

    /**
     * 获取部门树
     */
    @GetMapping("/tree")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<List<Department>> getDepartmentTree() {
        List<Department> tree = departmentService.getDepartmentTree();
        return ResponseEntity.ok(tree);
    }
    
     /**
     * 获取部门列表 (扁平结构，可能用于选择上级部门)
     */
    @GetMapping
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<List<Department>> getAllDepartments() {
        List<Department> list = departmentService.list(); 
        return ResponseEntity.ok(list);
    }

    /**
     * 新增部门
     */
    @PostMapping
    @SaCheckPermission("dept:manage:create")
    public ResponseEntity<Void> addDepartment(@RequestBody Department department) {
        // TODO: 校验 deptCode 是否唯一, parentId 是否有效
        departmentService.save(department);
        return ResponseEntity.ok().build();
    }

    /**
     * 修改部门
     */
    @PutMapping("/{id}")
    @SaCheckPermission("dept:manage:update")
    public ResponseEntity<Void> updateDepartment(@PathVariable Long id, @RequestBody Department department) {
        department.setId(id);
        // TODO: 校验 deptCode 是否唯一 (如果允许修改), parentId 不能是自己或自己的子部门
        departmentService.updateById(department);
        return ResponseEntity.ok().build();
    }

    /**
     * 删除部门
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission("dept:manage:delete")
    public ResponseEntity<Void> deleteDepartment(@PathVariable Long id) {
        // TODO: 检查是否有子部门，是否有用户关联，是否允许删除
        departmentService.removeById(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取单个部门详情 (包含负责人信息等)
     */
    @GetMapping("/{id}")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<Department> getDepartmentById(@PathVariable Long id) {
        Department department = departmentService.getById(id);
        if (department != null) {
            return ResponseEntity.ok(department);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 分页查询部门列表
     */
    @GetMapping("/page")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<IPage<Department>> getDepartmentPage(@RequestParam(defaultValue = "1") int current,
                                                               @RequestParam(defaultValue = "10") int size,
                                                               @RequestParam(required = false) Map<String, Object> queryParams) {
        Page<Department> page = new Page<>(current, size);
        IPage<Department> departmentPage = departmentService.getDepartmentPage(page, queryParams);
        return ResponseEntity.ok(departmentPage);
    }

    /**
     * 获取部门下的用户列表
     */
    @GetMapping("/{id}/users")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<List<User>> getDepartmentUsers(@PathVariable Long id) {
        List<User> users = departmentService.getDepartmentUsers(id);
        return ResponseEntity.ok(users);
    }

    /**
     * 批量删除部门
     */
    @DeleteMapping("/batch")
    @SaCheckPermission("dept:manage:delete")
    public ResponseEntity<Void> batchDeleteDepartments(@RequestBody List<Long> ids) {
        departmentService.batchRemove(ids);
        return ResponseEntity.ok().build();
    }

    /**
     * 移动部门到新的父部门
     */
    @PutMapping("/{id}/move")
    @SaCheckPermission("dept:manage:update")
    public ResponseEntity<Void> moveDepartment(@PathVariable Long id, @RequestParam Long newParentId) {
        departmentService.moveDepartment(id, newParentId);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取部门统计信息
     */
    @GetMapping("/statistics")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<Map<String, Object>> getDepartmentStatistics() {
        Map<String, Object> statistics = departmentService.getDepartmentStatistics();
        return ResponseEntity.ok(statistics);
    }

    /**
     * 设置部门负责人
     */
    @PutMapping("/{id}/manager")
    @SaCheckPermission("dept:manage:update")
    public ResponseEntity<Void> setDepartmentManager(@PathVariable Long id, @RequestParam Long managerId) {
        departmentService.setDepartmentManager(id, managerId);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取部门层级路径
     */
    @GetMapping("/{id}/path")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<List<Department>> getDepartmentPath(@PathVariable Long id) {
        List<Department> path = departmentService.getDepartmentPath(id);
        return ResponseEntity.ok(path);
    }

    /**
     * 启用/禁用部门
     */
    @PutMapping("/{id}/status")
    @SaCheckPermission("dept:manage:update")
    public ResponseEntity<Void> updateDepartmentStatus(@PathVariable Long id, @RequestParam Integer status) {
        departmentService.updateDepartmentStatus(id, status);
        return ResponseEntity.ok().build();
    }
}