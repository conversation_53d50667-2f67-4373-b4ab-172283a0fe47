<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理</title>
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/features/permission/permission.css">
    <style>
        /* Styles specific to Role permission assignment modal */
        .permission-assign-tree .card {
            margin-bottom: 1rem;
            box-shadow: none;
            border: 1px solid var(--bs-border-color-translucent);
        }
        .permission-assign-tree .card-header {
            padding: 0.5rem 1rem;
            background-color: var(--bs-light);
        }
        .permission-assign-tree .card-body {
            padding: 1rem;
        }
        .permission-assign-tree .form-check {
            margin-bottom: 0.5rem;
        }
        .permission-assign-tree .ms-4 {
            margin-left: 1.75rem !important; /* Increase indent for sub-permissions */
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <h2 class="mb-4 text-primary">角色管理</h2>
        
        <!-- 搜索与操作工具栏 -->
        <div class="row toolbar-row">
            <div class="col-md-8">
                <div class="d-flex gap-2">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                        <i class="bi bi-plus-lg"></i> 新增角色
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="搜索角色名称/编码/描述">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 角色列表表格 -->
        <div class="card">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th width="50" class="ps-3"><input type="checkbox" class="form-check-input"></th>
                                <th>角色名称</th>
                                <th>角色编码</th>
                                <th>描述</th>
                                <th>创建时间</th>
                                <th>状态</th>
                                <th width="180">操作</th>
                            </tr>
                        </thead>
                        <tbody id="roleTableBody">
                            <tr>
                                <td colspan="7" class="text-center p-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- 分页组件 -->
        <div class="d-flex justify-content-between align-items-center mt-3" id="paginationContainer">
            <div class="text-muted small">
                Loading pagination...
            </div>
        </div>
    </div>
    
    <!-- 新增角色模态框 -->
    <div class="modal fade" id="addRoleModal" tabindex="-1" aria-labelledby="addRoleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addRoleModalLabel">新增角色</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addRoleForm">
                        <div class="mb-3">
                            <label for="addRoleName" class="form-label">角色名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="addRoleName" name="roleName" required>
                        </div>
                        <div class="mb-3">
                            <label for="addRoleCode" class="form-label">角色编码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="addRoleCode" name="roleCode" required pattern="^[A-Z_]+$">
                            <div class="form-text">角色编码通常使用大写字母和下划线，如：ROLE_ADMIN</div>
                        </div>
                        <div class="mb-3">
                            <label for="addRoleDescription" class="form-label">角色描述</label>
                            <textarea class="form-control" id="addRoleDescription" name="description" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">状态</label>
                            <div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="status" id="addRoleStatusEnabled" value="1" checked>
                                    <label class="form-check-label" for="addRoleStatusEnabled">启用</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="status" id="addRoleStatusDisabled" value="0">
                                    <label class="form-check-label" for="addRoleStatusDisabled">禁用</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="addRoleForm" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 编辑角色模态框 -->
    <div class="modal fade" id="editRoleModal" tabindex="-1" aria-labelledby="editRoleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editRoleModalLabel">编辑角色</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editRoleForm">
                        <input type="hidden" id="editRoleId" name="id">
                        <div class="mb-3">
                            <label for="editRoleName" class="form-label">角色名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editRoleName" name="roleName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editRoleCode" class="form-label">角色编码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editRoleCode" name="roleCode" required pattern="^[A-Z_]+$">
                            <div class="form-text">角色编码通常使用大写字母和下划线，如：ROLE_ADMIN</div>
                        </div>
                        <div class="mb-3">
                            <label for="editRoleDescription" class="form-label">角色描述</label>
                            <textarea class="form-control" id="editRoleDescription" name="description" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">状态</label>
                            <div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="status" id="editRoleStatusEnabled" value="1">
                                    <label class="form-check-label" for="editRoleStatusEnabled">启用</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="status" id="editRoleStatusDisabled" value="0">
                                    <label class="form-check-label" for="editRoleStatusDisabled">禁用</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="editRoleForm" class="btn btn-primary">保存更改</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 分配权限模态框 -->
    <div class="modal fade" id="assignPermissionModal" tabindex="-1" aria-labelledby="assignPermissionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignPermissionModalLabel">分配权限 - <span id="assignPermissionRoleName" class="text-primary fw-bold"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="assignPermissionForm">
                        <input type="hidden" id="assignPermissionRoleId" name="roleId">
                        <div class="mb-3 border-bottom pb-2">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" role="switch" id="checkAllPermissions">
                                <label class="form-check-label fw-bold" for="checkAllPermissions">全选/全不选</label>
                            </div>
                        </div>
                        
                        <div id="permissionTreeContainer" class="permission-assign-tree">
                            <div class="text-center text-muted p-3">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading permissions...</span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="assignPermissionForm" class="btn btn-primary">保存权限设置</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script type="module" src="/wkg/js/features/permission/roles.js"></script>
</body>
</html> 