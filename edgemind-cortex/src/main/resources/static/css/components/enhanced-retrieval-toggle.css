/* 增强检索切换按钮样式 */
.enhanced-retrieval-toggle {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6c757d;
    margin-left: 6px;
    margin-right: 0;
}

.enhanced-retrieval-toggle:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
}

.enhanced-retrieval-toggle.active {
    background-color: #e8f5e8;
    border-color: #28a745;
    color: #155724;
}

.enhanced-retrieval-toggle.active:hover {
    background-color: #d4edda;
    border-color: #155724;
}

.enhanced-retrieval-toggle i {
    font-size: 16px;
    transition: transform 0.2s ease;
}

.enhanced-retrieval-toggle.active i {
    transform: scale(1.1);
}

/* 查询扩展中状态样式 */
.enhanced-retrieval-toggle.expanding {
    animation: retrieval-pulse 2s infinite;
}

/* 扩展中且激活状态的样式 */
.enhanced-retrieval-toggle.expanding.active {
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
}

/* 增强检索指示器动画 */
@keyframes retrieval-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.2);
    }
}

/* Tooltip 样式 */
.enhanced-retrieval-toggle[data-bs-toggle="tooltip"] {
    position: relative;
}

/* 响应式调整 */
@media (max-width: 576px) {
    .enhanced-retrieval-toggle {
        width: 28px;
        height: 28px;
    }
    
    .enhanced-retrieval-toggle i {
        font-size: 14px;
    }
} 