<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zibbava.edgemind.cortex.mapper.PermissionMapper">

    <!-- 根据角色ID列表查询权限列表 -->
    <select id="findPermissionsByRoleIds" resultType="com.zibbava.edgemind.cortex.entity.Permission">
        SELECT DISTINCT <!-- 使用 DISTINCT 避免重复权限 -->
            p.id,
            p.permission_name,
            p.permission_code,
            p.type,
            p.parent_id,
            p.description,
            p.status,
            p.create_time,
            p.update_time
        FROM
            sys_permission p
        JOIN
            sys_role_permission rp ON p.id = rp.permission_id
        WHERE
            rp.role_id IN
            <foreach item="roleId" collection="roleIds" open="(" separator="," close=")">
                #{roleId}
            </foreach>
          <!-- 可以考虑只查询启用的权限 -->
          <!-- AND p.status = 1 -->
    </select>

</mapper> 