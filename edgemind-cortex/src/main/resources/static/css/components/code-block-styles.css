/* 代码块样式优化 */

/* 代码块容器样式 */
.ai-message pre,
.welcome-message pre {
    background-color: #f6f8fa !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    margin: 1rem 0 !important;
    position: relative !important;
    overflow: auto !important;
    border: 1px solid #e1e4e8 !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05) !important;
}

/* 代码样式 */
.ai-message pre code,
.welcome-message pre code {
    font-family: 'SFMono-Regular', <PERSON>sol<PERSON>, 'Liberation Mono', Menlo, monospace !important;
    font-size: 1em !important; /* 调大字体大小，从0.9em到1em */
    line-height: 1.5 !important;
    display: block !important;
    white-space: pre !important;
    padding: 0 !important;
    background-color: transparent !important;
    color: #24292e !important;
    tab-size: 4 !important;
}

/* 复制按钮样式 */
.ai-message .copy-btn,
.welcome-message .copy-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 4px 8px;
    font-size: 0.8rem;
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid #d1d5da;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 鼠标悬停时显示复制按钮 */
.ai-message pre:hover .copy-btn,
.welcome-message pre:hover .copy-btn {
    opacity: 1;
}

/* 复制按钮悬停效果 */
.ai-message .copy-btn:hover,
.welcome-message .copy-btn:hover {
    background-color: #f1f1f1;
    border-color: #b1b5ba;
}

/* 复制成功状态 */
.ai-message .copy-btn.copied,
.welcome-message .copy-btn.copied {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
}

/* 代码语言标签 */
.ai-message pre[data-language]::before,
.welcome-message pre[data-language]::before {
    content: attr(data-language);
    position: absolute;
    top: 0;
    left: 0;
    padding: 2px 8px;
    font-size: 0.7rem;
    color: #6a737d;
    background-color: #f1f8ff;
    border-bottom-right-radius: 4px;
    border-top-left-radius: 7px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 行号样式 (如果需要) */
.ai-message .line-numbers,
.welcome-message .line-numbers {
    counter-reset: line;
    padding-left: 3.5em;
    position: relative;
}

.ai-message .line-numbers code::before,
.welcome-message .line-numbers code::before {
    counter-increment: line;
    content: counter(line);
    position: absolute;
    left: 0;
    width: 2.5em;
    text-align: right;
    color: #6a737d;
    padding-right: 1em;
    border-right: 1px solid #e1e4e8;
}

/* 强制高亮样式 - 针对代码语言高亮 */
.ai-message pre code.hljs .hljs-keyword,
.welcome-message pre code.hljs .hljs-keyword,
code.hljs .hljs-keyword {
    color: #d73a49 !important;
    font-weight: bold !important;
}

.ai-message pre code.hljs .hljs-function,
.welcome-message pre code.hljs .hljs-function,
code.hljs .hljs-function {
    color: #6f42c1 !important;
}

.ai-message pre code.hljs .hljs-string,
.welcome-message pre code.hljs .hljs-string,
code.hljs .hljs-string {
    color: #032f62 !important;
}

.ai-message pre code.hljs .hljs-comment,
.welcome-message pre code.hljs .hljs-comment,
code.hljs .hljs-comment {
    color: #6a737d !important;
    font-style: italic !important;
}

.ai-message pre code.hljs .hljs-number,
.welcome-message pre code.hljs .hljs-number,
code.hljs .hljs-number {
    color: #005cc5 !important;
}

/* 强制 Java 语言高亮 */
.language-java .hljs-keyword {
    color: #d73a49 !important;
    font-weight: bold !important;
}

.language-java .hljs-string {
    color: #032f62 !important;
}

.language-java .hljs-comment {
    color: #6a737d !important;
    font-style: italic !important;
}

.language-java .hljs-function {
    color: #6f42c1 !important;
}

.language-java .hljs-number {
    color: #005cc5 !important;
}

/* 移动端适配 */
@media (max-width: 576px) {
    .ai-message pre,
    .welcome-message pre {
        padding: 0.75rem;
        margin: 0.75rem 0;
    }

    .ai-message .copy-btn,
    .welcome-message .copy-btn {
        opacity: 1; /* 在移动端始终显示复制按钮 */
        top: 4px;
        right: 4px;
        padding: 2px 6px;
    }
}
