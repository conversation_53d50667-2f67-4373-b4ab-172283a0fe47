package com.zibbava.edgemind.cortex.service;

import java.util.Map;

/**
 * 系统设置服务接口
 */
public interface SystemSettingsService {

    /**
     * 获取系统唯一标识
     * @return 系统唯一标识
     */
    String getSystemIdentifier();

    /**
     * 获取知识库存储路径
     * @return 知识库存储路径
     */
    String getKnowledgeStoragePath();

    /**
     * 获取个人库存储路径
     * @return 个人库存储路径
     */
    String getPersonalKnowledgeStoragePath();

    /**
     * 更新知识库存储路径
     * @param path 新的存储路径
     */
    void updateKnowledgeStoragePath(String path);

    /**
     * 更新个人库存储路径
     * @param path 新的存储路径
     */
    void updatePersonalKnowledgeStoragePath(String path);

    /**
     * 同步知识库
     * 从指定目录导入文件到知识库
     */
    void syncKnowledgeBase();

    /**
     * 获取知识库同步状态
     * @return 同步状态信息的Map，包含 syncStatus、syncMessage 和 lastSyncTime
     */
    Map<String, Object> getKnowledgeSyncStatus();

    /**
     * 更新知识库同步状态
     * @param syncStatus 同步状态
     * @param syncMessage 同步消息
     */
    void updateSyncStatus(Integer syncStatus, String syncMessage);

    /**
     * 初始化系统设置
     * 如果设置不存在，则创建新的设置
     */
    void initSystemSettings();

    /**
     * 获取设置值
     * @param key 设置键名
     * @return 设置值
     */
    String getSettingValue(String key);

    /**
     * 获取设置值，如果不存在则返回默认值
     * @param key 设置键名
     * @param defaultValue 默认值
     * @return 设置值
     */
    String getSettingValue(String key, String defaultValue);

    /**
     * 更新设置值
     * @param key 设置键名
     * @param value 设置值
     * @param description 描述
     */
    void updateSettingValue(String key, String value, String description);
}
