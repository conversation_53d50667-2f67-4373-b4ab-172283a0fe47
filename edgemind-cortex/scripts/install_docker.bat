@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo Docker Installation Script
echo ========================================
echo.

REM Get current script directory
set CURRENT_DIR=%~dp0
set CURRENT_DIR=%CURRENT_DIR:~0,-1%

echo Current directory: %CURRENT_DIR%
echo.

REM Check if Docker Desktop Installer exists
set DOCKER_INSTALLER=%CURRENT_DIR%\Docker Desktop Installer.exe
if not exist "%DOCKER_INSTALLER%" (
    echo ERROR: Docker Desktop Installer.exe not found in current directory
    echo Please make sure Docker Desktop Installer.exe is in the same folder as this script
    pause
    exit /b 1
)

echo Found Docker installer: %DOCKER_INSTALLER%
echo.

REM Check if Docker is already installed
echo Checking if Docker is already installed...
docker --version >nul 2>&1
if !errorLevel! equ 0 (
    echo Docker is already installed!
    docker --version
    echo.
    echo Skipping installation, proceeding with configuration...
    goto :configure_only
)

echo Docker not detected, proceeding with installation...
echo.

REM Check admin rights
echo Checking admin rights...
net session >nul 2>&1
if !errorLevel! neq 0 (
    echo ERROR: Need admin rights
    echo Please right-click and "Run as admin"
    pause
    exit /b 1
)

echo OK: Admin rights confirmed
echo.

REM Set Docker paths to current directory
set DOCKER_INSTALL_PATH=%CURRENT_DIR%\Docker
set DOCKER_DATA_PATH=%CURRENT_DIR%\docker-data
set DOCKER_CONFIG_PATH=%CURRENT_DIR%\docker-config

echo Docker will be configured with:
echo - Installation path: %DOCKER_INSTALL_PATH%
echo - Data path: %DOCKER_DATA_PATH%
echo - Config path: %DOCKER_CONFIG_PATH%
echo.

REM Create directories
echo Creating Docker directories...
if not exist "%DOCKER_DATA_PATH%" mkdir "%DOCKER_DATA_PATH%"
if not exist "%DOCKER_CONFIG_PATH%" mkdir "%DOCKER_CONFIG_PATH%"

REM Create Docker daemon configuration
echo Creating Docker daemon configuration...
set DAEMON_CONFIG=%DOCKER_CONFIG_PATH%\daemon.json

echo { > "%DAEMON_CONFIG%"
echo   "data-root": "%DOCKER_DATA_PATH:\=\\%", >> "%DAEMON_CONFIG%"
echo   "registry-mirrors": [ >> "%DAEMON_CONFIG%"
echo     "https://docker.mirrors.ustc.edu.cn", >> "%DAEMON_CONFIG%"
echo     "https://hub-mirror.c.163.com" >> "%DAEMON_CONFIG%"
echo   ], >> "%DAEMON_CONFIG%"
echo   "log-driver": "json-file", >> "%DAEMON_CONFIG%"
echo   "log-opts": { >> "%DAEMON_CONFIG%"
echo     "max-size": "50m", >> "%DAEMON_CONFIG%"
echo     "max-file": "3" >> "%DAEMON_CONFIG%"
echo   } >> "%DAEMON_CONFIG%"
echo } >> "%DAEMON_CONFIG%"

echo Docker daemon configuration created: %DAEMON_CONFIG%
echo.

REM Create Docker Desktop settings for silent startup (no auto-start)
echo Creating Docker Desktop settings for silent startup...
set SETTINGS_CONFIG=%DOCKER_CONFIG_PATH%\settings.json

echo { > "%SETTINGS_CONFIG%"
echo   "configurationVersion": 8, >> "%SETTINGS_CONFIG%"
echo   "autoStart": false, >> "%SETTINGS_CONFIG%"
echo   "startInTray": true, >> "%SETTINGS_CONFIG%"
echo   "showWelcomeMessage": false, >> "%SETTINGS_CONFIG%"
echo   "checkForUpdates": false, >> "%SETTINGS_CONFIG%"
echo   "showSystemTrayIcon": true, >> "%SETTINGS_CONFIG%"
echo   "minimizeToTray": true, >> "%SETTINGS_CONFIG%"
echo   "showNotifications": false, >> "%SETTINGS_CONFIG%"
echo   "enableAnalytics": false, >> "%SETTINGS_CONFIG%"
echo   "skipUpdateToWSLPrompt": true, >> "%SETTINGS_CONFIG%"
echo   "useWindowsContainers": false, >> "%SETTINGS_CONFIG%"
echo   "wslEngineEnabled": true, >> "%SETTINGS_CONFIG%"
echo   "displayedTutorial": true, >> "%SETTINGS_CONFIG%"
echo   "skipFirstRunTutorial": true, >> "%SETTINGS_CONFIG%"
echo   "openUIOnStartupDisabled": true, >> "%SETTINGS_CONFIG%"
echo   "memoryMiB": 4096, >> "%SETTINGS_CONFIG%"
echo   "cpus": 2, >> "%SETTINGS_CONFIG%"
echo   "swapMiB": 1024, >> "%SETTINGS_CONFIG%"
echo   "diskSizeMiB": 61440, >> "%SETTINGS_CONFIG%"
echo   "filesharingDirectories": [ >> "%SETTINGS_CONFIG%"
echo     "%CURRENT_DIR:\=\\%" >> "%SETTINGS_CONFIG%"
echo   ], >> "%SETTINGS_CONFIG%"
echo   "dataFolder": "%DOCKER_DATA_PATH:\=\\%" >> "%SETTINGS_CONFIG%"
echo } >> "%SETTINGS_CONFIG%"

echo Docker Desktop settings created: %SETTINGS_CONFIG%
echo.

REM Install Docker Desktop to current directory
echo Starting Docker Desktop installation to current directory...
echo Installation path: %CURRENT_DIR%\Docker
echo This may take several minutes, please wait...
echo.

REM Create Docker installation directory
set DOCKER_INSTALL_PATH=%CURRENT_DIR%\Docker
if not exist "%DOCKER_INSTALL_PATH%" mkdir "%DOCKER_INSTALL_PATH%"

"%DOCKER_INSTALLER%" install --quiet --accept-license --backend=wsl-2 --installation-dir="%DOCKER_INSTALL_PATH%"

if !errorLevel! equ 0 (
    echo.
    echo Docker Desktop installed successfully!
    goto :show_summary
) else (
    echo.
    echo Docker installation failed or was cancelled
    echo Please check the installer and try again
    echo.
    pause
    exit /b 1
)

:configure_only
echo.
echo Configuring Docker for existing installation...
echo.

REM Check admin rights for configuration
net session >nul 2>&1
if !errorLevel! neq 0 (
    echo WARNING: Admin rights recommended for Docker configuration
    echo Some configuration changes may not take effect
    echo.
)

REM Set Docker paths to current directory
set DOCKER_INSTALL_PATH=%CURRENT_DIR%\Docker
set DOCKER_DATA_PATH=%CURRENT_DIR%\docker-data
set DOCKER_CONFIG_PATH=%CURRENT_DIR%\docker-config

echo Docker will be configured with:
echo - Installation path: %DOCKER_INSTALL_PATH%
echo - Data path: %DOCKER_DATA_PATH%
echo - Config path: %DOCKER_CONFIG_PATH%
echo.

REM Create directories
echo Creating Docker directories...
if not exist "%DOCKER_DATA_PATH%" mkdir "%DOCKER_DATA_PATH%"
if not exist "%DOCKER_CONFIG_PATH%" mkdir "%DOCKER_CONFIG_PATH%"

REM Create Docker daemon configuration
echo Creating Docker daemon configuration...
set DAEMON_CONFIG=%DOCKER_CONFIG_PATH%\daemon.json

echo { > "%DAEMON_CONFIG%"
echo   "data-root": "%DOCKER_DATA_PATH:\=\\%", >> "%DAEMON_CONFIG%"
echo   "registry-mirrors": [ >> "%DAEMON_CONFIG%"
echo     "https://docker.mirrors.ustc.edu.cn", >> "%DAEMON_CONFIG%"
echo     "https://hub-mirror.c.163.com" >> "%DAEMON_CONFIG%"
echo   ], >> "%DAEMON_CONFIG%"
echo   "log-driver": "json-file", >> "%DAEMON_CONFIG%"
echo   "log-opts": { >> "%DAEMON_CONFIG%"
echo     "max-size": "50m", >> "%DAEMON_CONFIG%"
echo     "max-file": "3" >> "%DAEMON_CONFIG%"
echo   } >> "%DAEMON_CONFIG%"
echo } >> "%DAEMON_CONFIG%"

echo Docker daemon configuration created: %DAEMON_CONFIG%
echo.

REM Create Docker Desktop settings for silent startup (no auto-start)
echo Creating Docker Desktop settings for silent startup...
set SETTINGS_CONFIG=%DOCKER_CONFIG_PATH%\settings.json

echo { > "%SETTINGS_CONFIG%"
echo   "configurationVersion": 8, >> "%SETTINGS_CONFIG%"
echo   "autoStart": false, >> "%SETTINGS_CONFIG%"
echo   "startInTray": true, >> "%SETTINGS_CONFIG%"
echo   "showWelcomeMessage": false, >> "%SETTINGS_CONFIG%"
echo   "checkForUpdates": false, >> "%SETTINGS_CONFIG%"
echo   "showSystemTrayIcon": true, >> "%SETTINGS_CONFIG%"
echo   "minimizeToTray": true, >> "%SETTINGS_CONFIG%"
echo   "showNotifications": false, >> "%SETTINGS_CONFIG%"
echo   "enableAnalytics": false, >> "%SETTINGS_CONFIG%"
echo   "skipUpdateToWSLPrompt": true, >> "%SETTINGS_CONFIG%"
echo   "useWindowsContainers": false, >> "%SETTINGS_CONFIG%"
echo   "wslEngineEnabled": true, >> "%SETTINGS_CONFIG%"
echo   "displayedTutorial": true, >> "%SETTINGS_CONFIG%"
echo   "skipFirstRunTutorial": true, >> "%SETTINGS_CONFIG%"
echo   "openUIOnStartupDisabled": true, >> "%SETTINGS_CONFIG%"
echo   "memoryMiB": 4096, >> "%SETTINGS_CONFIG%"
echo   "cpus": 2, >> "%SETTINGS_CONFIG%"
echo   "swapMiB": 1024, >> "%SETTINGS_CONFIG%"
echo   "diskSizeMiB": 61440, >> "%SETTINGS_CONFIG%"
echo   "filesharingDirectories": [ >> "%SETTINGS_CONFIG%"
echo     "%CURRENT_DIR:\=\\%" >> "%SETTINGS_CONFIG%"
echo   ], >> "%SETTINGS_CONFIG%"
echo   "dataFolder": "%DOCKER_DATA_PATH:\=\\%" >> "%SETTINGS_CONFIG%"
echo } >> "%SETTINGS_CONFIG%"

echo Docker Desktop settings created: %SETTINGS_CONFIG%
echo.

echo Configuration completed successfully!
echo.
echo IMPORTANT: To apply the new configuration:
echo 1. Close Docker Desktop if it's running
echo 2. Copy the daemon.json file to Docker's config directory:
echo    From: %DAEMON_CONFIG%
echo    To: %USERPROFILE%\.docker\daemon.json
echo 3. Copy the settings.json file to Docker Desktop's config directory:
echo    From: %SETTINGS_CONFIG%
echo    To: %APPDATA%\Docker\settings.json
echo 4. Restart Docker Desktop
echo.
echo SILENT STARTUP FEATURES CONFIGURED:
echo - Docker will start silently (configured for tray usage)
echo - Auto-start on boot is DISABLED (manual start required)
echo - Welcome messages and notifications are disabled
echo - Update prompts are disabled
echo - Memory: 4GB, CPU: 2 cores, Swap: 1GB
echo.
echo Or you can manually copy the configuration settings.
echo.

:show_summary
echo.
echo Configuration Summary:
echo =====================
echo Docker installation directory: %DOCKER_INSTALL_PATH%
echo Docker data directory: %DOCKER_DATA_PATH%
echo Docker config directory: %DOCKER_CONFIG_PATH%
echo Daemon configuration: %DAEMON_CONFIG%
echo Desktop settings: %SETTINGS_CONFIG%
echo.
echo CONFIGURED FEATURES:
echo - Local installation: %DOCKER_INSTALL_PATH%
echo - Data storage: Custom directory (%DOCKER_DATA_PATH%)
echo - Mirror acceleration: USTC + NetEase mirrors
echo - Log management: Max 50MB per file, keep 3 files
echo - Silent startup: Configured for tray usage (NO auto-start)
echo - Resource allocation: 4GB RAM, 2 CPU cores
echo - WSL2 backend enabled for better performance
echo.
echo Next steps:
echo 1. Apply the daemon.json configuration to Docker
echo 2. Apply the settings.json configuration to Docker Desktop
echo 3. Restart Docker Desktop
echo 4. Docker will NOT start automatically on boot (manual start required)
echo.

REM Wait for Docker to be ready and load images
echo ========================================
echo Loading Docker Images and Starting Services
echo ========================================
echo.

REM Start Docker Desktop automatically
echo Starting Docker Desktop...
echo.

REM Find and start Docker Desktop from local installation
set DOCKER_INSTALL_PATH=%CURRENT_DIR%\Docker
set DOCKER_DESKTOP_PATH=%DOCKER_INSTALL_PATH%\Docker Desktop.exe
set DOCKER_DESKTOP_PATH_ALT=%DOCKER_INSTALL_PATH%\Docker\Docker Desktop.exe
set DOCKER_DESKTOP_PATH_DEFAULT=C:\Program Files\Docker\Docker\Docker Desktop.exe

if exist "%DOCKER_DESKTOP_PATH%" (
    echo Found Docker Desktop at: "%DOCKER_DESKTOP_PATH%"
    echo Starting Docker Desktop from local installation...
    start "" "%DOCKER_DESKTOP_PATH%"
    echo Docker Desktop startup initiated.
) else if exist "%DOCKER_DESKTOP_PATH_ALT%" (
    echo Found Docker Desktop at: "%DOCKER_DESKTOP_PATH_ALT%"
    echo Starting Docker Desktop from local installation...
    start "" "%DOCKER_DESKTOP_PATH_ALT%"
    echo Docker Desktop startup initiated.
) else if exist "%DOCKER_DESKTOP_PATH_DEFAULT%" (
    echo Found Docker Desktop at default location: "%DOCKER_DESKTOP_PATH_DEFAULT%"
    echo Starting Docker Desktop...
    start "" "%DOCKER_DESKTOP_PATH_DEFAULT%"
    echo Docker Desktop startup initiated.
) else (
    echo WARNING: Docker Desktop not found at any expected location
    echo Checked paths:
    echo - "%DOCKER_DESKTOP_PATH%"
    echo - "%DOCKER_DESKTOP_PATH_ALT%"
    echo - "%DOCKER_DESKTOP_PATH_DEFAULT%"
    echo Please manually start Docker Desktop
)
echo.

REM Wait for Docker Desktop to start
echo Waiting 30 seconds for Docker Desktop to initialize...
timeout /t 30 /nobreak >nul
echo.

REM Refresh environment variables to include Docker in PATH
echo Refreshing environment variables...
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SYSTEM_PATH=%%b"
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "USER_PATH=%%b"
if defined USER_PATH (
    set "PATH=%SYSTEM_PATH%;%USER_PATH%"
) else (
    set "PATH=%SYSTEM_PATH%"
)
echo.

:wait_docker
echo Checking Docker daemon status...
docker info >nul 2>&1
if errorlevel 1 (
    echo Docker daemon not ready yet. Waiting 15 seconds...
    timeout /t 15 /nobreak >nul
    goto :wait_docker
)

echo Docker is ready!
echo.

REM Load Docker images
echo Loading Docker images...
echo.

REM Define image files
set REDIS_IMAGE=%CURRENT_DIR%\redis_7.2.tar
set MYSQL_IMAGE=%CURRENT_DIR%\mysql_8.0.tar
set WEAVIATE_IMAGE=%CURRENT_DIR%\cr.weaviate.io_semitechnologies_weaviate_1.30.4.tar
set ONLYOFFICE_IMAGE=%CURRENT_DIR%\onlyoffice_documentserver_8.3.3.tar

REM Load Redis image
if exist "%REDIS_IMAGE%" (
    echo Checking if Redis image already exists...
    docker images redis:7.2 --format "table {{.Repository}}:{{.Tag}}" | find "redis:7.2" >nul 2>&1
    if !errorLevel! equ 0 (
        echo Redis image already exists, skipping load
    ) else (
        echo Loading Redis image from %REDIS_IMAGE%...
        docker load -i "%REDIS_IMAGE%"
        if !errorLevel! equ 0 (
            echo Redis image loaded successfully!
        ) else (
            echo Failed to load Redis image!
        )
    )
    echo.
) else (
    echo WARNING: Redis image file not found: %REDIS_IMAGE%
    echo.
)

REM Load MySQL image
if exist "%MYSQL_IMAGE%" (
    echo Checking if MySQL image already exists...
    docker images mysql:8.0 --format "table {{.Repository}}:{{.Tag}}" | find "mysql:8.0" >nul 2>&1
    if !errorLevel! equ 0 (
        echo MySQL image already exists, skipping load
    ) else (
        echo Loading MySQL image from %MYSQL_IMAGE%...
        docker load -i "%MYSQL_IMAGE%"
        if !errorLevel! equ 0 (
            echo MySQL image loaded successfully!
        ) else (
            echo Failed to load MySQL image!
        )
    )
    echo.
) else (
    echo WARNING: MySQL image file not found: %MYSQL_IMAGE%
    echo.
)

REM Load Weaviate image
if exist "%WEAVIATE_IMAGE%" (
    echo Checking if Weaviate image already exists...
    docker images semitechnologies/weaviate:1.30.4 --format "table {{.Repository}}:{{.Tag}}" | find "semitechnologies/weaviate:1.30.4" >nul 2>&1
    if !errorLevel! equ 0 (
        echo Weaviate image already exists, skipping load
    ) else (
        echo Loading Weaviate image from %WEAVIATE_IMAGE%...
        docker load -i "%WEAVIATE_IMAGE%"
        if !errorLevel! equ 0 (
            echo Weaviate image loaded successfully!
        ) else (
            echo Failed to load Weaviate image!
        )
    )
    echo.
) else (
    echo WARNING: Weaviate image file not found: %WEAVIATE_IMAGE%
    echo.
)

REM Load OnlyOffice image
if exist "%ONLYOFFICE_IMAGE%" (
    echo Checking if OnlyOffice image already exists...
    docker images onlyoffice/documentserver:8.3.3 --format "table {{.Repository}}:{{.Tag}}" | find "onlyoffice/documentserver:8.3.3" >nul 2>&1
    if !errorLevel! equ 0 (
        echo OnlyOffice image already exists, skipping load
    ) else (
        echo Loading OnlyOffice image from %ONLYOFFICE_IMAGE%...
        docker load -i "%ONLYOFFICE_IMAGE%"
        if !errorLevel! equ 0 (
            echo OnlyOffice image loaded successfully!
        ) else (
            echo Failed to load OnlyOffice image!
        )
    )
    echo.
) else (
    echo WARNING: OnlyOffice image file not found: %ONLYOFFICE_IMAGE%
    echo.
)

REM Check if docker-compose.yml exists
set DOCKER_COMPOSE_FILE=%CURRENT_DIR%\docker-compose.yml
if not exist "%DOCKER_COMPOSE_FILE%" (
    echo ERROR: docker-compose.yml not found at %DOCKER_COMPOSE_FILE%
    echo Please make sure the docker-compose.yml file exists in the same directory as this script
    echo.
    goto :final_summary
)

echo Found docker-compose.yml at: %DOCKER_COMPOSE_FILE%
echo.

REM Check if .env file exists
set ENV_FILE=%CURRENT_DIR%\.env
if exist "%ENV_FILE%" (
    echo Found existing .env file: %ENV_FILE%
) else (
    echo WARNING: .env file not found at %ENV_FILE%
    echo Please make sure the .env file exists for docker-compose
)
echo.

REM Start services with docker-compose
echo Starting services with docker-compose...
echo.
cd /d "%CURRENT_DIR%"
docker-compose up -d

if !errorLevel! equ 0 (
    echo.
    echo ========================================
    echo Services started successfully!
    echo ========================================
    echo.
    echo Service URLs:
    echo - MySQL: localhost:3306 (root/wkg123456)
    echo - Redis: localhost:6379
    echo - OnlyOffice: http://localhost:8055
    echo - Weaviate: http://localhost:8090
    echo.
    echo You can check service status with: docker-compose ps
    echo To stop services: docker-compose down
    echo To view logs: docker-compose logs
    echo.
    goto :final_summary
) else (
    echo.
    echo Failed to start services with docker-compose!
    echo Please check the docker-compose.yml file and try again.
    echo.
    goto :final_summary
)

cd /d "%CURRENT_DIR%"

:final_summary
echo ========================================
echo Installation and Setup Complete!
echo ========================================
echo.
echo Docker installation and configuration completed.
echo Images loaded and services started (if available).
echo.
echo Process completed.
pause