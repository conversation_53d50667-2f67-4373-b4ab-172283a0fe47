/* Styles for top-right auth status */
#auth-status {
    padding: 10px 20px; /* Adjusted padding */
    text-align: right;
    font-size: 14px; /* Slightly larger font */
    color: #555; /* Slightly darker text */
    min-height: 40px; /* Ensure consistent height */
    display: flex; /* Use flex for alignment */
    align-items: center; /* Vertically center content */
    justify-content: flex-end; /* Align content to the right */
    gap: 10px; /* Add space between elements */
}

.auth-button {
     padding: 6px 14px; /* Increased button padding */
     cursor: pointer;
     border: 1px solid #e0e0e0;
     border-radius: 20px; /* Rounded pill style */
     background-color: #fff; /* White background */
     font-size: 13px; /* Adjust font size */
     transition: all 0.2s ease;
     box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.auth-button:hover {
     background-color: #f8f9fa;
     border-color: #d0d0d0;
     box-shadow: 0 3px 8px rgba(0,0,0,0.08);
     transform: translateY(-1px);
}

/* Modal dialog styling */
#authModal .modal-dialog {
    max-width: 420px; /* Slightly wider */
}

#authModal .modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15); /* Enhanced shadow */
    overflow: hidden;
}

/* Auth container styling */
.auth-container {
    padding: 2.5rem 2rem;
    background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
}

/* Logo styling */
.auth-container .logo-container {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
}

.auth-container .auth-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color, #4285f4);
}

.auth-container .auth-subtitle {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* Pills nav styling */
#authModal .nav-pills {
    background-color: #f0f4f8;
    border-radius: 50px;
    padding: 0.3rem;
    margin-bottom: 1.5rem;
}

#authModal .nav-pills .nav-link {
    color: #6c757d;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

#authModal .nav-pills .nav-link.active {
    background-color: var(--primary-color, #4285f4);
    color: white;
    box-shadow: 0 3px 6px rgba(66, 133, 244, 0.3);
}

/* Form styling */
#authModal .tab-content {
    padding: 0.5rem 0;
}

/* Input group styling */
#authModal .input-group {
    margin-bottom: 1.25rem;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.03);
    transition: all 0.2s ease;
}

#authModal .input-group:focus-within {
    box-shadow: 0 3px 10px rgba(66, 133, 244, 0.15);
}

#authModal .input-group .input-group-text {
    background-color: #f5f7fa;
    border-right: none;
    border-color: #e8eef3;
    padding-left: 1rem;
    transition: all 0.2s ease;
}

#authModal .input-group .form-control {
    border-left: none;
    border-color: #e8eef3;
    padding: 0.6rem 1rem;
    background-color: #f5f7fa;
    font-size: 0.95rem;
}

#authModal .input-group:focus-within .input-group-text,
#authModal .input-group:focus-within .form-control {
    background-color: #ffffff;
    border-color: #c6d8f0;
}

/* Style icon color */
#authModal .input-group .input-group-text .material-symbols-outlined {
    font-size: 1.2rem;
    color: var(--primary-color, #4285f4);
    font-variation-settings: 'FILL' 1, 'wght' 400;
}

/* Button styling */
#authModal form .btn {
    width: 100%;
    padding: 0.7rem 1rem;
    font-weight: 500;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 10px rgba(66, 133, 244, 0.25);
    transition: all 0.2s ease;
}

#authModal form .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(66, 133, 244, 0.3);
}

#authModal form .btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(66, 133, 244, 0.2);
}

/* Ensure icons within buttons are aligned */
#authModal form .btn .material-symbols-outlined {
    font-size: 1.2rem;
    line-height: 1;
    font-variation-settings: 'FILL' 1, 'wght' 400;
}

/* Close button positioning */
#authModal .modal-header {
    border-bottom: none;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    padding: 1rem;
}

#authModal .modal-header .btn-close {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    padding: 0.5rem;
    margin: 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

#authModal .modal-header .btn-close:hover {
    background-color: rgba(255, 255, 255, 0.95);
    transform: rotate(90deg);
}

#authModal .modal-header .btn-close:focus {
    box-shadow: 0 0 0 0.25rem rgba(66, 133, 244, 0.25);
}

/* Error message styling */
#authModal .error-message {
    color: #dc3545;
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
    min-height: 24px;
    background-color: rgba(220, 53, 69, 0.08);
    border-radius: 6px;
    margin: 0.5rem 0;
    text-align: center;
}

/* Footer styling */
.auth-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eaeef2;
}

.auth-footer p {
    font-size: 0.8rem;
    color: #8c959f;
}

.auth-footer a {
    color: var(--primary-color, #4285f4);
    text-decoration: none;
    transition: color 0.2s ease;
}

.auth-footer a:hover {
    color: var(--primary-dark, #3367d6);
    text-decoration: underline;
}

/* Remember me checkbox styling */
#authModal .form-check-input {
    border-color: #c6d8f0;
}

#authModal .form-check-input:checked {
    background-color: var(--primary-color, #4285f4);
    border-color: var(--primary-color, #4285f4);
}

#authModal .form-check-label {
    color: #6c757d;
}

/* Forgot password link */
#authModal a.small {
    color: var(--primary-color, #4285f4);
    transition: color 0.2s ease;
}

#authModal a.small:hover {
    color: var(--primary-dark, #3367d6);
    text-decoration: underline;
}

/* Animation for modal */
#authModal.fade .modal-dialog {
    transform: scale(0.95) translateY(-30px);
    transition: transform 0.3s ease-out;
}

#authModal.show .modal-dialog {
    transform: scale(1) translateY(0);
}