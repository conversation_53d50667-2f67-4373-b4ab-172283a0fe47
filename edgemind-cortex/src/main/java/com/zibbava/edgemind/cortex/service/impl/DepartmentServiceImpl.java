package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zibbava.edgemind.cortex.entity.Department;
import com.zibbava.edgemind.cortex.entity.User;
import com.zibbava.edgemind.cortex.exception.BadRequestException;
import com.zibbava.edgemind.cortex.exception.DuplicateResourceException;
import com.zibbava.edgemind.cortex.mapper.DepartmentMapper;
import com.zibbava.edgemind.cortex.mapper.UserMapper;
import com.zibbava.edgemind.cortex.service.DepartmentService;
import com.zibbava.edgemind.cortex.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DepartmentServiceImpl extends ServiceImpl<DepartmentMapper, Department> implements DepartmentService {

    @Lazy
    @Autowired
    private UserService userService;

    @Autowired
    private UserMapper userMapper;

    @Override
    public List<Department> getDepartmentTree() {
        List<Department> allDepartments = list();
        // 可以在这里加载负责人和父部门名称，如果树需要显示的话
        // 或者在构建树之后，对返回的树节点进行处理
        return buildTree(allDepartments);
    }

    /**
     * 构建部门树
     * @param departments 所有部门列表
     * @return 树形结构的部门列表 (只包含顶级节点)
     */
    private List<Department> buildTree(List<Department> departments) {
        if (departments == null || departments.isEmpty()) {
            return List.of();
        }

        // 将部门列表转为 Map<id, department>
        Map<Long, Department> departmentMap = departments.stream()
                .collect(Collectors.toMap(Department::getId, d -> d));

        // 遍历所有部门，将子节点添加到父节点的 children 列表中
        departments.forEach(department -> {
            Long parentId = department.getParentId();
            if (parentId != null && parentId != 0) { // 假设 0 或 null 是顶级节点
                Department parent = departmentMap.get(parentId);
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new java.util.ArrayList<>());
                    }
                    parent.getChildren().add(department);
                }
            }
        });

        // 筛选出顶级节点 (parentId 为 null 或 0)
        return departments.stream()
                .filter(d -> d.getParentId() == null || d.getParentId() == 0)
                .collect(Collectors.toList());

        // TODO: 可以考虑按 sortOrder 排序
    }

    /**
     * 重写 getById 方法，加载负责人和父部门名称
     * @param id 部门ID
     * @return 包含关联信息的部门对象，如果不存在则返回 null
     */
    @Override
    public Department getById(java.io.Serializable id) {
        Department department = super.getById(id);
        if (department != null) {
            // 加载负责人信息
            if (department.getManagerId() != null) {
                User manager = userService.getById(department.getManagerId());
                if (manager != null) {
                    // 避免暴露密码等敏感信息
                    manager.setPassword(null);
                }
                department.setManager(manager);
            }
            // 加载父部门名称
            if (department.getParentId() != null && department.getParentId() != 0) {
                Department parent = super.getById(department.getParentId()); // 直接用 super 避免再次加载关联
                department.setParentName(parent != null ? parent.getDeptName() : null);
            }
        }
        return department;
    }

    /**
     * 重写删除方法，添加关联检查
     */
    @Override
    @Transactional
    public boolean removeById(java.io.Serializable id) {
        Long departmentId = (Long) id;
        
        // 1. 检查是否有子部门
        LambdaQueryWrapper<Department> childQuery = new LambdaQueryWrapper<>();
        childQuery.eq(Department::getParentId, departmentId);
        if (this.count(childQuery) > 0) {
            throw new BadRequestException("存在子部门，请先删除子部门");
        }
        
        // 2. 检查是否有用户属于该部门
        LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
        userQuery.eq(User::getDeptId, departmentId);
        if (userMapper.selectCount(userQuery) > 0) { // 使用 UserMapper 查询
            throw new BadRequestException("部门下存在用户，无法删除");
        }
        
        // 3. 删除部门本身
        return super.removeById(id);
    }

    /**
     * 重写保存方法，添加校验
     */
    @Override
    public boolean save(Department entity) {
         if (entity == null || !StringUtils.hasText(entity.getDeptName())) {
            throw new BadRequestException("部门名称不能为空");
        }
        // 校验父部门是否存在
         if (entity.getParentId() != null && entity.getParentId() != 0 && this.getById(entity.getParentId()) == null) {
             throw new BadRequestException("指定的上级部门不存在: " + entity.getParentId());
        }
         // 校验部门编码是否唯一 (如果填写了)
         if (StringUtils.hasText(entity.getDeptCode())) {
             LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
             queryWrapper.eq(Department::getDeptCode, entity.getDeptCode());
             if (this.count(queryWrapper) > 0) {
                 throw new DuplicateResourceException("部门编码 '" + entity.getDeptCode() + "' 已存在");
             }
         }
         // 校验负责人是否存在 (如果填写了)
         if (entity.getManagerId() != null && userService.getById(entity.getManagerId()) == null) {
             throw new BadRequestException("指定的部门负责人不存在: " + entity.getManagerId());
         }
        LocalDateTime localDateTime = LocalDateTime.now();
        entity.setUpdateTime(localDateTime);
        entity.setCreateTime(localDateTime);

         return super.save(entity);
    }

     /**
     * 重写更新方法，添加校验
     */
    @Override
    @Transactional
    public boolean updateById(Department entity) {
        if (entity == null || entity.getId() == null) {
            throw new BadRequestException("无效的部门信息");
        }
        if (entity.getParentId() != null && entity.getParentId() != 0) {
            if(Objects.equals(entity.getId(), entity.getParentId())) {
                 throw new BadRequestException("上级部门不能设置为自身");
            }
            Department parent = this.getById(entity.getParentId()); // 使用 getById 确保获取的是最新数据
            if (parent == null) {
                throw new BadRequestException("指定的上级部门不存在: " + entity.getParentId());
            }
             // 防止将父级设置为自己的子孙节点
             if (isDescendantDept(entity.getId(), entity.getParentId())) {
                 throw new BadRequestException("不能将上级部门设置为自己的子孙部门");
             }
        }
         if (StringUtils.hasText(entity.getDeptCode())) {
             LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
             queryWrapper.eq(Department::getDeptCode, entity.getDeptCode());
             queryWrapper.ne(Department::getId, entity.getId());
             if (this.count(queryWrapper) > 0) {
                 throw new DuplicateResourceException("部门编码 '" + entity.getDeptCode() + "' 已存在");
             }
         }
         if (entity.getManagerId() != null && userService.getById(entity.getManagerId()) == null) {
             throw new BadRequestException("指定的部门负责人不存在: " + entity.getManagerId());
         }
        
        // TODO: (Optional) 如果部门信息改变（如名称），可能需要更新关联用户的缓存或冗余信息
        // 例如，如果 User 对象中冗余存储了 deptName，需要在这里查询并更新所有关联用户。
        // 或者，如果前端显示依赖缓存，可以考虑踢出部门下的用户。
         // clearCacheForDepartmentUsers(entity.getId());
         System.out.println("WARN: Department updated, related user caches/info might be stale.");

        LocalDateTime localDateTime = LocalDateTime.now();
        entity.setUpdateTime(localDateTime);
        entity.setCreateTime(localDateTime);
        return super.updateById(entity);
    }

    /**
     * 检查 targetId 是否是 sourceId 的子孙节点 (递归)
     */
    private boolean isDescendantDept(Long sourceId, Long targetId) {
         if (sourceId == null || targetId == null || Objects.equals(sourceId, targetId)) {
            return false; 
        }
        Department targetParent = this.getById(targetId); // 使用 getById 获取父级信息
        while (targetParent != null && targetParent.getParentId() != null && targetParent.getParentId() != 0) {
            if (Objects.equals(targetParent.getParentId(), sourceId)) {
                return true; 
            }
            targetParent = this.getById(targetParent.getParentId()); // 递归向上查找
        }
        return false;
    }

    @Override
    public IPage<Department> getDepartmentPage(Page<Department> page, Map<String, Object> queryParams) {
        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();

        if (queryParams != null) {
            // 部门名称模糊查询
            if (queryParams.containsKey("deptName") && StringUtils.hasText((String) queryParams.get("deptName"))) {
                queryWrapper.like(Department::getDeptName, queryParams.get("deptName"));
            }
            // 部门编码模糊查询
            if (queryParams.containsKey("deptCode") && StringUtils.hasText((String) queryParams.get("deptCode"))) {
                queryWrapper.like(Department::getDeptCode, queryParams.get("deptCode"));
            }
            // 状态查询
            if (queryParams.containsKey("status") && queryParams.get("status") != null) {
                queryWrapper.eq(Department::getStatus, queryParams.get("status"));
            }
            // 父部门查询
            if (queryParams.containsKey("parentId") && queryParams.get("parentId") != null) {
                queryWrapper.eq(Department::getParentId, queryParams.get("parentId"));
            }
        }

        queryWrapper.orderByAsc(Department::getSortOrder, Department::getCreateTime);
        return this.page(page, queryWrapper);
    }

    @Override
    public List<User> getDepartmentUsers(Long deptId) {
        if (deptId == null) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getDeptId, deptId);
        queryWrapper.eq(User::getStatus, 1); // 只查询启用的用户
        queryWrapper.orderByDesc(User::getCreateTime);

        List<User> users = userMapper.selectList(queryWrapper);
        // 清除敏感信息
        users.forEach(user -> user.setPassword(null));
        return users;
    }

    @Override
    @Transactional
    public void batchRemove(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        for (Long id : ids) {
            // 检查每个部门是否可以删除
            if (!canDeleteDepartment(id)) {
                Department dept = this.getById(id);
                String deptName = dept != null ? dept.getDeptName() : "ID:" + id;
                throw new BadRequestException("部门 [" + deptName + "] 存在子部门或用户，无法删除");
            }
        }

        // 批量删除
        this.removeByIds(ids);
    }

    @Override
    @Transactional
    public void moveDepartment(Long deptId, Long newParentId) {
        if (deptId == null) {
            throw new BadRequestException("部门ID不能为空");
        }

        Department department = this.getById(deptId);
        if (department == null) {
            throw new BadRequestException("部门不存在");
        }

        // 检查新父部门是否存在（除非是移动到根级别）
        if (newParentId != null && newParentId != 0) {
            Department newParent = this.getById(newParentId);
            if (newParent == null) {
                throw new BadRequestException("目标父部门不存在");
            }

            // 检查是否会形成循环
            if (Objects.equals(deptId, newParentId) || isDescendantDept(deptId, newParentId)) {
                throw new BadRequestException("不能将部门移动到自己或自己的子部门下");
            }
        }

        department.setParentId(newParentId);
        this.updateById(department);
    }

    @Override
    public Map<String, Object> getDepartmentStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 总部门数
        long totalDepts = this.count();
        statistics.put("totalDepts", totalDepts);

        // 启用的部门数
        LambdaQueryWrapper<Department> enabledQuery = new LambdaQueryWrapper<>();
        enabledQuery.eq(Department::getStatus, 1);
        long enabledDepts = this.count(enabledQuery);
        statistics.put("enabledDepts", enabledDepts);

        // 禁用的部门数
        statistics.put("disabledDepts", totalDepts - enabledDepts);

        // 根部门数
        LambdaQueryWrapper<Department> rootQuery = new LambdaQueryWrapper<>();
        rootQuery.and(wrapper -> wrapper.isNull(Department::getParentId).or().eq(Department::getParentId, 0));
        long rootDepts = this.count(rootQuery);
        statistics.put("rootDepts", rootDepts);

        // 有负责人的部门数
        LambdaQueryWrapper<Department> managerQuery = new LambdaQueryWrapper<>();
        managerQuery.isNotNull(Department::getManagerId);
        long deptsWithManager = this.count(managerQuery);
        statistics.put("deptsWithManager", deptsWithManager);

        return statistics;
    }

    @Override
    @Transactional
    public void setDepartmentManager(Long deptId, Long managerId) {
        if (deptId == null) {
            throw new BadRequestException("部门ID不能为空");
        }

        Department department = this.getById(deptId);
        if (department == null) {
            throw new BadRequestException("部门不存在");
        }

        // 检查用户是否存在
        if (managerId != null) {
            User manager = userService.getById(managerId);
            if (manager == null) {
                throw new BadRequestException("指定的用户不存在");
            }
            if (manager.getStatus() != 1) {
                throw new BadRequestException("指定的用户已被禁用");
            }
        }

        department.setManagerId(managerId);
        this.updateById(department);
    }

    @Override
    public List<Department> getDepartmentPath(Long deptId) {
        if (deptId == null) {
            return Collections.emptyList();
        }

        List<Department> path = new ArrayList<>();
        Department current = this.getById(deptId);

        while (current != null) {
            path.add(0, current); // 插入到开头，保持从根到叶的顺序
            if (current.getParentId() == null || current.getParentId() == 0) {
                break;
            }
            current = this.getById(current.getParentId());
        }

        return path;
    }

    @Override
    @Transactional
    public void updateDepartmentStatus(Long deptId, Integer status) {
        if (deptId == null) {
            throw new BadRequestException("部门ID不能为空");
        }

        if (status == null || (status != 0 && status != 1)) {
            throw new BadRequestException("状态值无效");
        }

        Department department = this.getById(deptId);
        if (department == null) {
            throw new BadRequestException("部门不存在");
        }

        department.setStatus(status);
        this.updateById(department);

        // 如果禁用部门，可以考虑同时禁用所有子部门
        if (status == 0) {
            disableChildDepartments(deptId);
        }
    }

    /**
     * 递归禁用子部门
     */
    private void disableChildDepartments(Long parentId) {
        LambdaQueryWrapper<Department> childQuery = new LambdaQueryWrapper<>();
        childQuery.eq(Department::getParentId, parentId);
        List<Department> children = this.list(childQuery);

        for (Department child : children) {
            child.setStatus(0);
            this.updateById(child);
            // 递归禁用子部门的子部门
            disableChildDepartments(child.getId());
        }
    }

    @Override
    public Department getDepartmentByUserId(Long userId) {
        if (userId == null) {
            return null;
        }

        User user = userService.getById(userId);
        if (user == null || user.getDeptId() == null) {
            return null;
        }

        return this.getById(user.getDeptId());
    }

    @Override
    public boolean isDeptCodeUnique(String deptCode, Long excludeId) {
        if (!StringUtils.hasText(deptCode)) {
            return true; // 空编码认为是唯一的
        }

        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Department::getDeptCode, deptCode);
        if (excludeId != null) {
            queryWrapper.ne(Department::getId, excludeId);
        }

        return this.count(queryWrapper) == 0;
    }

    @Override
    public boolean canDeleteDepartment(Long deptId) {
        if (deptId == null) {
            return false;
        }

        // 检查是否有子部门
        LambdaQueryWrapper<Department> childQuery = new LambdaQueryWrapper<>();
        childQuery.eq(Department::getParentId, deptId);
        if (this.count(childQuery) > 0) {
            return false;
        }

        // 检查是否有用户
        LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
        userQuery.eq(User::getDeptId, deptId);
        return userMapper.selectCount(userQuery) == 0;
    }

    // TODO: (Optional) Helper method to clear cache for users in a department
    // private void clearCacheForDepartmentUsers(Long deptId) { ... }
}