<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志</title>
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/features/permission/permission.css">
    <style>
        .log-card {
            transition: all 0.2s;
        }
        .log-card:hover {
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
        }
        .action-badge {
            font-size: 0.75rem;
            padding: 0.15rem 0.5rem;
            border-radius: 1rem;
        }
        .time-indicator {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .log-details {
            max-height: 250px;
            overflow-y: auto;
            font-size: 0.85rem;
            font-family: var(--bs-font-monospace);
            background-color: var(--bs-secondary-bg);
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            border: 1px solid var(--bs-border-color-translucent);
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <h2 class="mb-4 text-primary">操作日志</h2>
        
        <!-- 搜索与筛选区域 -->
        <div class="card mb-4 log-filter-card">
            <div class="card-body">
                <form class="row g-3 align-items-end">
                    <div class="col-md-2">
                        <label for="moduleSelect" class="form-label">模块</label>
                        <select class="form-select form-select-sm" id="moduleSelect">
                            <option value="" selected>全部</option>
                            <option value="USER">用户管理</option>
                            <option value="ROLE">角色管理</option>
                            <option value="PERMISSION">权限管理</option>
                            <option value="DEPT">部门管理</option>
                            <option value="SYSTEM">系统管理</option>
                            <option value="AUTH">认证授权</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="operationSelect" class="form-label">操作类型</label>
                        <select class="form-select form-select-sm" id="operationSelect">
                            <option value="" selected>全部</option>
                            <option value="CREATE">新增</option>
                            <option value="UPDATE">修改</option>
                            <option value="DELETE">删除</option>
                            <option value="QUERY">查询</option>
                            <option value="IMPORT">导入</option>
                            <option value="EXPORT">导出</option>
                            <option value="LOGIN">登录</option>
                            <option value="LOGOUT">登出</option>
                            <option value="OTHER">其他</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="userInput" class="form-label">操作人</label>
                        <input type="text" class="form-control form-control-sm" id="userInput" placeholder="用户名">
                    </div>
                    <div class="col-md-2">
                        <label for="statusSelect" class="form-label">状态</label>
                        <select class="form-select form-select-sm" id="statusSelect">
                            <option value="" selected>全部</option>
                            <option value="SUCCESS">成功</option>
                            <option value="FAILURE">失败</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="dateRangePicker" class="form-label">日期范围</label>
                        <input type="text" class="form-control form-control-sm" id="dateRangePicker" placeholder="选择日期">
                    </div>
                    <div class="col-md-2">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary btn-sm w-100">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                            <button type="reset" class="btn btn-outline-secondary btn-sm w-100">
                                <i class="bi bi-arrow-repeat"></i> 重置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 日志列表与操作 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="bi bi-journal-text me-2"></i>
                    <span class="fw-bold">日志列表</span>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-file-earmark-excel"></i> 导出
                    </button>
                    <button class="btn btn-outline-danger btn-sm">
                        <i class="bi bi-trash"></i> 清空
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th width="5%" class="ps-3">#</th>
                                <th width="10%">操作人</th>
                                <th width="10%">操作类型</th>
                                <th width="10%">模块</th>
                                <th width="30%">操作内容</th>
                                <th width="10%">IP地址</th>
                                <th width="15%">操作时间</th>
                                <th width="5%">状态</th>
                                <th width="5%">详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 日志记录1 -->
                            <tr>
                                <td class="ps-3">1</td>
                                <td>admin</td>
                                <td><span class="badge bg-success-subtle text-success-emphasis">新增</span></td>
                                <td>用户管理</td>
                                <td>新增用户: user123</td>
                                <td>*************</td>
                                <td><small>2023-09-20 14:30:25</small></td>
                                <td><span class="badge bg-success-subtle text-success-emphasis">成功</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#viewLogModal1" title="查看详情">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            
                            <!-- 日志记录2 -->
                            <tr>
                                <td class="ps-3">2</td>
                                <td>admin</td>
                                <td><span class="badge bg-primary-subtle text-primary-emphasis">修改</span></td>
                                <td>角色管理</td>
                                <td>修改角色: 管理员</td>
                                <td>*************</td>
                                <td><small>2023-09-20 14:25:18</small></td>
                                <td><span class="badge bg-success-subtle text-success-emphasis">成功</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#viewLogModal2" title="查看详情">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            
                            <!-- 日志记录3 -->
                            <tr>
                                <td class="ps-3">3</td>
                                <td>user123</td>
                                <td><span class="badge bg-info-subtle text-info-emphasis">登录</span></td>
                                <td>系统</td>
                                <td>用户登录成功</td>
                                <td>192.168.1.101</td>
                                <td><small>2023-09-20 14:20:05</small></td>
                                <td><span class="badge bg-success-subtle text-success-emphasis">成功</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#viewLogModal3" title="查看详情">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            
                            <!-- 日志记录4 (失败示例) -->
                            <tr>
                                <td class="ps-3">4</td>
                                <td>admin</td>
                                <td><span class="badge bg-danger-subtle text-danger-emphasis">删除</span></td>
                                <td>权限管理</td>
                                <td>删除权限: USER_DELETE</td>
                                <td>*************</td>
                                <td><small>2023-09-20 14:15:42</small></td>
                                <td><span class="badge bg-danger-subtle text-danger-emphasis">失败</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#viewLogModal4" title="查看详情">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            
                            <!-- 日志记录5 -->
                            <tr>
                                <td class="ps-3">5</td>
                                <td>user456</td>
                                <td><span class="badge bg-secondary-subtle text-secondary-emphasis">查询</span></td>
                                <td>用户管理</td>
                                <td>查询用户列表</td>
                                <td>192.168.1.102</td>
                                <td><small>2023-09-20 14:10:11</small></td>
                                <td><span class="badge bg-success-subtle text-success-emphasis">成功</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#viewLogModal5" title="查看详情">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <div class="text-muted small">
                    显示 1 至 5，共 258 条记录
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
    
    <!-- 查看日志详情Modal (Modal 1 as example) -->
    <div class="modal fade" id="viewLogModal1" tabindex="-1" aria-labelledby="viewLogModalLabel1" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewLogModalLabel1">操作日志详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>操作人:</strong> admin</p>
                            <p><strong>操作类型:</strong> <span class="badge bg-success-subtle text-success-emphasis">新增</span></p>
                            <p><strong>操作模块:</strong> 用户管理</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>IP地址:</strong> *************</p>
                            <p><strong>操作状态:</strong> <span class="badge bg-success-subtle text-success-emphasis">成功</span></p>
                            <p><strong>操作时间:</strong> 2023-09-20 14:30:25</p>
                        </div>
                    </div>
                    <div class="mb-3">
                        <h6>操作内容</h6>
                        <p class="text-muted">新增用户: user123，分配角色: 普通用户</p>
                    </div>
                    <div class="mb-3">
                        <h6>请求参数</h6>
                        <div class="log-details">
                            {
                              "username": "user123",
                              "nickname": "测试用户",
                              "email": "<EMAIL>",
                              "phone": "13800138000",
                              "roleIds": [2]
                            }
                        </div>
                    </div>
                    <div class="mb-3">
                        <h6>响应结果 / 错误信息</h6>
                        <div class="log-details">
                            {
                              "code": 200,
                              "message": "用户创建成功",
                              "data": {
                                "userId": 10,
                                "username": "user123",
                                "nickname": "测试用户",
                                "createTime": "2023-09-20 14:30:25"
                              }
                            }
                        </div>
                    </div>
                    <div class="mb-3">
                        <h6>执行时间</h6>
                        <p class="text-muted">15 ms</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Add other log modals (viewLogModal2, viewLogModal3, etc.) similar to the above -->
    
    <!-- JavaScript -->
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <!-- <script src="/wkg/js/features/permission/permission.js"></script> -->
</body>
</html> 