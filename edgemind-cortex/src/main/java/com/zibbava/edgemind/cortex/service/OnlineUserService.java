package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.dto.OnlineUser;

import java.util.List;
import java.util.Map;

public interface OnlineUserService {

    /**
     * 获取在线用户列表
     * @return 在线用户列表
     */
    List<OnlineUser> getOnlineUsers();

    /**
     * 根据条件查询在线用户
     * @param queryParams 查询参数
     * @return 在线用户列表
     */
    List<OnlineUser> getOnlineUsers(Map<String, Object> queryParams);

    /**
     * 强制下线用户
     * @param userId 用户ID
     */
    void kickoutUser(Long userId);

    /**
     * 批量强制下线用户
     * @param userIds 用户ID列表
     */
    void batchKickoutUsers(List<Long> userIds);

    /**
     * 根据Token强制下线
     * @param tokenValue Token值
     */
    void kickoutByToken(String tokenValue);

    /**
     * 获取在线用户统计信息
     * @return 统计信息
     */
    Map<String, Object> getOnlineUserStatistics();

    /**
     * 获取用户在线详情
     * @param userId 用户ID
     * @return 在线用户信息
     */
    OnlineUser getOnlineUserDetail(Long userId);

    /**
     * 检查用户是否在线
     * @param userId 用户ID
     * @return 是否在线
     */
    boolean isUserOnline(Long userId);

    /**
     * 获取在线用户数量
     * @return 在线用户数量
     */
    long getOnlineUserCount();

    /**
     * 获取用户登录历史
     * @param userId 用户ID
     * @param limit 返回数量限制
     * @return 登录历史
     */
    List<Map<String, Object>> getUserLoginHistory(Long userId, int limit);

    /**
     * 清理过期的在线用户信息
     */
    void cleanupExpiredOnlineUsers();

    /**
     * 更新用户最后活动时间
     * @param userId 用户ID
     */
    void updateLastActiveTime(Long userId);

    /**
     * 获取用户会话信息
     * @param userId 用户ID
     * @return 会话信息列表
     */
    List<Map<String, Object>> getUserSessions(Long userId);
}
