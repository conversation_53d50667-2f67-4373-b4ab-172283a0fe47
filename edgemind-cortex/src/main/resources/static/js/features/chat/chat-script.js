// chat-script.js (主入口文件 - 运行在 iframe 内)
import { 
    initialize as initializeMessageHandler, 
    sendMessage, 
    terminateConversation,
    clearImagePreview
} from './message-handler.js';
import { initialize as initializeModelSelector } from './model-selector-component.js';
import { initialize as initializeChatHistory } from './chat-history.js';

document.addEventListener('DOMContentLoaded', () => {
    

    try {
        // 1. 获取 iframe 内部需要的 DOM 元素
        const elements = {
            chatMessages: document.getElementById('chat-messages'),
            userInput: document.getElementById('user-input'),
            sendButton: document.getElementById('send-button'),
            modelSelect: document.getElementById('model-select'),
            uploadButton: document.getElementById('upload-button'),
            imageUpload: document.getElementById('image-upload'),
            imagePreviewContainer: document.getElementById('image-preview-container'),
            // 添加聊天历史相关元素
            conversationsList: document.getElementById('conversations-list'),
            newChatButton: document.getElementById('new-chat-button'),
            // 添加终止对话按钮
            pauseButton: document.getElementById('pause-button')
        };

        // 基础验证 (只检查 iframe 内部的核心元素)
        if (!elements.chatMessages || !elements.userInput || !elements.sendButton || !elements.modelSelect) {
            console.error("Chat Script (iframe): Missing core chat elements (#chat-messages, #user-input, #send-button, #model-select). Cannot initialize chat interface.");
            // 在 iframe 内部显示错误
            document.body.innerHTML = '<p style="color: red; padding: 20px;">Chat interface failed to load: Missing required page elements inside iframe.</p>';
            return;
        }

        // 2. 初始化消息处理器模块 (它也运行在 iframe 内)
        try {
            if (!initializeMessageHandler(elements)) {
                console.error("Chat Script (iframe): Message Handler initialization failed.");
                return;
            }
    
        } catch (e) {
            console.error("Chat Script (iframe): Error initializing messageHandler:", e);
            return;
        }

        // 3. 初始化模型选择器 (如果它也在此 iframe 内)
        if (elements.modelSelect) {
             try {
                initializeModelSelector('model-select');
        
            } catch (e) {
                 console.warn("Chat Script (iframe): Error initializing ModelSelectorComponent:", e);
            }
        } else {
            console.warn("Chat Script (iframe): modelSelect element missing.");
        }
        
        // 4. 初始化聊天历史组件
        if (elements.conversationsList) {
            try {
                if (!initializeChatHistory(elements)) {
                    console.error("Chat Script (iframe): Chat History initialization failed.");
                } else {
            
                }
            } catch (e) {
                console.error("Chat Script (iframe): Error initializing ChatHistory:", e);
            }
        } else {
            console.warn("Chat Script (iframe): conversationsList element missing.");
        }
        
        // 5. 加载初始会话消息 (根据 URL 参数)
        // 这部分现在由聊天历史组件处理，不需要在这里重复
        
        // 6. 绑定核心事件监听器 (仅限 iframe 内元素)
    
        
        // 发送消息的包装函数 - 确保消息处理器导出的sendMessage函数正确执行
        const handleSendMessage = () => {
    
            try {
                if (typeof sendMessage === 'function') {
                    sendMessage();
                } else {
                    console.error("Chat Script: sendMessage 不是函数！", sendMessage);
                    alert("发送功能暂时不可用，请刷新页面重试");
                }
            } catch (e) {
                console.error("Chat Script: 发送消息时出错:", e);
                alert("发送消息时出错: " + e.message);
            }
        };
        
        // 发送按钮点击
        elements.sendButton.addEventListener('click', handleSendMessage);
    

        // 输入框回车 (Shift+Enter 换行)
        elements.userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
    
                e.preventDefault();
                handleSendMessage();
            }
        });
    

        // 输入框内容变化 (调整高度, 启用/禁用发送按钮)
        elements.userInput.addEventListener('input', () => {
            elements.userInput.style.height = 'auto';
            elements.userInput.style.height = `${elements.userInput.scrollHeight}px`;
            const hasText = elements.userInput.value.trim() !== '';
            const hasImage = elements.imagePreviewContainer.querySelector('.image-preview') !== null;
            elements.sendButton.disabled = !hasText && !hasImage;
        });

        // 图片上传按钮点击
        if (elements.uploadButton && elements.imageUpload) {
            elements.uploadButton.addEventListener('click', () => elements.imageUpload.click());
        }

        // 图片文件选择变化
        if (elements.imageUpload) {
            elements.imageUpload.addEventListener('change', (event) => {
                try {
                    handleImageUpload(event);
                    const hasText = elements.userInput.value.trim() !== '';
                    const hasImage = elements.imagePreviewContainer.querySelector('.image-preview') !== null;
                    elements.sendButton.disabled = !hasText && !hasImage;
                } catch (e) {
                    console.error("Error handling image upload:", e);
                }
            });
        }

        // 终止按钮点击事件
        if (elements.pauseButton) {
            elements.pauseButton.addEventListener('click', () => {
                try {
                    if (terminateConversation()) {
            
                    }
                } catch (error) {
                    console.error("终止对话时出错:", error);
                }
            });
        }

        // 7. 初始状态设置
        elements.sendButton.disabled = true; // 初始禁用

        
    } catch (error) {
        console.error("Critical error during Chat Script initialization:", error);
    }
});