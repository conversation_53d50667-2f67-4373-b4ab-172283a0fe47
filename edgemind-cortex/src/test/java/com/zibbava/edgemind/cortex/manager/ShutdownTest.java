package com.zibbava.edgemind.cortex.manager;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 应用程序关闭测试
 */
@SpringBootTest
public class ShutdownTest {
    
    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }
    
    @Test
    void testShutdownHookRegistration() {
        // 测试是否正确注册了shutdown hook
        // 这里主要验证代码编译通过
        assert true : "Shutdown hook should be registered in main method";
    }
    
    @Test
    void testDockerStopMethod() {
        // 测试DockerManager.stopDocker()方法是否存在
        try {
            // 只验证方法存在，不实际执行
            DockerManager.class.getMethod("stopDocker");
            assert true : "DockerManager.stopDocker() method exists";
        } catch (NoSuchMethodException e) {
            assert false : "DockerManager.stopDocker() method not found";
        }
    }
    
    @Test
    void testOllamaStopMethod() {
        // 测试OllamaManager.stopOllama()方法是否存在
        try {
            // 只验证方法存在，不实际执行
            OllamaManager.class.getMethod("stopOllama");
            assert true : "OllamaManager.stopOllama() method exists";
        } catch (NoSuchMethodException e) {
            assert false : "OllamaManager.stopOllama() method not found";
        }
    }
} 