package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.zibbava.edgemind.cortex.entity.ChatConversation;
import com.zibbava.edgemind.cortex.entity.ChatMessage;
import com.zibbava.edgemind.cortex.service.ChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 聊天控制器，处理与聊天相关的API请求
 * todo 需要传上下文,传用户id到聊天中
 */
@RestController
@RequestMapping("/api/conversations")
@RequiredArgsConstructor
@Slf4j
public class ChatController {

    @Autowired
    private ChatService chatService;

    /**
     * 获取当前用户的所有对话（支持分页）
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getUserConversations(
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "size", defaultValue = "20") Integer size) {

        Long userId = StpUtil.getLoginIdAsLong();

        // 获取会话总数
        int totalCount = chatService.getUserConversationsCount(userId);

        // 计算总页数
        int totalPages = (int) Math.ceil((double) totalCount / size);

        // 获取分页会话列表
        List<ChatConversation> conversations = chatService.getUserConversationsPaged(userId, page, size);

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("content", conversations);
        result.put("totalElements", totalCount);
        result.put("totalPages", totalPages);
        result.put("size", size);
        result.put("page", page);

        return ResponseEntity.ok(result);
    }

    /**
     * 创建新对话
     */
    @PostMapping
    public ResponseEntity<ChatConversation> createConversation(@RequestBody Map<String, Object> params) {
        Long userId = StpUtil.getLoginIdAsLong();
        String title = (String) params.getOrDefault("title", "新对话");

        // 验证标题长度
        if (title.length() > 10) {
            log.warn("用户 {} 尝试创建超长标题的会话: {}", userId, title);
            return ResponseEntity.badRequest().build();
        }

        ChatConversation conversation = chatService.createConversation(userId, title);
        return ResponseEntity.ok(conversation);
    }

    /**
     * 获取指定对话的消息（支持分页）
     */
    @GetMapping("/{conversationId}/messages")
    public ResponseEntity<Map<String, Object>> getConversationMessages(
            @PathVariable Long conversationId,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "size", defaultValue = "20") Integer size) {

        // 获取当前登录用户ID
        Long userId = StpUtil.getLoginIdAsLong();

        // 验证该会话是否属于当前用户
        ChatConversation conversation = chatService.getConversation(conversationId);
        if (conversation == null || !conversation.getUserId().equals(userId)) {
            log.warn("用户 {} 尝试访问不属于他的会话 {}", userId, conversationId);
            return ResponseEntity.status(403).build(); // 返回禁止访问
        }

        // 获取会话消息总数
        int totalCount = chatService.getConversationMessagesCount(conversationId);

        // 计算总页数
        int totalPages = (int) Math.ceil((double) totalCount / size);

        // 获取会话消息，添加分页支持
        List<ChatMessage> messages = chatService.getConversationMessagesPaged(conversationId, page, size);

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("content", messages);
        result.put("totalElements", totalCount);
        result.put("totalPages", totalPages);
        result.put("size", size);
        result.put("page", page);

        return ResponseEntity.ok(result);
    }

    /**
     * 更新对话标题
     */
    @PutMapping("/{conversationId}/title")
    public ResponseEntity<Map<String, Object>> updateConversationTitle(
            @PathVariable Long conversationId,
            @RequestBody Map<String, String> params) {

        // 获取当前登录用户ID
        Long userId = StpUtil.getLoginIdAsLong();

        // 验证该会话是否属于当前用户
        ChatConversation conversation = chatService.getConversation(conversationId);
        if (conversation == null || !conversation.getUserId().equals(userId)) {
            log.warn("用户 {} 尝试更新不属于他的会话 {}", userId, conversationId);
            return ResponseEntity.status(403).build(); // 返回禁止访问
        }

        String title = params.get("title");

        // 验证标题长度
        if (title == null || title.isEmpty()) {
            log.warn("用户 {} 尝试将会话 {} 标题设置为空", userId, conversationId);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "标题不能为空");
            return ResponseEntity.badRequest().body(errorResponse);
        }

        if (title.length() > 20) {
            log.warn("用户 {} 尝试将会话 {} 标题设置为超长标题: {}", userId, conversationId, title);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "标题长度不能超过20个字符");
            return ResponseEntity.badRequest().body(errorResponse);
        }

        boolean success = chatService.updateConversationTitle(conversationId, title);

        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        return ResponseEntity.ok(response);
    }

    /**
     * 删除对话
     */
    @DeleteMapping("/{conversationId}")
    public ResponseEntity<Map<String, Object>> deleteConversation(@PathVariable Long conversationId) {
        // 获取当前登录用户ID
        Long userId = StpUtil.getLoginIdAsLong();

        // 验证该会话是否属于当前用户
        ChatConversation conversation = chatService.getConversation(conversationId);
        if (conversation == null || !conversation.getUserId().equals(userId)) {
            log.warn("用户 {} 尝试删除不属于他的会话 {}", userId, conversationId);
            return ResponseEntity.status(403).build(); // 返回禁止访问
        }

        boolean success = chatService.deleteConversation(conversationId);

        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        return ResponseEntity.ok(response);
    }

    /**
     * 保存用户消息和AI回复到数据库
     * 注意：这个方法不直接调用AI服务，只用于保存已经生成的消息
     */
    @PostMapping("/{conversationId}/messages")
    public ResponseEntity<Map<String, Object>> saveMessages(
            @PathVariable Long conversationId,
            @RequestBody Map<String, Object> params) {

        // 获取当前登录用户ID
        Long userId = StpUtil.getLoginIdAsLong();

        // 验证该会话是否属于当前用户
        ChatConversation conversation = chatService.getConversation(conversationId);
        if (conversation == null || !conversation.getUserId().equals(userId)) {
            log.warn("用户 {} 尝试在不属于他的会话 {} 中保存消息", userId, conversationId);
            return ResponseEntity.status(403).build(); // 返回禁止访问
        }

        String userContent = (String) params.get("userContent");
        String imagePath = (String) params.get("imagePath");
        String aiContent = (String) params.get("aiContent");
        String thinkingContent = (String) params.get("thinkingContent");
        Integer tokenCount = (Integer) params.getOrDefault("tokenCount", 0);
        String modelName = (String) params.getOrDefault("modelName", "");

        // 保存用户消息
        ChatMessage userMessage = chatService.saveUserMessage(conversationId, userContent, imagePath);

        // 保存AI消息
        ChatMessage aiMessage = chatService.saveAiMessage(conversationId, aiContent, thinkingContent, tokenCount, modelName);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("userMessageId", userMessage.getId());
        response.put("aiMessageId", aiMessage.getId());

        return ResponseEntity.ok(response);
    }

    /**
     * 获取当前用户的所有对话，如果不存在则创建一个新会话
     * 这个接口用于页面初始化时调用
     */
    @GetMapping("/init")
    public ResponseEntity<Map<String, Object>> initializeConversations(
            @RequestParam(value = "limit", defaultValue = "30") Integer limit) {
        Long userId = null;
        try {
            if (StpUtil.isLogin()) {
                userId = StpUtil.getLoginIdAsLong();
            }
        } catch (Exception e) {
            log.warn("[Chat] 获取用户ID失败: {}", e.getMessage());
        }

        Map<String, Object> response = new HashMap<>();

        if (userId != null) {
            // 获取用户的最近N个会话，传入limit参数
            List<ChatConversation> conversations = chatService.getRecentUserConversations(userId, limit);
            response.put("conversations", conversations);

            // 如果没有会话，自动创建一个新会话
            if (conversations == null || conversations.isEmpty()) {
                ChatConversation newConversation = chatService.createConversation(userId, "新会话");
                response.put("currentConversationId", newConversation.getId());
                response.put("messages", new ArrayList<>());
                log.info("[Chat] 用户{}没有会话，自动创建新会话: {}", userId, newConversation.getId());
            } else {
                // 返回最新的会话ID作为当前会话
                Long currentConversationId = conversations.get(0).getId();
                response.put("currentConversationId", currentConversationId);

                // 获取当前会话的最近N条消息，传入limit参数
                List<ChatMessage> messages = chatService.getRecentConversationMessages(currentConversationId, limit);
                response.put("messages", messages);
                log.info("[Chat] 加载会话{}的历史消息: {} 条", currentConversationId, messages == null ? 0 : messages.size());
            }
        } else {
            // 未登录用户，返回空数据
            response.put("conversations", new ArrayList<>());
            response.put("currentConversationId", null);
            response.put("messages", new ArrayList<>());
        }

        return ResponseEntity.ok(response);
    }


} 