package com.zibbava.edgemind.cortex.assistant.tools;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Configuration
class ToolsBean {

    @Autowired
    private ApplicationContext applicationContext;

    @Bean
    public List<Object> tools() {
        // 获取所有实现了 Tools 接口的 Bean
        Map<String, Tools> toolsMap = applicationContext.getBeansOfType(Tools.class);

        // 将所有工具类实例添加到列表中
        List<Object> toolsList = new ArrayList<>(toolsMap.values());

        // 打印日志，显示找到了哪些工具类
//        System.out.println("找到 " + toolsList.size() + " 个工具类实现:");
//        toolsMap.forEach((name, tool) -> {
//            System.out.println("  - " + name + ": " + tool.getClass().getName());
//        });

        return toolsList;
    }
}
