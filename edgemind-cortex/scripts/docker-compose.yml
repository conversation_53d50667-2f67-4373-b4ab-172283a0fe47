version: '3.8'

services:
  mysql:
    image: mysql:8.0 # 指定 MySQL 8.0 版本
    container_name: mysql # 给容器一个友好的名字
    restart: unless-stopped # 容器退出时自动重启,除非手动停止
    environment:
      MYSQL_ROOT_PASSWORD: wkg123456 # 设置 root 用户的密码
      MYSQL_DATABASE: wkg # 初始化时创建名为 wkg 的数据库
      # MYSQL_USER: your_custom_user # 如果需要额外用户，可以取消注释并设置
      # MYSQL_PASSWORD: your_custom_password # 额外用户的密码
    ports:
      - "3306:3306" # 将容器的 3306 端口映射到主机的 3306 端口
    volumes:
      - "${DATA_PATH}/mysql:/var/lib/mysql"
    command:
      # 确保服务器和客户端连接默认使用 utf8mb4
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_unicode_ci
      - --lower_case_table_names=1

  # Redis 服务
  redis: # 给 Redis 服务一个名字
    image: redis:7.2 # 使用 Redis 7.2 版本 (官方稳定版)
    # 如果您确认 redis:7.4.2 标签存在且可用，可以替换为 redis:7.4.2
    container_name: redis # 给容器一个友好的名字
    restart: unless-stopped
    ports:
      - "6379:6379" # 将主机的 6379 端口映射到容器的 6379 端口
    volumes:
      - "${DATA_PATH}/redis:/data"

  # OnlyOffice Document Server 服务
  onlyoffice: # 服务名称，可以自定义
    image: onlyoffice/documentserver:8.3.3 # 指定版本
    container_name: onlyoffice # 容器名称，与 docker run --name onlyoffice 对应
    restart: unless-stopped # 对应 --restart=always
    ports:
      - "8055:80" # 对应 -p 8055:80
    environment:
      TZ: "Asia/Shanghai" # 对应 -e TZ="Asia/Shanghai"
      ALLOW_PRIVATE_IP: 'true' # 对应 -e ALLOW_PRIVATE_IP='true'
      JWT_ENABLED: 'true' # 明确启用JWT（通常如果设置了JWT_SECRET就会启用，但显式更好）
      JWT_SECRET: wkg123456 # 对应 -e JWT_SECRET=llqzer3321
      # JWT_HEADER: "Authorization" # 默认是 Authorization，如果需要自定义 JWT 请求头中的 Bearer 前缀，可以使用这个
      # JWT_IN_BODY: "false" # 默认是 false, 如果 JWT 在请求体中
    volumes:
      # OnlyOffice Document Server 的数据持久化目录
      # 参考官方文档，主要有以下几个：
      - "${DATA_PATH}/onlyoffice/logs:/var/log/onlyoffice"   # 日志
      - "${DATA_PATH}/onlyoffice/data:/var/lib/onlyoffice"  # 运行数据、缓存等
      - "${DATA_PATH}/onlyoffice/fonts:/usr/share/fonts"    # 自定义字体 (可选)
      # - "${DATA_PATH}/onlyoffice/forgotten:/var/www/onlyoffice/Data/forgotten" # 如果使用 "forgotten files" 功能
    # stdin_open: true # 对应 docker run -i
    # tty: true        # 对应 docker run -t (对于后台服务通常不是必须的，但有些镜像可能需要)

  weaviate:
    command:
      - --host
      - 0.0.0.0
      - --port
      - '8080'
      - --scheme
      - http
    image: cr.weaviate.io/semitechnologies/weaviate:1.30.4
    ports:
      - 8090:8080
      - 50051:50051
    volumes:
      - "${DATA_PATH}/weaviate:/var/lib/weaviate"
    restart: on-failure:0
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      ENABLE_API_BASED_MODULES: 'true'
      CLUSTER_HOSTNAME: 'node1'
      ENABLE_TOKENIZER_GSE: 'true'
volumes:
  weaviate_data:


networks:
  default:
    name: milvus