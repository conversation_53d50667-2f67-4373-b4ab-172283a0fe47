// auth-service.js - 提供全局用户登录状态缓存

// 登录状态缓存
let userLoginPromise = null;
let currentUserData = null;
let isGuestMode = true;
let isRequestInProgress = false;
let requestCount = 0;

// 获取上下文路径
const CONTEXT_PATH = window.location.pathname.startsWith('/wkg') ? '/wkg' : '';

// 检查用户登录状态（带缓存）
async function checkLoginStatus() {
    requestCount++;
    const currentRequest = requestCount;

    
    // 如果已经有缓存的Promise，直接返回
    if (userLoginPromise) {

        return userLoginPromise;
    }
    
    // 如果已经有请求在进行中，等待一小段时间后再次尝试
    if (isRequestInProgress) {

        await new Promise(resolve => setTimeout(resolve, 10)); // 短暂等待
        return checkLoginStatus(); // 递归调用自己，此时可能已有结果缓存
    }
    

    isRequestInProgress = true;
    
    // 创建一个新的Promise并缓存它
    userLoginPromise = new Promise(async (resolve) => {
        try {

            const response = await fetch(`${CONTEXT_PATH}/auth/check-login`);
            
            if (!response.ok) {
                isGuestMode = true;
                currentUserData = null;

                resolve({ isLoggedIn: false, userData: null });
                return;
            }
            
            const result = await response.json();
            const isLoggedIn = result.code === 200 && result.data?.isLogin;
            
            isGuestMode = !isLoggedIn;
            currentUserData = isLoggedIn ? result.data : null;
            
            if (isLoggedIn) {

            } else {

            }
            
            resolve({ isLoggedIn, userData: result.data });
            
        } catch (error) {
            console.error("[AuthService] 检查登录状态失败:", error);
            isGuestMode = true;
            currentUserData = null;
            resolve({ isLoggedIn: false, userData: null });
        } finally {
            isRequestInProgress = false;
        }
    });
    
    return userLoginPromise;
}

// 清除登录状态缓存（在登录/登出时调用）
function clearLoginStatusCache() {
    userLoginPromise = null;
    currentUserData = null;
    isRequestInProgress = false;

}

// 判断是否为游客模式
function isGuest() {
    return isGuestMode;
}

// 获取当前用户数据
function getCurrentUser() {
    return currentUserData;
}

// 导出函数
export {
    checkLoginStatus,
    clearLoginStatusCache,
    isGuest,
    getCurrentUser
};