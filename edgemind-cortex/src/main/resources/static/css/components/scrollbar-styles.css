/* 代码块及通用滚动条美化 - 轻盈科技风 */

/* 适用于 pre, textarea, 和一般可滚动区域 */
pre, textarea, .custom-scrollbar {
    overflow: auto !important;
}

pre code {
    white-space: pre !important;
}

/* Webkit (Chrome, Safari, Edge) */
::-webkit-scrollbar {
    width: 7px;
    height: 7px;
}

::-webkit-scrollbar-track {
    background: var(--scrollbar-track, #ffffff); /* Use CSS variable, fallback */
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb, #f8f9fa); /* Use CSS variable, fallback */
    border-radius: 10px;
    border: 2px solid var(--scrollbar-track, #ffffff); /* Creates padding effect */
    background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover, #e9ecef); /* Darker on hover */
}

/* Firefox */
* { /* Apply to all elements for consistency, can be more specific */
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb, #f8f9fa) var(--scrollbar-track, #ffffff); /* thumb track */
}