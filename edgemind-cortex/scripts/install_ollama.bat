@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo Ollama Installation Script
echo ========================================
echo.

REM Get current script directory
set CURRENT_DIR=%~dp0
set CURRENT_DIR=%CURRENT_DIR:~0,-1%

echo Current directory: %CURRENT_DIR%
echo.

REM System requirement checks skipped per user request
echo.

REM Check if OllamaSetup.exe exists
set OLLAMA_INSTALLER=%CURRENT_DIR%\OllamaSetup.exe
if not exist "%OLLAMA_INSTALLER%" (
    echo ERROR: OllamaSetup.exe not found in current directory
    echo Please make sure OllamaSetup.exe is in the same folder as this script
    pause
    exit /b 1
)

echo Found Ollama installer: %OLLAMA_INSTALLER%
echo.

REM Check if Ollama is already installed
echo Checking if Ollama is already installed...
ollama --version >nul 2>&1
if %errorLevel% equ 0 (
    echo Ollama is already installed!
    ollama --version
    echo.
    echo Ollama is ready to use.
    echo No additional configuration needed.
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 0
)

echo Ollama not detected, proceeding with installation...
echo.

REM Check admin rights
echo Checking admin rights...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Need admin rights
    echo Please right-click and "Run as admin"
    pause
    exit /b 1
)

echo OK: Admin rights confirmed
echo.

REM Set Ollama installation and model paths to current directory
set OLLAMA_INSTALL_PATH=%CURRENT_DIR%\Ollama
set OLLAMA_MODELS_PATH=%CURRENT_DIR%\ollama-models

echo Ollama will be configured with:
echo - Installation path: %OLLAMA_INSTALL_PATH%
echo - Models path: %OLLAMA_MODELS_PATH%
echo.

REM Create installation and models directories
echo Creating Ollama directories...
if not exist "%OLLAMA_INSTALL_PATH%" mkdir "%OLLAMA_INSTALL_PATH%"
if not exist "%OLLAMA_MODELS_PATH%" mkdir "%OLLAMA_MODELS_PATH%"

REM Set environment variable for Ollama models path
echo Setting OLLAMA_MODELS environment variable...
REM Try user environment variable first (recommended)
setx OLLAMA_MODELS "%OLLAMA_MODELS_PATH%" >nul 2>&1
if %errorLevel% equ 0 (
    echo SUCCESS: User environment variable OLLAMA_MODELS set
) else (
    echo WARNING: Failed to set user environment variable
    echo Trying system environment variable...
    setx OLLAMA_MODELS "%OLLAMA_MODELS_PATH%" /M >nul 2>&1
    if !errorLevel! equ 0 (
        echo SUCCESS: System environment variable OLLAMA_MODELS set
    ) else (
        echo ERROR: Failed to set environment variable
        echo Please set OLLAMA_MODELS manually in Windows Settings
    )
)
echo.

REM Install Ollama to specified directory
echo Installing Ollama to local directory...
echo This may take a few minutes...
echo.

REM Change to current script directory and run installer with custom path
echo Running installer from current directory...
pushd "%CURRENT_DIR%"
"%OLLAMA_INSTALLER%" /DIR="%OLLAMA_INSTALL_PATH%"
set INSTALL_RESULT=%errorLevel%
popd

if %INSTALL_RESULT% equ 0 (
    echo Ollama installation completed successfully!
) else (
    echo ERROR: Ollama installation failed
    echo Error code: %INSTALL_RESULT%
    pause
    exit /b 1
)
echo.

REM Wait for installation to complete
echo Waiting for installation to complete...
timeout /t 10 /nobreak >nul

REM Verify installation
echo Verifying Ollama installation...
ollama --version >nul 2>&1
if %errorLevel% equ 0 (
    echo Ollama installed successfully!
    ollama --version
) else (
    echo WARNING: Ollama command not found
    echo Installation may need a system restart
)
echo.

goto :post_install



:post_install
REM Post-installation configuration and direct startup
echo Post-installation configuration...
echo.

echo ========================================
echo Ollama Installation Completed!
echo ========================================
echo.
echo CONFIGURATION SUMMARY:
echo - Installation path: %OLLAMA_INSTALL_PATH%
echo - Models path: %OLLAMA_MODELS_PATH%
echo - OLLAMA_MODELS environment variable has been set
echo.
echo Starting Ollama service automatically...
echo.

REM Set environment variable for current session
set OLLAMA_MODELS=%OLLAMA_MODELS_PATH%

echo Configuring Ollama to NOT start automatically on boot...
echo.

REM Disable Ollama auto-start by removing from startup if exists
reg delete "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run" /v "Ollama" /f >nul 2>&1
reg delete "HKEY_LOCAL_MACHINE\Software\Microsoft\Windows\CurrentVersion\Run" /v "Ollama" /f >nul 2>&1

echo Ollama auto-start disabled successfully.
echo.

echo Restarting Ollama service to apply environment variable changes...
echo.

REM Stop any existing Ollama processes
taskkill /f /im ollama.exe >nul 2>&1
echo Ollama service stopped

REM Wait a moment for cleanup
timeout /t 3 /nobreak >nul

REM Start Ollama service with new environment variable
echo Starting Ollama service with new models path...
start "Ollama Service" cmd /k "echo Ollama service running with models path: %OLLAMA_MODELS_PATH% && ollama serve"

echo.
echo INSTALLATION COMPLETED!
echo.
echo CONFIGURATION SUMMARY:
echo - Ollama installed successfully
echo - Models path: %OLLAMA_MODELS_PATH%
echo - Auto-start on boot: DISABLED
echo - Service started for current session
echo - Environment variable OLLAMA_MODELS configured
echo.
echo IMPORTANT: Ollama service has been automatically restarted
echo to apply the new environment variable settings.
echo.
echo To verify the configuration, run:
echo   ollama env
echo.
echo Expected output should show:
echo   OLLAMA_MODELS=%OLLAMA_MODELS_PATH%
echo.
echo To download recommended models, run:
echo   ollama pull qwen2.5:7b
echo   ollama pull nomic-embed-text
echo.
echo To test the installation, run:
echo   ollama run qwen2.5:7b
echo.
echo Press any key to exit this installer...
pause >nul