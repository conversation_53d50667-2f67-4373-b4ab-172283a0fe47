/**
 * 知识库聊天功能初始化
 * 与普通聊天使用同一套底层代码，但针对知识库场景进行特定配置
 */
// 知识库聊天模块 (ES Module)


// 导入消息处理模块 - 使用绝对路径确保正确加载
import { initialize as initializeMessageHandler, sendMessage, terminateConversation } from '/wkg/js/features/chat/message-handler.js';
import { initialize as initializeModelSelector } from '../chat/model-selector-component.js';

// 状态变量与DOM元素引用
let chatMessages = null;
let chatInput = null;
let sendButton = null;
let modelSelect = null;
let uploadButton = null;
let imageUpload = null;
let imagePreview = null;
let pauseButton = null;
let currentNodeId = null; // 当前选中的知识库节点ID
let currentKnowledgeNodeId = null; // 当前选中的知识库节点ID
let isInitialized = false; // 初始化状态标志



/**
 * 初始化知识库聊天组件
 */
export function initializeKnowledgeChat(options) {
    if (isInitialized) {
        return;
    }



    // 获取DOM元素
    chatMessages = document.getElementById('kb-chat-messages');
    chatInput = document.getElementById('chat-input');
    sendButton = document.getElementById('send-button');
    modelSelect = document.getElementById('model-select');
    uploadButton = document.getElementById('upload-button');
    imageUpload = document.getElementById('image-upload');
    imagePreview = document.getElementById('image-preview');
    pauseButton = document.getElementById('pause-button');



    // 验证必要的DOM元素是否存在
    if (!chatMessages || !chatInput || !sendButton || !modelSelect) {
        console.error("[知识库聊天] 初始化失败: 关键DOM元素未找到 (重构后检查)", {
            chatMessages: !!chatMessages,
            chatInput: !!chatInput,
            sendButton: !!sendButton,
            modelSelect: !!modelSelect,
            uploadButton: !!uploadButton,
            imageUpload: !!imageUpload,
            imagePreview: !!imagePreview,
            pauseButton: !!pauseButton
        });

        // 由于DOM加载问题，延迟500ms再次尝试初始化
        // 注意：如果组件化后加载方式改变，这个延迟可能不再有效或需要调整
        console.warn("[知识库聊天] 延迟500ms后重试初始化...");
        setTimeout(initializeKnowledgeChat, 500);
        return;
    }

    // 初始状态设置为禁用
    chatInput.disabled = true;
    sendButton.disabled = true;

    // 确保没有内联样式影响输入框状态
    chatInput.style.backgroundColor = '';
    chatInput.style.opacity = '1';

    // 通过闭包捕获文档节点点击事件
    document.addEventListener('click', function(event) {
        // 检查是否点击了文档节点（通过查找具有data-node-id属性的元素）
        let node = event.target.closest('[data-node-id]');
        if (node) {
            const nodeId = node.getAttribute('data-node-id');
            const nodeName = node.innerText || node.textContent || "选中节点";
            const nodeType = node.getAttribute('data-node-type'); // Get node type

            // 更新当前节点ID并启用聊天
            if (nodeId !== currentKnowledgeNodeId) {
                updateSelectedNode(nodeId, nodeName, nodeType);
            }
        }
    });

    // 监听DOM事件，支持通过事件机制获取所选节点
    document.addEventListener('document-node-selected', function(event) {
        if (event.detail && event.detail.id) {
            if (event.detail.id !== currentKnowledgeNodeId) {
                updateSelectedNode(event.detail.id, event.detail.name || "选中节点", event.detail.type);
            }
        }
    });

    // 监听关闭聊天侧边栏按钮点击
    const closeButton = document.getElementById('closeChatSidebarCenterButton');
    if (closeButton) {
        closeButton.addEventListener('click', closeChatSidebar);
    }

    // 绑定发送消息事件
    if (sendButton) {
        sendButton.addEventListener('click', function() {
            // Check if a valid node is selected before sending
            if (!currentKnowledgeNodeId) {
                console.warn("[知识库聊天] 发送失败：未选择有效的知识库节点。", `Current ID: ${currentKnowledgeNodeId}`);
                displaySystemMessage("请先选择一个文档或节点再发送消息。"); // Use helper function
                return;
            }
            if (chatInput.value.trim() || document.querySelector('.image-preview')) {
                // Pass context: type and knowledgeNodeId
                sendMessage({ type: 'knowledge', knowledgeNodeId: currentKnowledgeNodeId });
            }
        });
    }

    // 绑定输入框回车发送
    if (chatInput) {
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                // Check if a valid node is selected before sending
                if (!currentKnowledgeNodeId) {
                    console.warn("[知识库聊天] 发送失败：未选择有效的知识库节点。", `Current ID: ${currentKnowledgeNodeId}`);
                    displaySystemMessage("请先选择一个文档或节点再发送消息。"); // Use helper function
                    return;
                }
                if (chatInput.value.trim() || document.querySelector('.image-preview')) {
                    // Pass context: type and knowledgeNodeId
                    sendMessage({ type: 'knowledge', knowledgeNodeId: currentKnowledgeNodeId });
                }
            }
        });
    }

    // 绑定暂停按钮
    if (pauseButton) {
        pauseButton.addEventListener('click', function() {
            terminateConversation(); // 使用message-handler.js中的函数
        });
    }

    // 初始化聊天组件核心功能
    const elements = {
        chatMessages,
        userInput: chatInput,
        sendButton,
        modelSelect,
        uploadButton,
        imageUpload,
        imagePreviewContainer: imagePreview,
        pauseButton
    };

    // 初始化消息处理器和模型选择器
    try {


        // 检查message-handler.js所需的必要元素
        if (!elements.chatMessages || !elements.userInput || !elements.sendButton ||
            !elements.modelSelect || !elements.uploadButton || !elements.imageUpload ||
            !elements.imagePreviewContainer) {
            console.error("[知识库聊天] 初始化失败: 消息处理器需要的DOM元素不完整");
            return;
        }

        // 初始化消息处理器
        initializeMessageHandler(elements);

        // 初始化模型选择器
        if (modelSelect) {
            try {
                initializeModelSelector('model-select');
            } catch (error) {
                console.error('[知识库聊天] 初始化模型选择器失败:', error);
            }
        }

        // 初始化增强检索按钮
        const enhancedRetrievalButtonId = 'kb-chat-enhanced-retrieval';
        setTimeout(() => {
            const enhancedRetrievalButton = document.getElementById(enhancedRetrievalButtonId);
            if (enhancedRetrievalButton && window.enhancedRetrievalManager) {
                window.enhancedRetrievalManager.register(enhancedRetrievalButtonId);
            } else if (window.initializeEnhancedRetrievalButtons) {
                // 如果管理器还未准备好，尝试手动初始化
                window.initializeEnhancedRetrievalButtons();
            }
        }, 100); // 给增强检索模块一些时间完成初始化

        isInitialized = true;


        // 添加调试：查看初始化后的元素状态
        setTimeout(() => {

            // 修复：检查URL参数或已选择的节点
            checkForExistingNode();

            // 设置聊天区域自动选择功能
            setupChatAreaAutoSelect();
        }, 500);
    } catch (error) {
        console.error("[知识库聊天] 初始化消息处理器失败:", error);
    }
}

/**
 * 加载可用的模型列表
 * @deprecated 使用 model-selector-component.js 替代
 */
async function loadAvailableModels() {
    // 已移除自定义实现的模型加载功能
    // 现在使用 model-selector-component.js 实现
}

/**
 * 检查是否已有节点需要显示聊天
 */
function checkForExistingNode() {
    const urlParams = new URLSearchParams(window.location.search);
    const nodeIdFromUrl = urlParams.get('nodeId');

    // 如果URL参数中有节点ID，直接启用聊天
    if (nodeIdFromUrl) {


        // 获取节点名称（可能需要从DOM中查找）
        let nodeName = "从URL参数加载的节点";
        const nodeElement = document.querySelector(`[data-node-id="${nodeIdFromUrl}"]`);
        if (nodeElement) {
            nodeName = nodeElement.innerText || nodeElement.textContent || nodeName;
        }

        // 启用聊天
        setChatInputEnabled(true, nodeIdFromUrl);

        return;
    }

    // 检查是否已有选中的节点
    const selectedNode = document.querySelector('.nav-link-container.active');
    if (selectedNode) {
        const nodeId = selectedNode.getAttribute('data-node-id');
        if (nodeId) {
            const nodeName = selectedNode.innerText || selectedNode.textContent || "选中节点";


            setChatInputEnabled(true, nodeId);

            // 如果用户打开了聊天侧边栏，则尝试自动调整聊天样式
            const pageContent = document.querySelector('.page-content');
            if (pageContent && pageContent.classList.contains('chat-active')) {

                setTimeout(() => {
                    const messages = document.getElementById('kb-chat-messages');
                    if (messages) {
                        messages.style.display = 'block';
                    }
                }, 100);
            }
        }
    } else {

    }
}

/**
 * 设置聊天输入框状态
 * @param {boolean} enabled 是否启用
 * @param {string} nodeId 节点ID
 */
function setChatInputEnabled(enabled, nodeId) {


    // 重新获取DOM元素，确保引用最新
    chatInput = document.getElementById('chat-input');
    sendButton = document.getElementById('send-button');
    uploadButton = document.getElementById('upload-button');
    pauseButton = document.getElementById('pause-button');
    chatMessages = document.getElementById('kb-chat-messages');

    if (!chatInput || !sendButton) {
        console.error('[知识库聊天] 无法设置聊天输入框状态，DOM元素未找到');

        return;
    }

    // 记住当前处理的节点ID
    currentNodeId = nodeId || null;
    currentKnowledgeNodeId = nodeId || null; // 确保两个变量保持同步

    // 强制设置输入框状态
    chatInput.disabled = !enabled;
    sendButton.disabled = !enabled;

    if (enabled) {
        if (chatInput.hasAttribute('readonly')) {
            chatInput.removeAttribute('readonly');
        }

        chatInput.placeholder = "在此输入您的问题...";

        if (uploadButton) {
            uploadButton.disabled = false;
        }

        if (pauseButton) {
            pauseButton.disabled = false;
        }

        // 确保聊天容器可见
        if (chatMessages) {
            chatMessages.style.display = 'block';
        }

        // 强制清除可能的内联样式
        chatInput.style.backgroundColor = '';
        chatInput.style.opacity = '1';
    } else {
        chatInput.placeholder = "请先选择左侧的文档节点...";

        if (uploadButton) {
            uploadButton.disabled = true;
        }

        if (pauseButton) {
            pauseButton.disabled = true;
        }
    }


}

/**
 * 关闭聊天侧边栏
 */
function closeChatSidebar() {


    const pageContent = document.querySelector('.page-content');
    if (pageContent) {
        pageContent.classList.remove('chat-active');
    }

    // 清理当前会话状态
    if (chatMessages) {
        chatMessages.innerHTML = '';
    }

    // 禁用输入框和按钮
    setChatInputEnabled(false);
}

/**
 * 更新选中的节点，准备聊天
 * @param {string} nodeId 节点ID
 * @param {string} nodeName 节点名称
 * @param {string} nodeType 节点类型
 */
function updateSelectedNode(nodeId, nodeName, nodeType) {
    if (!nodeId) {
        console.warn('[知识库聊天] 无效的节点ID，无法更新选择');
        return;
    }



    // 避免重复处理同一节点
    if (nodeId === currentKnowledgeNodeId) {

        setChatInputEnabled(true, nodeId);
        return;
    }

    // 清空现有聊天信息
    // 注释掉清空聊天历史，避免点击文档来源时丢失对话数据
    // if (chatMessages) {
    //     chatMessages.innerHTML = '';
    // }

    // 更新当前知识库节点 ID
    currentKnowledgeNodeId = nodeId;

    // 显示增强检索按钮（仅在知识库聊天中）
    showEnhancedRetrievalButton();

    // 如果聊天侧边栏未显示，可能不需要执行后续操作
    const pageContent = document.querySelector('.page-content');
    const isChatActive = pageContent && pageContent.classList.contains('chat-active');

    if (!isChatActive) {

        return;
    }

    // 重新初始化，传入知识库节点ID
    try {


        // 启用聊天输入框和发送按钮
        setChatInputEnabled(true, nodeId);
    } catch (error) {
        console.error("[知识库聊天] 重新初始化消息处理器失败:", error);
    }
}

/**
 * 设置聊天区域自动选择功能
 * 当用户点击聊天区域但未选择节点时，自动选择第一个可用节点
 */
function setupChatAreaAutoSelect() {


    // 当用户点击聊天区域但未选择节点时，尝试自动选择第一个节点
    const chatArea = document.querySelector('.chat-sidebar');
    if (chatArea) {
        chatArea.addEventListener('click', function(event) {
            // 如果点击的不是已有节点链接，并且当前没有选中节点
            if (!event.target.closest('[data-node-id]') && !currentKnowledgeNodeId) {
    
                autoSelectFirstAvailableNode();
            }
        });
    }

    // 监听聊天侧边栏的显示变化
    monitorChatSidebar();
}

/**
 * 监听聊天侧边栏的显示/隐藏状态
 */
function monitorChatSidebar() {
    const pageContentArea = document.querySelector('.page-content');

    if (pageContentArea) {
        // 使用MutationObserver监听class变化
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.attributeName === 'class') {
                    const isChatActive = pageContentArea.classList.contains('chat-active');
            

                    if (isChatActive) {
                        // 当聊天侧边栏显示，但没有选中节点时，自动选择第一个节点
                        const selectedNode = document.querySelector('.nav-link-container.active');
                        if (!selectedNode && !currentKnowledgeNodeId) {
                
                            setTimeout(autoSelectFirstAvailableNode, 100); // 稍微延迟以确保DOM已完全更新
                        }
                    }
                }
            });
        });
        observer.observe(pageContentArea, { attributes: true });
    
    }

    // 直接处理当前已打开的聊天侧边栏
    if (document.querySelector('.page-content.chat-active')) {

        const selectedNode = document.querySelector('.nav-link-container.active');
        if (!selectedNode) {

            setTimeout(autoSelectFirstAvailableNode, 200); // 延迟稍长一点，确保树节点已加载
        }
    }
}

/**
 * 自动选择第一个可用的文档节点
 */
function autoSelectFirstAvailableNode() {


    // 查找第一个可用的文档节点
    const firstNode = document.querySelector('.nav-link-container[data-node-id]');
    if (firstNode) {
        const nodeId = firstNode.getAttribute('data-node-id');
        const nodeName = firstNode.innerText || firstNode.textContent || "自动选择的节点";



        // 模拟点击该节点
        firstNode.click();

        // 更新当前聊天节点
        updateSelectedNode(nodeId, nodeName, firstNode.getAttribute('data-node-type'));

        return true;
    } else {
        console.warn('[知识库聊天] 未找到可用的文档节点');
        return false;
    }
}

// Function to display system messages (Helper)
function displaySystemMessage(message) {
    if (chatMessages) {
        // Avoid adding duplicate system messages rapidly
        const lastMessage = chatMessages.lastElementChild;
        if (lastMessage && lastMessage.classList.contains('system-message') && lastMessage.textContent.includes(message)) {
            return;
        }

        const systemDiv = document.createElement('div');
        systemDiv.classList.add('message', 'system-message', 'error-message'); // Style as error/warning
        systemDiv.innerHTML = `<p>${message}</p>`;
        chatMessages.appendChild(systemDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 尚不立即初始化，等待用户选择节点

});

// 导出公共函数
export function updateKnowledgeChatNode(nodeId, nodeName, nodeType) {

    updateSelectedNode(nodeId, nodeName, nodeType);
}

// 将初始化函数挂载到 window 对象，保持与现有代码的兼容性
window.initializeKnowledgeChat = function() {

    initializeMessageHandler();
};

// 将更新节点函数挂载到 window 对象，便于外部调用
window.updateKnowledgeChatNode = function(nodeId, nodeName, nodeType) {

    updateSelectedNode(nodeId, nodeName, nodeType);
};



/**
 * 显示增强检索按钮（仅在知识库聊天中）
 */
function showEnhancedRetrievalButton() {
    const enhancedRetrievalButton = document.getElementById('kb-chat-enhanced-retrieval');
    if (enhancedRetrievalButton) {
        enhancedRetrievalButton.style.display = 'inline-flex';

    } else {
        console.warn('[知识库聊天] 未找到增强检索按钮元素');
    }
}

/**
 * 隐藏增强检索按钮
 */
function hideEnhancedRetrievalButton() {
    const enhancedRetrievalButton = document.getElementById('kb-chat-enhanced-retrieval');
    if (enhancedRetrievalButton) {
        enhancedRetrievalButton.style.display = 'none';

    }
}

/**
 * 获取增强检索状态
 * @returns {boolean} 增强检索是否启用
 */
function getEnhancedRetrievalStatus() {
    const enhancedRetrievalButton = document.getElementById('kb-chat-enhanced-retrieval');
    if (enhancedRetrievalButton && enhancedRetrievalButton.enhancedRetrievalToggle) {
        return enhancedRetrievalButton.enhancedRetrievalToggle.getEnabled();
    }
    return false; // 默认不启用
}

// 导出增强检索相关函数
window.getEnhancedRetrievalStatus = getEnhancedRetrievalStatus;
window.showEnhancedRetrievalButton = showEnhancedRetrievalButton;
window.hideEnhancedRetrievalButton = hideEnhancedRetrievalButton;
