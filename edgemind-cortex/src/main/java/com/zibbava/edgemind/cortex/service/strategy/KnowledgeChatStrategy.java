package com.zibbava.edgemind.cortex.service.strategy;

import com.zibbava.edgemind.cortex.common.enums.NodeType;
import com.zibbava.edgemind.cortex.common.exception.ResourceNotFoundException;
import com.zibbava.edgemind.cortex.dto.ChatContext;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaChatRequest;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaMessage;
import com.zibbava.edgemind.cortex.entity.ChatMessage;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.enums.ModelInfo;
import com.zibbava.edgemind.cortex.service.ChatService;
import com.zibbava.edgemind.cortex.service.KnowledgeBaseService;
import com.zibbava.edgemind.cortex.service.QueryExpansionService;
import com.zibbava.edgemind.cortex.store.WeaviateEmbeddingStore;
import com.zibbava.edgemind.cortex.util.ModelUtils;
import com.zibbava.edgemind.cortex.util.OllamaUtils;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.filter.Filter;
import dev.langchain4j.store.embedding.filter.MetadataFilterBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.SynchronousSink;

import java.util.*;

/**
 * 知识库对话策略实现 - 支持混合检索和查询扩展
 * 使用 Dense + BM25 混合搜索和查询扩展技术提供更精准的知识检索
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class KnowledgeChatStrategy implements ChatStrategy {

    private final KnowledgeBaseService knowledgeBaseService;
    private final EmbeddingStore<TextSegment> embeddingStore;
    private final EmbeddingModel embeddingModel;
    private final ChatService chatService;
    private final ModelUtils modelUtils;
    private final PromptTemplateManager promptTemplateManager;
    private final QueryExpansionService queryExpansionService;

    @Value("${weaviate.optimization.search.max-results:5}")
    private int retrieverMaxResults;

    @Value("${weaviate.optimization.search.min-score:0.7}")
    private double retrieverMinScore;

    @Value("${weaviate.hybrid.enabled:false}")
    private boolean hybridSearchEnabled;

    @Value("${enhanced.retrieval.enabled:false}")
    private boolean enhancedRetrievalEnabled;

    @Value("${enhanced.retrieval.sub-queries:3}")
    private int numSubQueries;

    @Value("${enhanced.retrieval.hypothetical-docs:2}")
    private int numHypotheticalDocs;

    @Override
    public boolean checkPermission(ChatContext context) {
        try {
            if (context.getKnowledgeNodeId() != null) {
                KnowledgeNode node = knowledgeBaseService.findNodeById(context.getKnowledgeNodeId());
                if (node == null) {
                    log.warn("知识库节点不存在: {}", context.getKnowledgeNodeId());
                    return false;
                }

                // 检查用户是否有权访问该知识库
                knowledgeBaseService.checkAccess(node.getSpaceId(), context.getUserId());
                return true;
            }
            log.warn("知识库聊天缺少知识库节点ID");
            return false;
        } catch (Exception e) {
            log.error("知识库权限检查失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public ChatContext prepareContext(ChatContext context) {
        try {
            // 获取知识库节点
            KnowledgeNode node = knowledgeBaseService.findNodeById(context.getKnowledgeNodeId());
            if (node == null) {
                throw new ResourceNotFoundException("知识库节点不存在: " + context.getKnowledgeNodeId());
            }

            // 准备检索查询文本
            String searchQuery = prepareSearchQuery(context.getPrompt(), context);
            
            // 执行检索 - 直接使用nodeId进行路径过滤
            List<EmbeddingMatch<TextSegment>> matches = performRetrieval(searchQuery, context.getKnowledgeNodeId());

            // 构建检索上下文
            String retrievedContext = buildRetrievedContext(matches, context.getPrompt(), context.getKnowledgeNodeId(), context);
            context.setRetrievedContext(retrievedContext);

            log.info("✅ 知识库检索完成，获得 {} 个相关片段", matches.size());

            return context;
        } catch (Exception e) {
            log.error("准备知识库聊天上下文失败: {}", e.getMessage(), e);
            throw new RuntimeException("准备知识库聊天上下文失败", e);
        }
    }

    /**
     * 准备搜索查询文本 - 在启用增强检索时扩展查询
     */
    private String prepareSearchQuery(String originalQuery, ChatContext context) {
        // 检查用户是否启用了增强检索功能
        boolean userEnabledEnhancedRetrieval = context.getEnableEnhancedRetrieval() != null && 
                                               context.getEnableEnhancedRetrieval();
        
        if (!enhancedRetrievalEnabled || !userEnabledEnhancedRetrieval || 
            (numSubQueries <= 0 && numHypotheticalDocs <= 0)) {
            log.info("📊 使用原始查询进行检索 (全局配置: {}, 用户设置: {})", 
                    enhancedRetrievalEnabled, userEnabledEnhancedRetrieval);
            return originalQuery;
        }

        try {
            log.info("🚀 使用查询扩展技术增强检索 (用户已启用)");
            String expandedQuery = queryExpansionService.expandQueryText(
                    originalQuery, numSubQueries, numHypotheticalDocs);
            
            log.info("✅ 查询扩展完成，原长度: {}, 扩展后长度: {}", 
                    originalQuery.length(), expandedQuery.length());
            return expandedQuery;
            
        } catch (Exception e) {
            log.warn("❌ 查询扩展失败，使用原始查询: {}", e.getMessage());
            return originalQuery;
        }
    }

    /**
     * 执行检索 - 自动选择混合搜索或传统搜索
     */
    private List<EmbeddingMatch<TextSegment>> performRetrieval(String queryText, String nodeId) {
        var embedding = embeddingModel.embed(queryText).content();
        Filter filter = createMetadataFilter(nodeId);
        
        // 检查是否支持混合搜索
        if (hybridSearchEnabled && embeddingStore instanceof WeaviateEmbeddingStore) {
            try {
                log.debug("🔀 使用混合搜索 (Dense + BM25) 检索");
                
                WeaviateEmbeddingStore weaviateStore = (WeaviateEmbeddingStore) embeddingStore;
                
                // 执行混合搜索
                EmbeddingSearchResult<TextSegment> result = weaviateStore.hybridSearch(
                        queryText, 
                        embedding, 
                        retrieverMaxResults, 
                        retrieverMinScore, 
                        filter);
                List<EmbeddingMatch<TextSegment>> matches = result.matches();
                
                log.debug("✅ 混合搜索完成，返回 {} 个结果", matches.size());
                return matches;
                
            } catch (Exception e) {
                log.warn("❌ 混合搜索失败，回退到传统Dense搜索: {}", e.getMessage());
                // 回退到传统搜索
                return performDenseSearch(embedding, filter);
            }
        } else {
            log.debug("📊 使用传统Dense搜索检索");
            return performDenseSearch(embedding, filter);
        }
    }

    /**
     * 执行传统Dense向量搜索
     */
    private List<EmbeddingMatch<TextSegment>> performDenseSearch(
            dev.langchain4j.data.embedding.Embedding embedding, 
            Filter filter) {
        
        try {
            EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                    .queryEmbedding(embedding)
                    .maxResults(retrieverMaxResults)
                    .minScore(retrieverMinScore)
                    .filter(filter)
                    .build();

            EmbeddingSearchResult<TextSegment> searchResult = embeddingStore.search(searchRequest);
            List<EmbeddingMatch<TextSegment>> matches = searchResult.matches();
            
            log.debug("✅ Dense搜索完成，返回 {} 个结果", matches.size());
            return matches;
            
        } catch (Exception e) {
            log.error("❌ Dense搜索失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public String buildPrompt(ChatContext context) {
        // 使用企业级提示词模板管理器构建知识库对话提示词
        String searchMethod = getSearchMethodDescription(context);
            
        try {
            String finalPrompt = promptTemplateManager.buildKnowledgeChatPrompt(
                context.getPrompt(),
                context.getRetrievedContext()
            );

            log.debug("构建优化知识库对话提示词完成，长度: {} 字符", finalPrompt.length());
            return finalPrompt;

        } catch (Exception e) {
            log.error("构建知识库对话提示词失败: {}", e.getMessage(), e);
            // 使用备用提示词构建方法
            return buildFallbackKnowledgePrompt(context, searchMethod);
        }
    }

    /**
     * 获取搜索方法描述
     */
    private String getSearchMethodDescription(ChatContext context) {
        boolean userEnabledEnhancedRetrieval = context.getEnableEnhancedRetrieval() != null && 
                                               context.getEnableEnhancedRetrieval();
        boolean actualEnhancedRetrieval = enhancedRetrievalEnabled && userEnabledEnhancedRetrieval;
        
        if (actualEnhancedRetrieval && hybridSearchEnabled) {
            return "先进的增强检索技术（查询扩展 + 混合检索：语义相似度 + 关键词匹配）";
        } else if (actualEnhancedRetrieval) {
            return "增强检索技术（查询扩展 + 语义搜索）";
        } else if (hybridSearchEnabled) {
            return "混合检索技术（语义相似度 + 关键词匹配）";
        } else {
            return "语义搜索技术";
        }
    }
    
    /**
     * 构建备用知识库提示词（当主要模板失败时使用）
     */
    private String buildFallbackKnowledgePrompt(ChatContext context, String searchMethod) {
        return String.format(
                "你是一个专业的知识库问答助手。我已经使用%s为你找到了最相关的背景信息。\n\n"
                        + "请基于以下背景信息准确回答用户的问题：\n"
                        + "- 如果背景信息充分，请提供详细和准确的答案\n"
                        + "- 如果背景信息不足或与问题无关，请明确说明无法从已知信息中找到答案\n"
                        + "- 不要编造或推测超出背景信息范围的内容\n"
                        + "- 可以适当引用背景信息中的关键内容来支持你的回答\n\n"
                        + "背景信息：\n"
                        + "%s\n"
                        + "---\n"
                        + "用户问题：%s",
                searchMethod,
                context.getRetrievedContext(),
                context.getPrompt()
        );
    }

    @Override
    public Flux<String> executeChat(ChatContext context) {
        try {
            String searchType = hybridSearchEnabled ? "混合检索" : "语义搜索";
            log.info("🔍 执行知识库{}聊天，节点：{}，问题：{}", searchType, context.getKnowledgeNodeId(), context.getPrompt());

            // 构建知识库对话消息（通常不包含历史记忆）
            List<OllamaMessage> messages = new ArrayList<>();
            messages.add(OllamaMessage.user(context.getFinalPrompt()));

            // 创建OllamaChatRequest
            OllamaChatRequest chatRequest = OllamaChatRequest.builder()
                    .model(context.getModelName())
                    .messages(messages)
                    .stream(true)
                    .sessionId("knowledge_chat_" + context.getUserId() + "_" + context.getConversationId())
                    .build();

            // 设置深度思考功能 - 根据ModelInfo枚举检查模型支持
            boolean shouldEnableThinking = false;
            if (ModelInfo.supportsThinking(context.getModelName())) {
                if (context.getEnableThinking() != null) {
                    shouldEnableThinking = context.getEnableThinking();
                    log.info("🧠 知识库深度思考功能: {} (模型: {})", shouldEnableThinking ? "开启" : "关闭", context.getModelName());
                } else {
                    // 默认开启深度思考，知识库问答通常需要更深入的思考
                    shouldEnableThinking = true;
                    log.info("🧠 知识库深度思考功能: 开启 (默认, 模型: {})", context.getModelName());
                }
            } else {
                log.info("🧠 知识库深度思考功能: 关闭 (模型 {} 不支持think功能)", context.getModelName());
            }
            chatRequest.enableThinking(shouldEnableThinking);

            // 使用OllamaUtils执行流式聊天
            return OllamaUtils.streamChat(chatRequest)
                    .startWith(Mono.fromCallable(() -> {
                        // 在流式响应开始前发送文档来源信息
                        if (context.getDocumentSources() != null && !context.getDocumentSources().trim().isEmpty()) {
                            return "SOURCES:" + context.getDocumentSources();
                        }
                        return null;
                    }).filter(sources -> sources != null))
                    .handle((String rawToken, SynchronousSink<String> sink) -> {
                        chatService.handelSSE(
                                context.getMainContentBuilder(),
                                context.getThinkingContentBuilder(),
                                rawToken,
                                sink,
                                context.getIsInsideThinkingBlock()
                        );
                    })
                    .doOnComplete(() -> {
                        log.info("✅ 知识库{}聊天完成: conversationId={}, nodeId={}",
                                searchType, context.getConversationId(), context.getKnowledgeNodeId());
                    })
                    .doOnError(e -> log.error("❌ 知识库{}聊天出错: {}", searchType, e.getMessage(), e));
        } catch (Exception e) {
            log.error("❌ 执行知识库对话出错: {}", e.getMessage(), e);
            return Flux.error(e);
        }
    }

    @Override
    public ChatMessage saveChatHistory(ChatContext context, String userContent, String aiContent, String thinkingContent) {
//        // 保存用户消息和AI消息
//        chatService.saveUserMessage(
//                context.getConversationId(),
//                userContent,
//                null
//        );
//
//        // 保存AI消息（这里可以考虑添加引用信息的保存）
//        return chatService.saveAiMessage(
//                context.getConversationId(),
//                aiContent,
//                thinkingContent,
//                0,
//                context.getModelName()
//        );

        //知识库不保存对话消息
        return null;
    }

    @Override
    public boolean isApplicable(ChatContext context) {
        return context.getKnowledgeNodeId() != null && !context.getKnowledgeNodeId().trim().isEmpty();
    }

    /**
     * 获取检索目标节点ID列表
     */
    private List<String> getTargetNodeIds(KnowledgeNode node) {
        List<String> targetNodeIds = new ArrayList<>();

        if (node.getType() == NodeType.FILE) {
            // 如果是文件节点，直接检索该文件
            targetNodeIds.add(node.getNodeId());
        } else if (node.getType() == NodeType.FOLDER) {
            // 如果是文件夹节点，检索文件夹下的所有文件
            targetNodeIds.addAll(knowledgeBaseService.getAllFileNodeIdsInFolder(node.getNodeId()));
        }

        return targetNodeIds;
    }

    /**
     * 创建元数据过滤器 - 基于节点ID进行过滤
     */
    private Filter createMetadataFilter(String nodeId) {
        if (nodeId == null || nodeId.isEmpty()) {
            return null;
        }
        
        try {
            // 获取节点信息
            KnowledgeNode node = knowledgeBaseService.findNodeById(nodeId);
            if (node == null) {
                return null;
            }
            
            if (NodeType.FILE.equals(node.getType())) {
                // 文件节点：使用node_id进行精确匹配
                return MetadataFilterBuilder.metadataKey("node_id").isEqualTo(nodeId);
            } else{
                // 文件夹节点：使用node_path包含nodeId进行匹配（类似like操作）
                return MetadataFilterBuilder.metadataKey("node_path").containsString(nodeId);
            }
        } catch (Exception e) {
            log.error("创建元数据过滤器失败: nodeId={}", nodeId, e);
        }
        
        return null;
    }

    /**
     * 构建检索上下文 - 增强版本，突出混合检索的效果
     */
    private String buildRetrievedContext(List<EmbeddingMatch<TextSegment>> matches, String query, String contextNodeId, ChatContext chatContext) {
        if (matches.isEmpty()) {
            return "未找到相关内容。";
        }

        StringBuilder context = new StringBuilder();
        StringBuilder sourcesInfo = new StringBuilder();
        Set<String> uniqueSources = new LinkedHashSet<>(); // 用于去重文档来源
        
        // 添加检索方法说明
        String searchMethod = getSearchMethodDescription(chatContext);
        context.append(String.format("【检索说明】使用%s找到以下相关内容：\n\n", searchMethod));

        for (int i = 0; i < matches.size(); i++) {
            EmbeddingMatch<TextSegment> match = matches.get(i);
            TextSegment segment = match.embedded();
            
            // 添加片段编号和相似度分数
            context.append(String.format("【片段 %d】（相似度: %.3f）\n", i + 1, match.score()));
            
            // 收集文档来源信息（用于前端显示）
            if (segment.metadata() != null) {
                String fileName = segment.metadata().getString("file_name");
                String nodeId = segment.metadata().getString("node_id");
                if (fileName != null && !fileName.isEmpty() && nodeId != null && !nodeId.isEmpty()) {
                    String sourceKey = fileName + "|" + nodeId;
                    if (!uniqueSources.contains(sourceKey)) {
                        uniqueSources.add(sourceKey);
                        sourcesInfo.append(String.format("来源文件: %s (节点ID: %s)\n", fileName, nodeId));
                    }
                    // 在检索内容中也添加来源信息
                    context.append(String.format("来源文件: %s (节点ID: %s)\n", fileName, nodeId));
                } else if (fileName != null && !fileName.isEmpty()) {
                    context.append(String.format("来源文件: %s\n", fileName));
                }
            }
            
            // 添加内容
            context.append(segment.text());
            context.append("\n\n");
        }

        // 添加检索统计信息
        String resultDescription = hybridSearchEnabled ? 
                "涵盖语义相似和关键词匹配结果" : "基于语义相似度排序";
        context.append(String.format("【检索统计】共找到 %d 个相关片段，%s。", matches.size(), resultDescription));

        // 将文档来源信息存储到上下文中，供流式响应使用
        if (sourcesInfo.length() > 0) {
            chatContext.setDocumentSources(sourcesInfo.toString());
        }

        return context.toString();
    }
}
