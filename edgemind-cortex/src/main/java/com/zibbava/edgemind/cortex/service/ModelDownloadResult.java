package com.zibbava.edgemind.cortex.service;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模型下载结果数据传输对象
 * 用于封装下载进度信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelDownloadResult {
    /**
     * 下载状态
     */
    private String status;
    
    /**
     * 已下载字节数
     */
    private long completed;
    
    /**
     * 总字节数
     */
    private long total;
    
    /**
     * 下载进度百分比 (0-100)
     */
    private int progress;
    
    /**
     * 错误信息
     */
    private String error;
    
    /**
     * 计算当前进度百分比
     * 
     * @return 百分比值(0-100)
     */
    public int calculateProgress() {
        if (total <= 0) return 0;
        return (int)((completed * 100) / total);
    }
}
