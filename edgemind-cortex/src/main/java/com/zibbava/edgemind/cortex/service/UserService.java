package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.dto.LoginResponse;
import com.zibbava.edgemind.cortex.entity.User;

import java.util.List;
import java.util.Map;

public interface UserService extends IService<User> {

    /**
     * 用户注册
     * @param username 用户名
     * @param password 原始密码
     * @param nickname 昵称
     * @param email 邮箱
     * @param phone 手机号
     * @param deptId 部门ID
     * @param roleIds 角色ID列表
     * @param remark 备注
     */
    void register(String username, String password, String nickname, String email, String phone, Long deptId, List<Long> roleIds, String remark);

    /**
     * 用户登录
     * @param username 用户名
     * @param password 原始密码
     * @return LoginResponse 包含 Token 和用户信息
     */
    LoginResponse login(String username, String password);

    /**
     * 用户登出
     */
    void logout();

    /**
     * 给用户分配角色
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    void assignRolesToUser(Long userId, List<Long> roleIds);

    /**
     * 根据用户ID获取角色ID列表
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> getRoleIdsByUserId(Long userId);

    /**
     * 分页查询用户列表 (包含部门和角色信息)
     * @param page 分页参数
     * @param queryParams 查询参数 (如 username, nickname, deptId, status 等)
     * @return 分页结果
     */
    IPage<User> getUserPage(Page<User> page, Map<String, Object> queryParams);

    /**
     * 更新用户信息 (不包括密码和角色)
     * @param user 用户信息
     */
    void updateUserProfile(User user);

    /**
     * 更新用户状态
     * @param userId 用户ID
     * @param status 状态值
     */
    void updateUserStatus(Long userId, Integer status);

    /**
     * 重置用户密码
     * @param userId 用户ID
     * @param newPassword 新密码 (明文)
     */
    void resetPassword(Long userId, String newPassword);

    /**
     * 根据用户ID删除用户 (逻辑删除或物理删除)
     * @param userId 用户ID
     */
    void deleteUser(Long userId);

}