/**
 * settings.js - 系统设置页面功能
 */
document.addEventListener('DOMContentLoaded', function() {
    

    // 初始化设置页面
    initSettings();

    // 注册事件监听器
    registerEventListeners();
});

// API 基础路径
 const API_BASE_URL = '/wkg/api';

/**
 * 初始化设置页面
 */
function initSettings() {
    // 从后端获取设置
    fetchSettings();
}

/**
 * 注册事件监听器
 */
function registerEventListeners() {
    // 复制标识码按钮
    const copyIdentifierBtn = document.getElementById('copyIdentifierBtn');
    if (copyIdentifierBtn) {
        copyIdentifierBtn.addEventListener('click', function() {
            const systemIdentifier = document.getElementById('systemIdentifier').textContent;
            copyToClipboard(systemIdentifier);
            showToast('标识码已复制到剪贴板', 'success');
        });
    }

    // 同步按钮
    const syncButton = document.getElementById('syncButton');
    if (syncButton) {
        syncButton.addEventListener('click', function() {
            // 显示确认模态框
            const syncConfirmModal = new bootstrap.Modal(document.getElementById('syncConfirmModal'));
            syncConfirmModal.show();
        });
    }

    // 确认同步按钮
    const confirmSyncBtn = document.getElementById('confirmSyncBtn');
    if (confirmSyncBtn) {
        confirmSyncBtn.addEventListener('click', function() {
            // 关闭模态框
            const syncConfirmModal = bootstrap.Modal.getInstance(document.getElementById('syncConfirmModal'));
            syncConfirmModal.hide();

            // 执行同步操作
            syncKnowledgeBase();
        });
    }

    // 保存个人库路径按钮
    const savePersonalPathButton = document.getElementById('savePersonalPathButton');
    if (savePersonalPathButton) {
        savePersonalPathButton.addEventListener('click', function() {
            savePersonalStoragePath();
        });
    }

    // 路径输入框事件
    const storagePathInput = document.getElementById('storagePath');
    if (storagePathInput) {
        // 添加回车键事件
        storagePathInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault(); // 防止表单提交
            }
        });
    }

    // 个人库路径输入框事件
    const personalStoragePathInput = document.getElementById('personalStoragePath');
    if (personalStoragePathInput) {
        // 添加回车键事件
        personalStoragePathInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault(); // 防止表单提交
                savePersonalStoragePath();
            }
        });
    }
}

/**
 * 从服务器获取设置
 */
function fetchSettings() {
    fetch(`${API_BASE_URL}/settings`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.code === 200 && data.data) {
    
                // 显示设置数据
                displaySettings(data.data);
            } else {
                console.error('加载设置失败:', data.message);
                showToast('加载设置失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('加载设置失败:', error);
            showToast('加载设置失败，请稍后再试', 'error');
        });
}

/**
 * 显示设置数据
 * @param {Object} settings - 设置数据
 */
function displaySettings(settings) {
    // 显示系统版本
    const versionElement = document.getElementById('systemVersion');
    if (versionElement && settings.version) {
        versionElement.textContent = settings.version;
    }

    // 显示系统唯一标识
    const identifierElement = document.getElementById('systemIdentifier');
    if (identifierElement && settings.systemId) {
        identifierElement.textContent = settings.systemId;
    }

    // 显示知识库存储路径
    const storagePathElement = document.getElementById('storagePath');
    if (storagePathElement && settings.storagePath) {
        storagePathElement.value = settings.storagePath;
    }

    // 显示个人库存储路径
    const personalStoragePathElement = document.getElementById('personalStoragePath');
    if (personalStoragePathElement && settings.personalStoragePath) {
        personalStoragePathElement.value = settings.personalStoragePath;
    }

    // 显示同步状态
    updateSyncStatusDisplay(settings);
}

/**
 * 更新同步状态显示
 * @param {Object} settings - 设置数据
 */
function updateSyncStatusDisplay(settings) {
    const syncStatusElement = document.getElementById('syncStatus');
    const lastSyncTimeElement = document.getElementById('lastSyncTime');

    if (!syncStatusElement || !lastSyncTimeElement) return;

    // 同步状态文本
    let statusText = '未同步';
    let statusClass = 'text-secondary';

    if (settings.syncStatus !== undefined) {
        switch (settings.syncStatus) {
            case 0: // 未同步
                statusText = '未同步';
                statusClass = 'text-secondary';
                break;
            case 1: // 同步中
                statusText = '同步中...';
                statusClass = 'text-primary';
                break;
            case 2: // 同步完成
                statusText = '已同步';
                statusClass = 'text-success';
                break;
            case 3: // 同步失败
                statusText = '同步失败';
                statusClass = 'text-danger';
                break;
            default:
                statusText = '未知状态';
                statusClass = 'text-secondary';
        }
    }

    // 更新状态文本和类
    syncStatusElement.textContent = statusText;
    syncStatusElement.className = statusClass + ' fw-medium';

    // 更新最后同步时间
    if (settings.lastSyncTime) {
        const date = new Date(settings.lastSyncTime);
        lastSyncTimeElement.textContent = date.toLocaleString();
    } else {
        lastSyncTimeElement.textContent = '无';
    }

    // 如果有同步消息，显示在工具提示中
    if (settings.syncMessage && settings.syncStatus === 3) { // 只在同步失败时显示消息
        const syncButton = document.getElementById('syncButton');
        if (syncButton) {
            syncButton.setAttribute('title', settings.syncMessage);
            syncButton.setAttribute('data-bs-toggle', 'tooltip');
            syncButton.setAttribute('data-bs-placement', 'top');

            // 初始化工具提示
            new bootstrap.Tooltip(syncButton);
        }
    }
}

/**
 * 保存设置
 * @param {Function} callback - 保存成功后的回调函数
 */
function saveSettings(callback) {
    const storagePath = document.getElementById('storagePath').value;

    if (!storagePath) {
        showToast('知识库存储路径不能为空', 'warning');
        return;
    }

    const settings = {
        storagePath: storagePath
    };

    fetch(`${API_BASE_URL}/settings`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.code === 200) {
            // 如果有回调函数，则执行
            if (typeof callback === 'function') {
                callback();
            }
        } else {
            showToast('设置保存失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('保存设置失败:', error);
        showToast('保存设置失败，请稍后再试', 'error');
    });
}

/**
 * 保存个人库存储路径
 */
function savePersonalStoragePath() {
    const personalStoragePath = document.getElementById('personalStoragePath').value;

    if (!personalStoragePath) {
        showToast('个人库存储路径不能为空', 'warning');
        return;
    }

    // 禁用保存按钮
    const saveButton = document.getElementById('savePersonalPathButton');
    if (saveButton) {
        saveButton.disabled = true;
        saveButton.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> 保存中...';
    }

    const settings = {
        personalStoragePath: personalStoragePath
    };

    fetch(`${API_BASE_URL}/settings/personal`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // 恢复按钮状态
        if (saveButton) {
            saveButton.disabled = false;
            saveButton.innerHTML = '<i class="bi bi-save me-1"></i> 保存';
        }

        if (data.code === 200) {
            showToast('个人库路径保存成功', 'success');
        } else {
            showToast('个人库路径保存失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('保存个人库路径失败:', error);
        // 恢复按钮状态
        if (saveButton) {
            saveButton.disabled = false;
            saveButton.innerHTML = '<i class="bi bi-save me-1"></i> 保存';
        }
        showToast('保存个人库路径失败，请稍后再试', 'error');
    });
}

/**
 * 同步知识库
 */
function syncKnowledgeBase() {
    // 获取存储路径
    const storagePath = document.getElementById('storagePath').value;

    if (!storagePath) {
        showToast('知识库存储路径不能为空', 'warning');
        return;
    }

    // 显示加载中提示
    showToast('正在保存路径并同步知识库，请稍候...', 'info');

    // 禁用同步按钮
    const syncButton = document.getElementById('syncButton');
    if (syncButton) {
        syncButton.disabled = true;
        syncButton.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> 同步中...';
    }

    // 先保存路径设置，然后再执行同步
    saveSettings(function() {
        // 路径保存成功后执行同步
        fetch(`${API_BASE_URL}/settings/sync`, {
            method: 'POST'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.code === 200) {
                // 开始轮询同步状态
                pollSyncStatus();
            } else {
                // 恢复按钮状态
                if (syncButton) {
                    syncButton.disabled = false;
                    syncButton.innerHTML = '<i class="bi bi-arrow-repeat me-1"></i> 同步';
                }
                showToast('知识库同步失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('同步知识库失败:', error);
            // 恢复按钮状态
            if (syncButton) {
                syncButton.disabled = false;
                syncButton.innerHTML = '<i class="bi bi-arrow-repeat me-1"></i> 同步';
            }
            showToast('同步知识库失败，请稍后再试', 'error');
        });
    });
}

/**
 * 轮询同步状态
 */
function pollSyncStatus() {
    let pollCount = 0;
    const maxPolls = 60; // 最多轮询 60 次，大约 5 分钟
    const pollInterval = 5000; // 5 秒轮询一次

    const pollTimer = setInterval(() => {
        pollCount++;

        fetch(`${API_BASE_URL}/settings/sync/status`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.code === 200 && data.data) {
                    const status = data.data;

                    // 更新状态显示
                    updateSyncStatusDisplay(status);

                    // 检查是否完成或失败
                    if (status.syncStatus === 2) { // 同步完成
                        clearInterval(pollTimer);
                        // 恢复按钮状态
                        const syncButton = document.getElementById('syncButton');
                        if (syncButton) {
                            syncButton.disabled = false;
                            syncButton.innerHTML = '<i class="bi bi-arrow-repeat me-1"></i> 同步';
                        }
                        showToast('知识库同步成功', 'success');
                    } else if (status.syncStatus === 3) { // 同步失败
                        clearInterval(pollTimer);
                        // 恢复按钮状态
                        const syncButton = document.getElementById('syncButton');
                        if (syncButton) {
                            syncButton.disabled = false;
                            syncButton.innerHTML = '<i class="bi bi-arrow-repeat me-1"></i> 同步';
                        }
                        showToast('知识库同步失败: ' + status.syncMessage, 'error');
                    } else if (pollCount >= maxPolls) { // 超时
                        clearInterval(pollTimer);
                        // 恢复按钮状态
                        const syncButton = document.getElementById('syncButton');
                        if (syncButton) {
                            syncButton.disabled = false;
                            syncButton.innerHTML = '<i class="bi bi-arrow-repeat me-1"></i> 同步';
                        }
                        showToast('同步状态查询超时，请手动刷新页面查看结果', 'warning');
                    }
                }
            })
            .catch(error => {
                console.error('查询同步状态失败:', error);
                // 如果查询失败，不终止轮询，继续尝试
            });
    }, pollInterval);
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 */
function copyToClipboard(text) {
    // 创建一个临时的文本区域
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed'; // 防止滚动到视图中
    document.body.appendChild(textarea);
    textarea.select();

    try {
        // 执行复制命令
        document.execCommand('copy');
    } catch (err) {
        console.error('复制失败:', err);
    }

    // 删除临时文本区域
    document.body.removeChild(textarea);
}

/**
 * 显示消息提示
 * @param {string} message - 提示消息
 * @param {string} type - 消息类型：'success', 'error', 'warning', 'info'
 */
function showToast(message, type = 'info') {
    // 使用全局Toast组件显示消息
    window.parent.postMessage({
        type: 'SHOW_TOAST',
        payload: { message, type }
    }, '*');
}
