/**
 * 通用聊天组件初始化模块
 * 提供统一的聊天功能初始化接口，支持普通聊天和知识库聊天
 */

import { initialize as initializeMessageHandler, sendMessage, terminateConversation } from '/wkg/js/features/chat/message-handler.js';
import { initialize as initializeModelSelector } from '/wkg/js/features/chat/model-selector-component.js';

/**
 * 聊天组件配置类
 */
class ChatComponentConfig {
    constructor(configElement) {
        this.componentId = configElement.dataset.componentId;
        this.chatType = configElement.dataset.chatType || 'general';
        this.knowledgeNodeId = configElement.dataset.knowledgeNodeId || null;
        this.conversationId = configElement.dataset.conversationId || null;
    }

    /**
     * 更新知识库节点ID
     */
    updateKnowledgeNodeId(nodeId) {
        this.knowledgeNodeId = nodeId;
        const configElement = document.getElementById(`chat-config-${this.componentId}`);
        if (configElement) {
            configElement.dataset.knowledgeNodeId = nodeId || '';
        }
    }

    /**
     * 获取发送消息时的上下文参数
     */
    getSendContext() {
        const context = {
            type: this.chatType
        };

        if (this.chatType === 'knowledge' && this.knowledgeNodeId) {
            context.knowledgeNodeId = this.knowledgeNodeId;
        }

        if (this.conversationId) {
            context.conversationId = this.conversationId;
        }

        return context;
    }
}

/**
 * 通用聊天组件类
 */
class ChatComponent {
    constructor(componentId) {
        this.componentId = componentId;
        this.isInitialized = false;
        this.config = null;
        this.elements = {};
    }

    /**
     * 初始化聊天组件
     */
    initialize() {
        if (this.isInitialized) {

            return true;
        }



        // 获取配置
        const configElement = document.getElementById(`chat-config-${this.componentId}`);
        if (!configElement) {
            console.error(`[聊天组件] 未找到配置元素: chat-config-${this.componentId}`);
            return false;
        }

        this.config = new ChatComponentConfig(configElement);


        // 获取DOM元素
        if (!this._getDOMElements()) {
            console.error(`[聊天组件] DOM元素获取失败`);
            return false;
        }

        // 初始化核心功能
        if (!this._initializeCore()) {
            console.error(`[聊天组件] 核心功能初始化失败`);
            return false;
        }

        // 绑定事件
        this._bindEvents();

        // 特定类型的初始化
        this._initializeSpecificType();

        this.isInitialized = true;

        return true;
    }

    /**
     * 获取DOM元素
     */
    _getDOMElements() {
        // 根据组件ID构建元素ID映射
        const elementIds = this._getElementIds();
        
        for (const [key, id] of Object.entries(elementIds)) {
            const element = document.getElementById(id);
            if (!element) {
                console.error(`[聊天组件] 未找到元素: ${id}`);
                return false;
            }
            this.elements[key] = element;
        }


        return true;
    }

    /**
     * 获取元素ID映射（子类可重写）
     */
    _getElementIds() {
        // 默认的元素ID映射，基于组件配置推断
        const baseIds = {
            chatMessages: `${this.componentId === 'kb-chat' ? 'kb-chat-messages' : 'chat-messages'}`,
            userInput: `${this.componentId === 'kb-chat' ? 'chat-input' : 'user-input'}`,
            sendButton: 'send-button',
            modelSelect: 'model-select',
            uploadButton: 'upload-button',
            imageUpload: `${this.componentId === 'kb-chat' ? 'image-upload' : 'image-upload'}`,
            imagePreviewContainer: `${this.componentId === 'kb-chat' ? 'image-preview' : 'image-preview-container'}`,
            pauseButton: 'pause-button'
        };

        return baseIds;
    }

    /**
     * 初始化核心功能
     */
    _initializeCore() {
        try {
            // 初始化消息处理器
            if (!initializeMessageHandler(this.elements)) {
                console.error(`[聊天组件] 消息处理器初始化失败`);
                return false;
            }

            // 初始化模型选择器
            if (this.elements.modelSelect) {
                initializeModelSelector('model-select');
            }

            return true;
        } catch (error) {
            console.error(`[聊天组件] 核心功能初始化异常:`, error);
            return false;
        }
    }

    /**
     * 绑定事件
     */
    _bindEvents() {
        // 发送按钮点击事件
        if (this.elements.sendButton) {
            this.elements.sendButton.addEventListener('click', () => {
                this._handleSendMessage();
            });
        }

        // 输入框回车事件
        if (this.elements.userInput) {
            this.elements.userInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this._handleSendMessage();
                }
            });
        }

        // 暂停按钮事件
        if (this.elements.pauseButton) {
            this.elements.pauseButton.addEventListener('click', () => {
                terminateConversation();
            });
        }


    }

    /**
     * 处理发送消息
     */
    _handleSendMessage() {
        // 检查是否可以发送消息
        if (!this._canSendMessage()) {
            return;
        }

        // 获取发送上下文
        const context = this.config.getSendContext();
        
        // 发送消息
        sendMessage(context);
    }

    /**
     * 检查是否可以发送消息
     */
    _canSendMessage() {
        const hasText = this.elements.userInput.value.trim() !== '';
        const hasImage = this.elements.imagePreviewContainer.querySelector('.image-preview') !== null;
        
        if (!hasText && !hasImage) {
            return false;
        }

        // 知识库聊天需要选择节点
        if (this.config.chatType === 'knowledge' && !this.config.knowledgeNodeId) {
            this._displaySystemMessage("请先选择一个文档或节点再发送消息。");
            return false;
        }

        return true;
    }

    /**
     * 显示系统消息
     */
    _displaySystemMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message system-message';
        messageDiv.innerHTML = `<p class="text-warning">${message}</p>`;
        this.elements.chatMessages.appendChild(messageDiv);
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
    }

    /**
     * 特定类型的初始化（子类可重写）
     */
    _initializeSpecificType() {
        if (this.config.chatType === 'knowledge') {
            this._initializeKnowledgeChat();
        } else if (this.config.chatType === 'general') {
            this._initializeGeneralChat();
        }
    }

    /**
     * 初始化知识库聊天特定功能
     */
    _initializeKnowledgeChat() {
        // 初始状态设置为禁用
        this.elements.userInput.disabled = true;
        this.elements.sendButton.disabled = true;

        // 监听文档节点选择事件
        document.addEventListener('click', (event) => {
            const node = event.target.closest('[data-node-id]');
            if (node) {
                const nodeId = node.getAttribute('data-node-id');
                this.updateSelectedNode(nodeId);
            }
        });

        // 监听自定义节点选择事件
        document.addEventListener('document-node-selected', (event) => {
            if (event.detail && event.detail.id) {
                this.updateSelectedNode(event.detail.id);
            }
        });


    }

    /**
     * 初始化普通聊天特定功能
     */
    _initializeGeneralChat() {
        // 普通聊天默认启用
        this.elements.userInput.disabled = false;
        this.elements.sendButton.disabled = true; // 初始禁用，有内容时启用


    }

    /**
     * 更新选中的知识库节点
     */
    updateSelectedNode(nodeId) {
        if (this.config.chatType !== 'knowledge') {
            return;
        }

        this.config.updateKnowledgeNodeId(nodeId);
        
        // 启用聊天输入
        this.elements.userInput.disabled = false;
        this.elements.sendButton.disabled = false;


    }

    /**
     * 获取组件配置
     */
    getConfig() {
        return this.config;
    }
}

/**
 * 聊天组件管理器
 */
class ChatComponentManager {
    constructor() {
        this.components = new Map();
    }

    /**
     * 注册并初始化聊天组件
     */
    register(componentId) {
        if (this.components.has(componentId)) {

            return this.components.get(componentId);
        }

        const component = new ChatComponent(componentId);
        if (component.initialize()) {
            this.components.set(componentId, component);
    
            return component;
        } else {
            console.error(`[聊天组件管理器] 组件 ${componentId} 注册失败`);
            return null;
        }
    }

    /**
     * 获取组件
     */
    get(componentId) {
        return this.components.get(componentId);
    }

    /**
     * 自动发现并初始化页面中的所有聊天组件
     */
    autoDiscoverAndInitialize() {
        const configElements = document.querySelectorAll('.chat-component-config');
        const initializedComponents = [];

        configElements.forEach(configElement => {
            const componentId = configElement.dataset.componentId;
            if (componentId) {
                const component = this.register(componentId);
                if (component) {
                    initializedComponents.push(componentId);
                }
            }
        });

    
        return initializedComponents;
    }
}

// 创建全局管理器实例
const chatComponentManager = new ChatComponentManager();

// 将管理器暴露到全局window对象，以便其他脚本可以访问
window.chatComponentManager = chatComponentManager;

// 导出接口
export {
    ChatComponent,
    ChatComponentManager,
    chatComponentManager
};

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', () => {
    
    chatComponentManager.autoDiscoverAndInitialize();
});