package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.dto.LoginRequest;
import com.zibbava.edgemind.cortex.dto.LoginResponse;
import com.zibbava.edgemind.cortex.dto.RegisterRequest;
import com.zibbava.edgemind.cortex.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/auth") // 给所有接口加上 /auth 前缀
public class AuthController {

    @Autowired
    private UserService userService;

    /**
     * 注册接口
     * @param request 包含 username 和 password 的 DTO
     * @return 标准响应体
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<Void>> register(@RequestBody RegisterRequest request) {
        // 可以在这里添加 Bean Validation (@Valid RegisterRequest request)
//        userService.register(request.getUsername(), request.getPassword()); //todo 注册逻辑
        return ResponseEntity.ok(ApiResponse.success("注册成功"));
    }

    /**
     * 登录接口
     * @param request 包含 username 和 password 的 DTO
     * @return 标准响应体，包含 Token 和用户信息
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<LoginResponse>> login(@RequestBody LoginRequest request) {
        // 可以在这里添加 Bean Validation (@Valid LoginRequest request)
        LoginResponse loginResponse = userService.login(request.getUsername(), request.getPassword());
        return ResponseEntity.ok(ApiResponse.success("登录成功", loginResponse));
    }

    /**
     * 登出接口
     * @return 标准响应体
     */
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Void>> logout() {
         userService.logout(); // Service 层会处理未登录异常
         return ResponseEntity.ok(ApiResponse.success("登出成功"));
    }

     /**
     * 检查登录状态接口
     * @return 标准响应体，包含登录状态和用户信息
     */
    @GetMapping("/check-login")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkLogin() {
        boolean isLogin = StpUtil.isLogin();
        Map<String, Object> data = new java.util.HashMap<>();
        data.put("isLogin", isLogin);
        if (isLogin) {
            data.put("userId", StpUtil.getLoginIdAsLong());
            // 从 Session 获取用户名
            String username = StpUtil.getSession().getString("username"); 
            data.put("username", username != null ? username : "用户" + StpUtil.getLoginIdAsLong()); // 提供一个回退显示
        }
        String message = isLogin ? "已登录" : "未登录";
        return ResponseEntity.ok(ApiResponse.success(message, data));
    }
} 