// chat-history.js - 处理聊天历史功能

import { loadMessagesForConversation } from './message-handler.js';
import { showToast } from '../../shared/components/modal-component.js';
import { ModalComponent } from '../../shared/components/modal-component.js';
import { checkLoginStatus, isGuest } from '../../shared/services/auth-service.js';

// --- 全局状态变量 ---
let currentConversationId = null;
let isLoadingConversationList = false;
let currentConversationPage = 0;
let hasMoreConversations = true;
let isGuestMode = false;

// --- DOM 元素引用 ---
let elements = {
    conversationsList: null,
    newChatButton: null
};

// --- 初始化函数 ---
function initialize(domElements) {

    elements = domElements;

    if (!elements.conversationsList) {
        console.error("[ChatHistory] Error: Cannot find conversations-list element.");
        return false;
    }

    if (elements.newChatButton) {
        elements.newChatButton.addEventListener('click', createNewConversation);
    } else {
        console.warn("[ChatHistory] Warning: Cannot find new-chat-button element.");
    }

    // 初始化会话状态
    initializeConversationState()
        .then(() => {
            // 设置滚动监听器（在会话加载完成后）
            setupConversationListScrollListener();
        })
        .catch(error => {
            console.error("[ChatHistory] 初始化失败:", error);
        });

    return true;
}

// 检查用户登录状态（使用共享服务）
async function checkUserLoginStatus() {
    try {
        const result = await checkLoginStatus();
        isGuestMode = !result.isLoggedIn;
        return result.isLoggedIn;
    } catch (error) {
        console.error("[ChatHistory] 检查登录状态失败:", error);
        isGuestMode = true;
        return false;
    }
}

// 初始化会话状态
async function initializeConversationState() {
    try {
        // 检查登录状态（使用缓存）
        const isLoggedIn = await checkUserLoginStatus();

        if (!isLoggedIn) {
            showWelcomeMessage();
            return;
        }

        // 从URL获取会话ID
        const urlParams = new URLSearchParams(window.location.search);
        const conversationIdParam = urlParams.get('conversationId');

        if (conversationIdParam && !isNaN(parseInt(conversationIdParam))) {
            currentConversationId = parseInt(conversationIdParam);
        }

        // 加载会话列表
        await loadConversationList();

        // 如果URL中没有指定会话ID，则调用初始化接口获取最近活跃会话
        if (!currentConversationId) {
            try {
                const initResponse = await fetch('/wkg/api/conversations/init');
                if (initResponse.ok) {
                    const initData = await initResponse.json();
                    if (initData.currentConversationId) {
                        currentConversationId = initData.currentConversationId;
                    } else if (initData.data && initData.data.currentConversationId) {
                        // 适配可能的不同返回格式
                        currentConversationId = initData.data.currentConversationId;
                    }
                } else {
                    console.warn("[ChatHistory] 初始化接口调用失败:", initResponse.status);
                }
            } catch (error) {
                console.error("[ChatHistory] 调用初始化接口失败:", error);
            }
        }

        // 如果有会话ID（从URL或初始化接口获取），则加载该会话
        if (currentConversationId) {
            await loadConversation(currentConversationId);
        }
        // 否则加载第一个会话或创建新会话
        else {
            const firstItem = elements.conversationsList.querySelector('.conversation-item');
            if (firstItem && firstItem.dataset.conversationId) {
                await loadConversation(firstItem.dataset.conversationId);
            } else {
                await createNewConversation();
            }
        }
    } catch (error) {
        console.error("[ChatHistory] 初始化会话状态失败:", error);
        showWelcomeMessage();
    }
}

// 重置会话列表分页状态
function resetConversationListPagination() {
    currentConversationPage = 0;
    hasMoreConversations = true;
    if (elements.conversationsList) {
        elements.conversationsList.innerHTML = ''; // 清空列表
        delete elements.conversationsList.dataset.noMoreConversations;
    }
}

// 加载会话列表
async function loadConversationList(page = 0, size = 30) {
    if (isLoadingConversationList || !hasMoreConversations || !elements.conversationsList) return;

    isLoadingConversationList = true;

    let loadingIndicator = null;
    if (page > 0) {
        loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'conversation-loading-indicator text-center text-muted small py-2';
        loadingIndicator.innerHTML = '<span>加载更多会话...</span>';
        elements.conversationsList.appendChild(loadingIndicator);
    }

    try {
        const response = await fetch(`/wkg/api/conversations?page=${page}&size=${size}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        const conversations = data.content || [];
        const totalPages = data.totalPages || 1;

        hasMoreConversations = page < totalPages - 1;
        if (!hasMoreConversations && elements.conversationsList) {
            elements.conversationsList.dataset.noMoreConversations = "true";
        }

        if (loadingIndicator) loadingIndicator.remove();

        // 添加历史会话
        appendHistoryConversations(conversations);

        currentConversationPage = page;

        // 确保当前会话项高亮
        if (currentConversationId) {
            activateConversationItem(currentConversationId);
        }

        // 显示空列表提示
        checkAndShowEmptyIndicator();

    } catch (error) {
        console.error(`加载会话列表第${page}页失败:`, error);
        if (loadingIndicator) loadingIndicator.remove();
        if (page > 0) {
            showConversationLoadingError(page, size);
        }
    } finally {
        isLoadingConversationList = false;
    }
}

// 添加历史会话到列表
function appendHistoryConversations(conversations) {
    if (!elements.conversationsList || !conversations || !conversations.length) return;

    conversations.forEach(conv => {
        const title = conv.title && conv.title.trim() !== '' ?
            conv.title :
            `对话 ${formatDate(conv.createTime)}`;

        const isActive = parseInt(conv.id) === parseInt(currentConversationId);

        addConversationItem(conv.id, title, isActive);
    });
}

// 创建会话项元素
function createConversationItemElement(id, title, isActive) {
    const item = document.createElement('div');
    item.className = `conversation-item ${isActive ? 'active' : ''}`;
    item.dataset.conversationId = id;

    // 主链接区域
    const mainLink = document.createElement('div');
    mainLink.className = 'conversation-main-link w-100 d-flex align-items-center';

    // 图标和标题
    const iconEl = document.createElement('i');
    iconEl.className = 'bi bi-chat-dots me-2';

    const titleEl = document.createElement('span');
    titleEl.className = 'conversation-title';
    titleEl.textContent = title;

    mainLink.appendChild(iconEl);
    mainLink.appendChild(titleEl);

    // 添加操作按钮容器
    const optionsContainer = document.createElement('div');
    optionsContainer.className = 'conversation-options-dropdown';
    const dropdownId = `conv-options-${id}`;

    // 使用更简单的按钮结构
    optionsContainer.innerHTML = `
        <button class="btn btn-sm conversation-options-btn" type="button" id="${dropdownId}" data-bs-toggle="dropdown" aria-expanded="false" title="更多选项">
            <i class="bi bi-three-dots-vertical"></i>
        </button>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="${dropdownId}">
            <li><a class="dropdown-item rename-conv-item" href="#"><i class="bi bi-pencil-square me-2"></i>重命名</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item delete-conv-item text-danger" href="#"><i class="bi bi-trash me-2"></i>删除</a></li>
        </ul>
    `;

    // 阻止按钮和下拉菜单的点击事件冒泡
    const dropdownButton = optionsContainer.querySelector('.conversation-options-btn');
    const dropdownMenu = optionsContainer.querySelector('.dropdown-menu');

    // 监听下拉菜单的显示和隐藏事件，管理 z-index 类
    if (dropdownButton) {
        dropdownButton.addEventListener('show.bs.dropdown', () => {
            item.classList.add('z-index-high');
        });
        dropdownButton.addEventListener('hide.bs.dropdown', () => {
            // 延迟移除，确保模态框能正常触发
            setTimeout(() => {
                // 检查模态框是否正在显示，如果是，则不移除 z-index
                if (!document.querySelector('.modal.show')) {
                    item.classList.remove('z-index-high');
                }
            }, 150); // 稍作延迟
        });
        dropdownButton.addEventListener('click', (e) => {
            e.stopPropagation();
            e.preventDefault();
        });
    }

    // 绑定重命名事件
    const renameItem = optionsContainer.querySelector('.rename-conv-item');
    renameItem?.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        item.classList.add('z-index-high'); // 确保点击时提升层级
        showRenameModal(id, title).finally(() => {
            // 模态框关闭后（无论成功或取消），移除层级提升
            // 但要检查下拉菜单是否仍然打开 (虽然通常不会)
            const ddInstance = bootstrap.Dropdown.getInstance(dropdownButton);
            if (!ddInstance || !ddInstance._isShown()) {
                item.classList.remove('z-index-high');
            }
        });
        const ddInstance = bootstrap.Dropdown.getInstance(dropdownButton);
        ddInstance?.hide();
    });

    // 绑定删除事件
    const deleteItem = optionsContainer.querySelector('.delete-conv-item');
    deleteItem?.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        item.classList.add('z-index-high'); // 确保点击时提升层级
        const currentTitle = item.querySelector('.conversation-title').textContent;
        showDeleteModal(id, currentTitle).finally(() => {
            const ddInstance = bootstrap.Dropdown.getInstance(dropdownButton);
            if (!ddInstance || !ddInstance._isShown()) {
                item.classList.remove('z-index-high');
            }
        });
        const ddInstance = bootstrap.Dropdown.getInstance(dropdownButton);
        ddInstance?.hide();
    });

    // 将操作按钮添加到外层，而不是mainLink
    item.appendChild(mainLink);
    item.appendChild(optionsContainer);

    // 为整个会话项添加点击事件
    item.addEventListener('click', (e) => {
        // 确保不是点击了下拉菜单相关元素
        if (!e.target.closest('.conversation-options-dropdown')) {
            loadConversation(id);
        }
    });

    return item;
}

// 添加会话项到列表
function addConversationItem(id, title, isActive, prepend = false) {
    if (!elements.conversationsList) return;

    // 检查是否已存在
    const existingItem = elements.conversationsList.querySelector(`.conversation-item[data-conversation-id="${id}"]`);
    if (existingItem) {
        if (isActive) {
            activateConversationItem(id);
        }
        return existingItem;
    }

    const item = createConversationItemElement(id, title, isActive);

    if (prepend && elements.conversationsList.firstChild) {
        elements.conversationsList.insertBefore(item, elements.conversationsList.firstChild);
    } else {
        elements.conversationsList.appendChild(item);
    }

    return item;
}

// 激活会话项
function activateConversationItem(conversationId) {
    if (!elements.conversationsList) return;

    // 清除所有激活状态
    const allItems = elements.conversationsList.querySelectorAll('.conversation-item');
    allItems.forEach(item => item.classList.remove('active'));

    // 设置当前项激活
    const currentItem = elements.conversationsList.querySelector(`.conversation-item[data-conversation-id="${conversationId}"]`);
    if (currentItem) {
        currentItem.classList.add('active');
        currentConversationId = conversationId;
    }
}

// 创建新会话
async function createNewConversation() {
    try {
        if (isGuestMode) {
            // 游客模式下直接显示欢迎消息
            currentConversationId = null;
            showWelcomeMessage();
            return;
        }

        // 创建新会话
        const response = await fetch('/wkg/api/conversations', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ title: '新对话' })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // 兼容两种可能的响应格式
        let conversationId;
        if (data.data && data.data.id) {
            // 嵌套在data字段中的情况
            conversationId = data.data.id;
        } else if (data.id) {
            // 直接返回会话对象的情况
            conversationId = data.id;
        } else {
            throw new Error('创建会话失败: 服务器未返回会话ID');
        }

        // 添加到列表
        const title = data.title || data.data?.title || '新对话';
        const item = addConversationItem(conversationId, title, true, true);

        // 加载会话 (显示欢迎消息)
        await loadConversation(conversationId, true);

        // 更新URL
        if (window.history && typeof window.history.replaceState === 'function') {
            const url = new URL(window.location.href);
            url.searchParams.set('conversationId', conversationId);
            window.history.replaceState(null, '', url.toString());
        }

    } catch (error) {
        console.error('创建新会话失败:', error);
        showToast('创建新会话失败，请稍后重试', 'error');
    }
}

// 加载会话
async function loadConversation(conversationId, isNew = false) {
    try {
        // 先高亮当前会话项
        activateConversationItem(conversationId);

        // 通知父窗口更新当前会话ID (如果在iframe中)
        if (window.parent && window.parent !== window) {
            window.parent.postMessage({
                type: 'UPDATE_CURRENT_CONVERSATION',
                payload: { conversationId: conversationId }
            }, '*');
        }

        // 加载会话消息
        await loadMessagesForConversation(conversationId, isNew);

        // 更新URL
        if (window.history && typeof window.history.replaceState === 'function') {
            const url = new URL(window.location.href);
            url.searchParams.set('conversationId', conversationId);
            window.history.replaceState(null, '', url.toString());
        }

    } catch (error) {
        console.error(`加载会话${conversationId}失败:`, error);
        showToast("加载会话失败，请稍后重试", "error");
    }
}

// 设置会话列表滚动监听
function setupConversationListScrollListener() {
    if (!elements.conversationsList) return;
    if (elements.conversationsList._scrollListenerAttached) return;

    elements.conversationsList.addEventListener('scroll', () => {
        const { scrollTop, scrollHeight, clientHeight } = elements.conversationsList;
        if (scrollTop + clientHeight > scrollHeight - 100 &&
            !isLoadingConversationList &&
            !elements.conversationsList.dataset.noMoreConversations) {
            loadConversationList(currentConversationPage + 1);
        }
    });

    elements.conversationsList._scrollListenerAttached = true;
}

// 显示会话加载错误提示
function showConversationLoadingError(page, size) {
    if (!elements.conversationsList) return;

    const errorElem = document.createElement('div');
    errorElem.className = 'conversation-error-indicator text-center text-danger small py-2 mx-2';
    errorElem.innerHTML = '<span>加载失败，点击重试</span>';
    errorElem.style.cursor = 'pointer';
    errorElem.addEventListener('click', () => {
        errorElem.remove();
        loadConversationList(page, size);
    });

    elements.conversationsList.appendChild(errorElem);
}

// 检查并显示空列表提示
function checkAndShowEmptyIndicator() {
    if (!elements.conversationsList) return;

    // 移除旧的空列表提示
    const oldIndicator = elements.conversationsList.querySelector('.conversation-empty-indicator');
    if (oldIndicator) oldIndicator.remove();

    // 检查是否有会话项
    const hasItems = elements.conversationsList.querySelector('.conversation-item') !== null;

    if (!hasItems && !isLoadingConversationList) {
        const emptyElem = document.createElement('div');
        emptyElem.className = 'conversation-empty-indicator text-center text-muted small py-3 mx-2';
        emptyElem.innerHTML = '<span>暂无会话，点击右上角"+"创建</span>';
        elements.conversationsList.appendChild(emptyElem);
    }
}

// 显示欢迎消息 (调用message-handler.js的方法)
function showWelcomeMessage() {
    // 这里应该由message-handler.js的相应方法处理
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        chatMessages.innerHTML = '<div class="message welcome-message"><p>你好！我是AI助手，有什么可以帮你的吗？</p></div>';
    }
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getMonth() + 1}月${date.getDate()}日`;
}

// HTML转义工具
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// 显示重命名Modal
async function showRenameModal(conversationId, currentTitle) {
    const item = elements.conversationsList?.querySelector(`.conversation-item[data-conversation-id="${conversationId}"]`);
    item?.classList.add('z-index-high'); // 确保显示时层级高
    
    return ModalComponent.show({ // 返回 Promise
        modalId: 'renameConversationModal',
        title: '重命名对话',
        size: null, // 使用默认大小，与知识库保持一致
        bodyHtml: `
            <form id="renameConversationForm">
                <div class="mb-3">
                    <label for="renameConversationTitleInput" class="form-label">对话名称</label>
                    <input type="text" class="form-control form-control-sm" id="renameConversationTitleInput" value="${escapeHtml(currentTitle)}"
                           placeholder="请输入新标题 (最多20个字符)" maxlength="20" required>
                    <div class="invalid-feedback">请输入有效的对话名称</div>
                </div>
            </form>
        `,
        confirmText: '保存',
        confirmButtonClass: 'btn-primary btn-sm',
        cancelText: '取消',
        cancelButtonClass: 'btn-secondary btn-sm',
        backdrop: 'static', // 点击外部不关闭
        backdropZIndex: 1050, // 设置backdrop的z-index
        keyboard: true,
        modalClass: 'modal-unified modal-rename',
        onConfirm: async (modalInstance) => {
            const input = document.getElementById('renameConversationTitleInput');
            const newTitle = input.value.trim();
            if (!newTitle) {
                input.classList.add('is-invalid');
                return false; 
            }
            input.classList.remove('is-invalid');
            let success = false;
            if (newTitle !== currentTitle) {
                 try {
                     await updateConversationTitle(conversationId, newTitle);
                     success = true;
                 } catch (e) {
                     console.error("Update title failed in modal confirm:", e);
                     success = false; // 即使失败也要关闭模态框
                 }
            }
             return true; // 总是允许关闭
        },
        onHidden: () => { // Modal 隐藏后执行
            item?.classList.remove('z-index-high');
        },
        onShown: () => { // Modal 显示后执行
            const inputElement = document.getElementById('renameConversationTitleInput');
            if (inputElement) {
                inputElement.focus();
                inputElement.select();
                inputElement.addEventListener('input', () => inputElement.classList.remove('is-invalid'));
            }
        }
    });
}

// 显示删除Modal
async function showDeleteModal(conversationId, title) {
    const item = elements.conversationsList?.querySelector(`.conversation-item[data-conversation-id="${conversationId}"]`);
    item?.classList.add('z-index-high'); // 确保显示时层级高

    return ModalComponent.show({ // 返回 Promise
        modalId: 'deleteConversationModal',
        title: '确认删除',
        size: null, // 使用默认大小，与知识库保持一致
        bodyHtml: `
            <p>确定要删除对话 <strong>"${escapeHtml(title)}"</strong> 吗？</p>
            <p class="text-muted small">此操作无法撤销。</p>
        `,
        confirmText: '删除',
        confirmButtonClass: 'btn-danger btn-sm',
        cancelText: '取消',
        cancelButtonClass: 'btn-secondary btn-sm',
        backdrop: 'static',
        backdropZIndex: 1050, // 设置backdrop的z-index
        keyboard: true,
        modalClass: 'modal-unified modal-delete',
        onConfirm: async () => {
            try {
                 await deleteConversation(conversationId);
             } catch (e) {
                 console.error("Delete conversation failed in modal confirm:", e);
             }
            return true; // 总是允许关闭
        },
        onHidden: () => { // Modal 隐藏后执行
            item?.classList.remove('z-index-high');
        }
    });
}

// 更新会话标题
async function updateConversationTitle(conversationId, title) {
    if (!conversationId || typeof title !== 'string') return;
    const trimmedTitle = title.trim();
    if (!trimmedTitle) {
        showToast("标题不能为空", "warning");
        return;
    }
    const finalTitle = trimmedTitle.length > 20 ? trimmedTitle.substring(0, 20) : trimmedTitle;

    try {
        const response = await fetch(`/wkg/api/conversations/${conversationId}/title`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ title: finalTitle })
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }
        // 更新UI
        const conversationItemTitle = elements.conversationsList?.querySelector(`.conversation-item[data-conversation-id="${conversationId}"] .conversation-title`);
        if (conversationItemTitle) {
            conversationItemTitle.textContent = finalTitle;
        }
    } catch (error) {
        console.error(`更新会话 ${conversationId} 标题失败:`, error);
        showToast(`更新标题失败: ${error.message}`, 'error');
    }
}

// 删除会话
async function deleteConversation(conversationId) {
    try {
        const response = await fetch(`/wkg/api/conversations/${conversationId}`, {
            method: 'DELETE'
        });
        if (!response.ok) {
            let errorMsg = `HTTP error! status: ${response.status}`;
            try { errorMsg = (await response.json()).message || errorMsg; } catch(e){}
            throw new Error(errorMsg);
        }

        // 从 UI 移除
        elements.conversationsList?.querySelector(`.conversation-item[data-conversation-id="${conversationId}"]`)?.remove();

        // 如果删除的是当前会话，切换到其他会话或新建
        if (currentConversationId == conversationId) {
            currentConversationId = null;
            const nextItem = elements.conversationsList?.querySelector('.conversation-item');
            if (nextItem?.dataset.conversationId) {
                loadConversation(nextItem.dataset.conversationId);
            } else {
                createNewConversation();
            }
        } else {
            checkAndShowEmptyIndicator(); // 检查列表是否空了
        }
    } catch (error) {
        console.error(`删除会话 ${conversationId} 失败:`, error);
        showToast(`删除失败: ${error.message}`, 'error');
    }
}

// 检查并根据首次消息自动更新标题
async function checkAndUpdateConversationTitle(conversationId, firstMessageText) {

    
    if (isGuestMode || !conversationId || !firstMessageText || !elements.conversationsList) {

        return;
    }

    try {
        const currentItem = elements.conversationsList.querySelector(`.conversation-item[data-conversation-id="${conversationId}"]`);
        const currentTitleElement = currentItem?.querySelector('.conversation-title');
        

        
        if (!currentTitleElement) {

            return;
        }

        const currentTitle = currentTitleElement.textContent;
        const isDefaultTitle = currentTitle === '新对话' || currentTitle.startsWith('新对话 ') ||
                              currentTitle.startsWith('对话 ') || /^对话 \d{1,2}月\d{1,2}日$/.test(currentTitle);



        if (isDefaultTitle) {

            let newTitle = firstMessageText.substring(0, 17).trim();
            if (firstMessageText.length > 15) newTitle += '...';
            if (newTitle) {

                await updateConversationTitle(conversationId, newTitle);
            }
        } else {

        }
    } catch (error) {
        console.error("检查并更新会话标题时出错:", error);
    }
}

// 导出功能接口
export {
    initialize,
    createNewConversation,
    loadConversation,
    showWelcomeMessage,
    checkAndUpdateConversationTitle,
    checkUserLoginStatus,
    showRenameModal,
    showDeleteModal,
    updateConversationTitle,
    deleteConversation
};

// 为了支持从其他JS文件直接调用，将关键函数设置为全局对象
// 这样conversation-manager.js等文件可以访问这些功能
window.chatHistory = {
    showRenameModal,
    showDeleteModal,
    updateConversationTitle,
    deleteConversation,
    checkAndUpdateConversationTitle
};