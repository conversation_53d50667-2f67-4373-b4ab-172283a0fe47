// src/main/resources/static/js/features/permission/roles.js

document.addEventListener('DOMContentLoaded', () => {
    

    const roleTableBody = document.getElementById('roleTableBody');
    const paginationContainer = document.getElementById('paginationContainer');
    const addRoleModalElement = document.getElementById('addRoleModal');
    const addRoleModal = new bootstrap.Modal(addRoleModalElement);
    const editRoleModalElement = document.getElementById('editRoleModal');
    const editRoleModal = new bootstrap.Modal(editRoleModalElement);
    const assignPermissionModalElement = document.getElementById('assignPermissionModal');
    const assignPermissionModal = new bootstrap.Modal(assignPermissionModalElement);

    const addRoleForm = document.getElementById('addRoleForm');
    const editRoleForm = document.getElementById('editRoleForm');
    const assignPermissionForm = document.getElementById('assignPermissionForm');

    let currentPage = 1;
    const pageSize = 10;
    let currentFilters = {};

    // --- Function to fetch role data --- 
    async function fetchRoles(page = 1, filters = {}) {
        currentPage = page;
        currentFilters = filters;
        const params = new URLSearchParams({
            current: page,
            size: pageSize,
            ...filters
        });
        roleTableBody.innerHTML = `<tr><td colspan="7" class="text-center p-5"><div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div></td></tr>`;
        paginationContainer.innerHTML = '<span class="text-muted small">Loading...</span>';
        try {
            const response = await fetch(`/wkg/api/permission/roles?${params.toString()}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            renderTable(data.records || []);
            renderPagination(data);
        } catch (error) {
            console.error('Error fetching roles:', error);
            showToast('Error fetching role list.', 'error');
            roleTableBody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">Failed to load roles.</td></tr>';
            paginationContainer.innerHTML = '<span class="text-danger small">Failed to load</span>';
        }
    }

    // --- Function to render the role table --- 
    function renderTable(roles) {
        roleTableBody.innerHTML = '';
        if (!roles || roles.length === 0) {
            roleTableBody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No roles found.</td></tr>';
            return;
        }
        roles.forEach(role => {
            const row = document.createElement('tr');
            const statusBadgeClass = role.status === 1 ? 'bg-success-subtle text-success-emphasis' : 'bg-danger-subtle text-danger-emphasis';
            const statusText = role.status === 1 ? '启用' : '禁用';
            row.innerHTML = `
                <td class="ps-3"><input type="checkbox" class="form-check-input role-checkbox" data-role-id="${role.id}"></td>
                <td>${role.roleName || '-'}</td>
                <td><span class="badge bg-light text-dark border">${role.roleCode || '-'}</span></td>
                <td class="text-muted small">${role.description || '-'}</td>
                <td><small>${role.createTime ? new Date(role.createTime).toLocaleString() : '-'}</small></td>
                <td><span class="badge ${statusBadgeClass}">${statusText}</span></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary btn-edit" data-role-id="${role.id}" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-delete" data-role-id="${role.id}" data-role-name="${role.roleName}" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                        <button class="btn btn-outline-info btn-assign-permission" data-role-id="${role.id}" data-role-name="${role.roleName}" title="分配权限">
                            <i class="bi bi-shield-check"></i> 权限
                        </button>
                    </div>
                </td>
            `;
            roleTableBody.appendChild(row);
        });
        addTableButtonListeners();
    }

    // --- Function to render pagination --- 
    function renderPagination(pageData) {
        paginationContainer.innerHTML = '';
        const { current, pages, total } = pageData;
        if (!pages || pages <= 0) {
             paginationContainer.innerHTML = `<span class="text-muted small">无角色数据</span>`;
            return;
        }
         if (pages === 1 && total > 0) {
             paginationContainer.innerHTML = `<span class="text-muted small">共 ${total} 条记录</span>`;
             return;
        }
         if (pages === 1 && total === 0) {
              paginationContainer.innerHTML = `<span class="text-muted small">无角色数据</span>`;
              return;
         }
        let paginationHtml = `<span class="text-muted small me-3">共 ${total} 条记录</span>`;
        paginationHtml += '<ul class="pagination pagination-sm mb-0">';
        paginationHtml += `
            <li class="page-item ${current === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${current - 1}" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>
            </li>`;
        const maxPagesToShow = 5;
        const delta = Math.floor(maxPagesToShow / 2);
        let startPage = Math.max(1, current - delta);
        let endPage = Math.min(pages, current + delta);
         if (current - delta <= 1) endPage = Math.min(pages, maxPagesToShow);
         if (current + delta >= pages) startPage = Math.max(1, pages - maxPagesToShow + 1);
        if (startPage > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`;
            if (startPage > 2) paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `<li class="page-item ${i === current ? 'active' : ''}"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
        }
        if (endPage < pages) {
             if (endPage < pages - 1) paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${pages}">${pages}</a></li>`;
        }
        paginationHtml += `
            <li class="page-item ${current === pages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${current + 1}" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>
            </li>`;
        paginationHtml += '</ul>';
        paginationContainer.innerHTML = paginationHtml;
        paginationContainer.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const pageNum = parseInt(link.dataset.page);
                if (!isNaN(pageNum) && pageNum !== currentPage) {
                    fetchRoles(pageNum, currentFilters);
                }
            });
        });
    }

    // --- Function to add listeners to table action buttons --- 
    function addTableButtonListeners() {
        roleTableBody.querySelectorAll('.btn-edit').forEach(button => {
            button.addEventListener('click', handleEditClick);
        });
        roleTableBody.querySelectorAll('.btn-delete').forEach(button => {
            button.addEventListener('click', handleDeleteClick);
        });
        roleTableBody.querySelectorAll('.btn-assign-permission').forEach(button => {
            button.addEventListener('click', handleAssignPermissionClick);
        });
    }

    // --- Event Handlers --- 
    async function handleEditClick(event) {
        const roleId = event.currentTarget.dataset.roleId;

        editRoleForm.reset();
        editRoleForm.classList.remove('was-validated');
        try {
            const response = await fetch(`/wkg/api/permission/roles/${roleId}`);
            if (!response.ok) {
                 if (response.status === 404) throw new Error(`角色 ID ${roleId} 未找到.`);
                 throw new Error(`获取角色详情失败: ${response.status}`);
            }
            const roleData = await response.json();
            document.getElementById('editRoleId').value = roleData.id;
            document.getElementById('editRoleName').value = roleData.roleName || '';
            document.getElementById('editRoleCode').value = roleData.roleCode || '';
            document.getElementById('editRoleDescription').value = roleData.description || '';
            if (roleData.status === 0) {
                document.getElementById('editRoleStatusDisabled').checked = true;
            } else {
                document.getElementById('editRoleStatusEnabled').checked = true;
            }
            editRoleModal.show();
        } catch (error) {
            console.error('Error preparing edit role modal:', error);
            showToast(`错误: ${error.message}`, 'error');
        }
    }

    async function handleDeleteClick(event) {
        const roleId = event.currentTarget.dataset.roleId;
        const roleName = event.currentTarget.dataset.roleName;

        if (!confirm(`确定要删除角色 "${roleName}" 吗？删除后不可恢复，且关联用户的权限将受影响。`)) {
            return;
        }
        event.currentTarget.disabled = true;
        event.currentTarget.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
        try {
            const response = await fetch(`/wkg/api/permission/roles/${roleId}`, {
                method: 'DELETE',
                headers: { /* 'Sa-Token': ... */ }
            });
            if (response.ok) {
                showToast(`角色 "${roleName}" 删除成功!`, 'success');
                fetchRoles(currentPage, currentFilters);
            } else {
                const errorData = await response.json().catch(() => ({ message: '删除失败，可能有关联用户' }));
                console.error("Delete role error:", errorData);
                showToast(`错误: ${errorData.message || response.statusText}`, 'error');
                event.currentTarget.disabled = false;
                event.currentTarget.innerHTML = '<i class="bi bi-trash"></i>';
            }
        } catch (error) {
            console.error('Error deleting role:', error);
            showToast('发生意外错误，请稍后重试', 'error');
            event.currentTarget.disabled = false;
            event.currentTarget.innerHTML = '<i class="bi bi-trash"></i>';
        }
    }

    // --- Assign Permission --- 
    async function handleAssignPermissionClick(event) {
        const roleId = event.currentTarget.dataset.roleId;
        const roleName = event.currentTarget.dataset.roleName;
        const modalTitle = document.getElementById('assignPermissionRoleName');
        const hiddenRoleIdInput = document.getElementById('assignPermissionRoleId');
        const treeContainer = document.getElementById('permissionTreeContainer');
        const checkAllCheckbox = document.getElementById('checkAllPermissions');
        modalTitle.textContent = roleName;
        hiddenRoleIdInput.value = roleId;
        treeContainer.innerHTML = `<div class="text-center text-muted p-3"><div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div></div>`;
        assignPermissionModal.show();
        if(checkAllCheckbox) { checkAllCheckbox.checked = false; checkAllCheckbox.indeterminate = false; }
        try {
            const [permTreeResponse, rolePermsResponse] = await Promise.all([
                fetch('/wkg/api/permission/permissions/tree'),
                fetch(`/wkg/api/permission/roles/${roleId}/permissions`)
            ]);
            if (!permTreeResponse.ok || !rolePermsResponse.ok) throw new Error('Failed to load permission data.');
            const permissionTree = await permTreeResponse.json();
            const currentPermissionIds = await rolePermsResponse.json();
            renderPermissionTree(treeContainer, permissionTree, currentPermissionIds);
            assignPermissionModalElement.querySelector('button[type="submit"]').disabled = false;
        } catch (error) {
            console.error('Error loading permissions for assignment:', error);
            showToast(`Error loading permissions: ${error.message}`, 'error');
            treeContainer.innerHTML = '<p class="text-danger text-center">Failed to load permissions.</p>';
            assignPermissionModalElement.querySelector('button[type="submit"]').disabled = true;
        }
    }

    function renderPermissionTree(container, treeData, checkedIds) {
        container.innerHTML = '';
        if (!treeData || treeData.length === 0) {
           container.innerHTML = '<p class="text-muted text-center">没有可用的权限项。</p>';
           return;
        }
        function buildTreeHtml(nodes, level = 0) {
           let html = '<div class="permission-level">';
           nodes.forEach(node => {
               const isChecked = checkedIds.includes(node.id);
               const hasChildren = node.children && node.children.length > 0;
               const inputId = `perm-${node.id}`;
               const parentAttr = node.parentId ? `data-parent-ref="perm-${node.parentId}"` : '';
               html += `
                   <div class="form-check mb-1 permission-node" style="padding-left: ${1.5 + level * 1.25}rem;" data-level="${level}" id="node-${inputId}">
                       <input class="form-check-input permission-checkbox" 
                              type="checkbox" 
                              value="${node.id}" 
                              id="${inputId}" 
                              name="permissionIds" 
                              ${isChecked ? 'checked' : ''} 
                              ${parentAttr} 
                              ${hasChildren ? 'data-has-children="true"' : ''}>
                       <label class="form-check-label" for="${inputId}">
                           ${node.permissionName || 'N/A'} 
                           <small class="text-muted">(${node.permissionCode || '-'})</small>
                            <span class="badge bg-light text-dark border ms-1">${node.type || '-'}</span>
                       </label>
                   </div>
               `;
               if (hasChildren) {
                   html += buildTreeHtml(node.children, level + 1);
               }
           });
            html += '</div>';
           return html;
        }
        container.innerHTML = buildTreeHtml(treeData);
        addPermissionCheckboxListeners(container);
        // Initialize parent states after render
        container.querySelectorAll('input[data-has-children="true"]').forEach(cb => {
            const parentId = cb.dataset.parentRef;
            if(parentId) updateParentCheckboxState(parentId);
        });
        updateCheckAllState();
    }

    function addPermissionCheckboxListeners(container) {
        container.querySelectorAll('.permission-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', handlePermissionCheckboxChange);
        });
        const checkAllCheckbox = document.getElementById('checkAllPermissions');
        if (checkAllCheckbox) {
            checkAllCheckbox.addEventListener('change', (event) => {
                container.querySelectorAll('.permission-checkbox').forEach(cb => {
                     if (cb.checked !== event.target.checked) {
                          cb.checked = event.target.checked;
                     }
                     cb.indeterminate = false;
                });
                 updateCheckAllState(); 
            });
        }
    }

    function handlePermissionCheckboxChange(event) {
        const changedCheckbox = event.target;
        const isChecked = changedCheckbox.checked;
        changedCheckbox.indeterminate = false; 
        checkDescendants(changedCheckbox, isChecked);
        const parentId = changedCheckbox.dataset.parentRef;
        if (parentId) {
           updateParentCheckboxState(parentId);
        }
        updateCheckAllState();
    }

    function checkDescendants(checkbox, checkedState) {
       const children = assignPermissionForm.querySelectorAll(`input[data-parent-ref="${checkbox.id}"]`);
       children.forEach(child => {
           if (child.checked !== checkedState || child.indeterminate) {
                child.checked = checkedState;
                child.indeterminate = false;
                checkDescendants(child, checkedState);
           }
       });
    }

    function updateParentCheckboxState(parentId) {
        const parentCheckbox = document.getElementById(parentId);
        if (!parentCheckbox) return;
        const siblings = assignPermissionForm.querySelectorAll(`input[data-parent-ref="${parentId}"]`);
        if (siblings.length === 0) {
             // If a node has data-has-children but no children rendered (e.g., filtered tree), treat it as leaf for parent state
             parentCheckbox.indeterminate = false; 
            return;
        }
        let allChecked = true;
        let noneChecked = true;
        let hasIndeterminate = false;
        siblings.forEach(sibling => {
            if (sibling.checked) noneChecked = false;
            if (!sibling.checked) allChecked = false;
            if (sibling.indeterminate) hasIndeterminate = true;
        });
        if (allChecked && !hasIndeterminate) {
            parentCheckbox.checked = true;
            parentCheckbox.indeterminate = false;
        } else if (noneChecked && !hasIndeterminate) {
            parentCheckbox.checked = false;
            parentCheckbox.indeterminate = false;
        } else {
            parentCheckbox.checked = false; 
            parentCheckbox.indeterminate = true;
        }
        const grandparentId = parentCheckbox.dataset.parentRef;
        if (grandparentId) {
           updateParentCheckboxState(grandparentId);
        }
    }

    function updateCheckAllState() {
       const checkAllCheckbox = document.getElementById('checkAllPermissions');
       if (!checkAllCheckbox) return;
       const allCheckboxes = assignPermissionForm.querySelectorAll('.permission-checkbox');
       const totalCount = allCheckboxes.length;
       if (totalCount === 0) {
           checkAllCheckbox.checked = false;
           checkAllCheckbox.indeterminate = false;
           return;
       }
       let checkedCount = 0;
       let indeterminateExists = false;
       allCheckboxes.forEach(cb => {
            if (cb.checked) checkedCount++;
            if (cb.indeterminate) indeterminateExists = true;
       });
       if (checkedCount === 0 && !indeterminateExists) {
           checkAllCheckbox.checked = false;
           checkAllCheckbox.indeterminate = false;
       } else if (checkedCount === totalCount && !indeterminateExists) {
           checkAllCheckbox.checked = true;
           checkAllCheckbox.indeterminate = false;
       } else {
           checkAllCheckbox.checked = false;
           checkAllCheckbox.indeterminate = true;
       }
    }

    // --- Handle Add Role Modal Show --- 
    addRoleModalElement?.addEventListener('show.bs.modal', () => {
        addRoleForm.reset();
        addRoleForm.classList.remove('was-validated');
    });

    // --- Add Role Form Submission --- 
    addRoleForm?.addEventListener('submit', async (event) => {
        event.preventDefault();
        event.stopPropagation();
        if (!addRoleForm.checkValidity()) {
             addRoleForm.classList.add('was-validated');
             const firstInvalidField = addRoleForm.querySelector(':invalid');
             firstInvalidField?.focus();
             firstInvalidField?.reportValidity();
             return;
        }
        addRoleForm.classList.add('was-validated');
        const formData = new FormData(addRoleForm);
        const roleData = Object.fromEntries(formData.entries());
        roleData.status = parseInt(roleData.status || '1');

        const submitButton = addRoleForm.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
        try {
            const response = await fetch('/wkg/api/permission/roles', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' /*, 'Sa-Token': ... */ },
                body: JSON.stringify(roleData),
            });
            if (response.ok) {
                addRoleModal.hide();
                showToast('角色添加成功!', 'success');
                fetchRoles(1);
            } else {
                const errorData = await response.json().catch(() => ({ message: '添加角色失败，请检查角色编码是否重复' }));
                console.error("Add role error:", errorData);
                showToast(`错误: ${errorData.message || response.statusText}`, 'error');
            }
        } catch (error) {
            console.error('Error submitting new role:', error);
            showToast('发生意外错误，请稍后重试', 'error');
        } finally {
            submitButton.disabled = false;
            submitButton.innerHTML = '保存';
        }
    });

    // --- Edit Role Form Submission --- 
    editRoleForm?.addEventListener('submit', async (event) => {
         event.preventDefault();
         event.stopPropagation();
         if (!editRoleForm.checkValidity()) {
              editRoleForm.classList.add('was-validated');
              const firstInvalidField = editRoleForm.querySelector(':invalid');
              firstInvalidField?.focus();
              firstInvalidField?.reportValidity();
              return;
         }
         editRoleForm.classList.add('was-validated');
         const formData = new FormData(editRoleForm);
         const roleData = Object.fromEntries(formData.entries());
         roleData.id = parseInt(roleData.id);
         roleData.status = parseInt(roleData.status || '1');
 
         const submitButton = editRoleForm.querySelector('button[type="submit"]');
         submitButton.disabled = true;
         submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
         try {
             const response = await fetch(`/wkg/api/permission/roles/${roleData.id}`, {
                 method: 'PUT',
                 headers: { 'Content-Type': 'application/json' /*, 'Sa-Token': ... */ },
                 body: JSON.stringify(roleData),
             });
             if (response.ok) {
                 editRoleModal.hide();
                 showToast('角色信息更新成功!', 'success');
                 fetchRoles(currentPage, currentFilters);
             } else {
                 const errorData = await response.json().catch(() => ({ message: '更新角色失败，请检查输入' }));
                 console.error("Update role error:", errorData);
                 showToast(`错误: ${errorData.message || response.statusText}`, 'error');
             }
         } catch (error) {
             console.error('Error submitting role update:', error);
             showToast('发生意外错误，请稍后重试', 'error');
         } finally {
             submitButton.disabled = false;
             submitButton.innerHTML = '保存更改';
         }
     });
    
    // --- Assign Permission Form Submission ---
    assignPermissionForm?.addEventListener('submit', async (event) => {
        event.preventDefault();
        event.stopPropagation();
        const roleId = document.getElementById('assignPermissionRoleId').value;
        if (!roleId) {
             showToast('Error: Role ID not found.', 'error');
             return;
        }
        const selectedPermissionIds = []; 
         assignPermissionForm.querySelectorAll('input[type="checkbox"][name="permissionIds"]:checked').forEach(cb => {
            selectedPermissionIds.push(parseInt(cb.value));
            // Include indeterminate parents? Usually no, only explicitly checked ones.
         });

        const submitButton = assignPermissionForm.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
        try {
            const response = await fetch(`/wkg/api/permission/roles/${roleId}/permissions`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' /*, 'Sa-Token': ... */ },
                body: JSON.stringify(selectedPermissionIds)
            });
            if (response.ok) {
                assignPermissionModal.hide();
                showToast('权限分配成功!', 'success');
            } else {
                 const errorData = await response.json().catch(() => ({ message: '权限分配失败' }));
                 showToast(`错误: ${errorData.message || response.statusText}`, 'error');
            }
        } catch (error) {
             showToast('发生意外错误', 'error');
        } finally {
             submitButton.disabled = false;
             submitButton.innerHTML = '保存权限设置';
        }
    });

    // --- Toast Notification Helper - 使用全局组件 ---
    function showToast(message, type = 'info') {
        // 使用全局Toast组件显示消息
        window.parent?.postMessage({
            type: 'SHOW_TOAST',
            payload: { message: message, type: type }
        }, '*');
    }

    // --- Initial Load --- 
    fetchRoles(currentPage, currentFilters);

});