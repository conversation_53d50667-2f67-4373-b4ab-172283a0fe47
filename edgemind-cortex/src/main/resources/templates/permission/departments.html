<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部门管理</title>
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/features/permission/permission.css">
    <style>
        .department-tree-container {
            max-height: 600px;
            overflow-y: auto;
        }
        .tree-node {
            padding: 8px 0;
            border-radius: 4px;
            transition: all 0.2s;
        }
        .tree-node:hover {
            background-color: #f8f9fa;
        }
        .node-content {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        .node-toggle {
            width: 24px;
            text-align: center;
            cursor: pointer;
        }
        .node-icon {
            margin-right: 8px;
            color: #6c757d;
        }
        .node-text {
            flex-grow: 1;
        }
        .node-actions {
            display: none;
        }
        .tree-node:hover .node-actions {
            display: block;
        }
        .children {
            padding-left: 32px;
        }
        .dept-details {
            border-left: 1px solid #dee2e6;
            padding-left: 1.5rem;
        }
        .employee-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #6c757d;
        }
        .dept-info-card {
            transition: all 0.2s;
        }
        .dept-info-card:hover {
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <h2 class="mb-4 text-primary">部门管理</h2>
        
        <!-- 搜索与操作工具栏 -->
<!--        <div class="row toolbar-row">-->
<!--            <div class="col-md-8">-->
<!--                <div class="d-flex gap-2">-->
<!--                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDepartmentModal">-->
<!--                        <i class="bi bi-plus-lg"></i> 新增部门-->
<!--                    </button>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="col-md-4">-->
<!--            </div>-->
<!--        </div>-->
        
        <!-- 部门树与详情 -->
        <div class="row g-4">
            <!-- 左侧部门树 -->
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-header">
                         <i class="bi bi-diagram-3 me-2"></i>
                        <span class="fw-bold">部门结构</span>
                    </div>
                    <div class="card-body department-tree-container p-2" id="departmentTreeContainer">
                        <div class="text-center p-3">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧部门详情 -->
            <div class="col-md-8">
                <div id="departmentDetailsContainer">
                    <div class="card h-100 d-flex justify-content-center align-items-center">
                        <div class="text-center text-muted p-5">
                             <i class="bi bi-info-circle fs-2 mb-2"></i><br>
                             请在左侧选择一个部门查看详情
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 新增部门模态框 -->
    <div class="modal fade" id="addDepartmentModal" tabindex="-1" aria-labelledby="addDepartmentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addDepartmentModalLabel">新增部门</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addDepartmentForm">
                        <div class="mb-3">
                            <label for="addDepartmentName" class="form-label">部门名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="addDepartmentName" name="deptName" required>
                        </div>
                        <div class="mb-3">
                            <label for="addDepartmentCode" class="form-label">部门编码</label>
                            <input type="text" class="form-control" id="addDepartmentCode" name="deptCode">
                        </div>
                        <div class="mb-3">
                            <label for="addParentDepartment" class="form-label">上级部门</label>
                            <select class="form-select" id="addParentDepartment" name="parentId">
                                <option value="">无 (作为一级部门)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="addDepartmentManager" class="form-label">部门负责人</label>
                            <select class="form-select" id="addDepartmentManager" name="managerId">
                                <option value="">请选择</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="addDepartmentSort" class="form-label">显示顺序</label>
                                <input type="number" class="form-control" id="addDepartmentSort" name="sortOrder" value="1">
                            </div>
                             <div class="col-md-6 mb-3">
                                <label for="addDepartmentStatus" class="form-label">状态</label>
                                <select class="form-select" id="addDepartmentStatus" name="status">
                                    <option value="1" selected>正常</option>
                                    <option value="0">停用</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="addDepartmentDescription" class="form-label">部门职责</label>
                            <textarea class="form-control" id="addDepartmentDescription" name="description" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="addDepartmentForm" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 编辑部门模态框 -->
    <div class="modal fade" id="editDepartmentModal" tabindex="-1" aria-labelledby="editDepartmentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editDepartmentModalLabel">编辑部门</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editDepartmentForm">
                         <input type="hidden" id="editDepartmentId" name="id">
                         <div class="mb-3">
                            <label for="editDepartmentName" class="form-label">部门名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editDepartmentName" name="deptName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editDepartmentCode" class="form-label">部门编码</label>
                            <input type="text" class="form-control" id="editDepartmentCode" name="deptCode">
                        </div>
                        <div class="mb-3">
                            <label for="editParentDepartment" class="form-label">上级部门</label>
                            <select class="form-select" id="editParentDepartment" name="parentId">
                                <option value="">无 (作为一级部门)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editDepartmentManager" class="form-label">部门负责人</label>
                            <select class="form-select" id="editDepartmentManager" name="managerId">
                                <option value="">请选择</option>
                            </select>
                        </div>
                         <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editDepartmentSort" class="form-label">显示顺序</label>
                                <input type="number" class="form-control" id="editDepartmentSort" name="sortOrder">
                            </div>
                             <div class="col-md-6 mb-3">
                                <label for="editDepartmentStatus" class="form-label">状态</label>
                                <select class="form-select" id="editDepartmentStatus" name="status">
                                    <option value="1">正常</option>
                                    <option value="0">停用</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="editDepartmentDescription" class="form-label">部门职责</label>
                            <textarea class="form-control" id="editDepartmentDescription" name="description" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="editDepartmentForm" class="btn btn-primary">保存更改</button>
                </div>
            </div>
        </div>
    </div>

     <!-- Toast Container -->
     <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toastPlacement">
         <!-- Toasts will appear here -->
     </div>
    
    <!-- JavaScript -->
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script type="module" src="/wkg/js/features/permission/departments.js"></script>
</body>
</html> 