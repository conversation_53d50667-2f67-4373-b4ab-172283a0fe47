package com.zibbava.edgemind.cortex.manager;

import javax.swing.*;
import java.awt.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
import com.zibbava.edgemind.cortex.config.EnvironmentConfig;

/**
 * Ollama管理器 - 企业级AI模型管理
 * 负责Ollama的检测、安装、启动、停止和状态监控（纯Java实现）
 * 支持离线安装、架构检测、模型管理和智能故障排除
 *
 * <AUTHOR> Team
 * @version 2.0.0
 */
public class OllamaManager {

    // 常量定义
    private static final String OLLAMA_DEFAULT_PORT = "11434";
    private static final String OLLAMA_SERVICE_URL = "http://localhost:" + OLLAMA_DEFAULT_PORT;
    // 下载URL常量已移除，仅支持离线安装
    private static final int INSTALLATION_TIMEOUT_MINUTES = 10;
    private static final int SERVICE_START_TIMEOUT_SECONDS = 30;

    // GUI组件
    private static JLabel statusLabel;
    private static JProgressBar progressBar;
    private static boolean restartRequired = false;

    // 系统信息
    private static SystemArchitecture systemArch;
    private static String ollamaInstallPath;

    /**
     * 系统架构枚举
     */
    public enum SystemArchitecture {
        X64("x64", "amd64", "OllamaSetup.exe"),
        ARM64("arm64", "aarch64", "OllamaSetup-arm64.exe"),
        UNKNOWN("unknown", "unknown", "OllamaSetup.exe");

        private final String displayName;
        private final String javaArch;
        private final String installerName;

        SystemArchitecture(String displayName, String javaArch, String installerName) {
            this.displayName = displayName;
            this.javaArch = javaArch;
            this.installerName = installerName;
        }

        public String getDisplayName() { return displayName; }
        public String getJavaArch() { return javaArch; }
        public String getInstallerName() { return installerName; }
    }

    /**
     * Ollama离线文件信息类
     */
    private static class OllamaOfflineFiles {
        private File ollamaInstaller;
        private File modelsDir;
        // private File configFile; // Removed
        private boolean hasInstaller;
        private boolean hasModels;
        // private boolean hasConfig; // Removed
        private SystemArchitecture detectedArch;

        public OllamaOfflineFiles(File installer, File models, /* File config, */ SystemArchitecture arch) { // Config parameter removed
            this.ollamaInstaller = installer;
            this.modelsDir = models;
            // this.configFile = config; // Removed
            this.detectedArch = arch;
            this.hasInstaller = installer != null && installer.exists();
            this.hasModels = models != null && models.exists() && models.isDirectory();
            // this.hasConfig = config != null && config.exists(); // Removed
        }

        public boolean isComplete() {
            return hasInstaller; // 至少需要安装程序
        }

        public String getStatus() {
            return String.format("架构:%s 安装程序:%s 模型:%s", // Config status removed
                    detectedArch.getDisplayName(),
                    hasInstaller ? "✓" : "✗",
                    hasModels ? "✓" : "✗");
        }

        // Getters
        public File getOllamaInstaller() { return ollamaInstaller; }
        public File getModelsDir() { return modelsDir; }
        // public File getConfigFile() { return configFile; } // Removed
        public boolean hasInstaller() { return hasInstaller; }
        public boolean hasModels() { return hasModels; }
        // public boolean hasConfig() { return hasConfig; } // Removed
        public SystemArchitecture getDetectedArch() { return detectedArch; }
    }

    /**
     * Ollama状态信息类
     */
    public static class OllamaStatus {
        private boolean installed;
        private boolean running;
        private boolean serviceHealthy;
        private String version;
        private String models;
        private String installPath;
        private SystemArchitecture architecture;
        private int modelCount;
        private long totalModelSize;

        public OllamaStatus(boolean installed, boolean running, boolean serviceHealthy,
                            String version, String models, String installPath,
                            SystemArchitecture architecture, int modelCount, long totalModelSize) {
            this.installed = installed;
            this.running = running;
            this.serviceHealthy = serviceHealthy;
            this.version = version;
            this.models = models;
            this.installPath = installPath;
            this.architecture = architecture;
            this.modelCount = modelCount;
            this.totalModelSize = totalModelSize;
        }

        // Getters
        public boolean isInstalled() { return installed; }
        public boolean isRunning() { return running; }
        public boolean isServiceHealthy() { return serviceHealthy; }
        public String getVersion() { return version; }
        public String getModels() { return models; }
        public String getInstallPath() { return installPath; }
        public SystemArchitecture getArchitecture() { return architecture; }
        public int getModelCount() { return modelCount; }
        public long getTotalModelSize() { return totalModelSize; }

        public String getStatusSummary() {
            if (!installed) return "未安装";
            if (!running) return "已安装但未运行";
            if (!serviceHealthy) return "运行中但服务异常";
            return String.format("正常运行 (%d个模型)", modelCount);
        }
    }

    // 静态初始化
    static {
        systemArch = detectSystemArchitecture();
        detectOllamaInstallPath();
    }

    /**
     * 设置GUI组件引用
     * @param progressBar 进度条组件
     * @param statusLabel 状态标签组件
     */
    public static void setGuiComponents(JProgressBar progressBar, JLabel statusLabel) {
        OllamaManager.progressBar = progressBar;
        OllamaManager.statusLabel = statusLabel;
    }

    /**
     * 检测系统架构
     */
    private static SystemArchitecture detectSystemArchitecture() {
        String osArch = System.getProperty("os.arch").toLowerCase();

        if (osArch.contains("amd64") || osArch.contains("x86_64")) {
            return SystemArchitecture.X64;
        } else if (osArch.contains("aarch64") || osArch.contains("arm64")) {
            return SystemArchitecture.ARM64;
        } else {
            System.out.println("警告: 未知系统架构 " + osArch + "，默认使用x64");
            return SystemArchitecture.X64;
        }
    }

    /**
     * 检测Ollama安装路径
     */
    private static void detectOllamaInstallPath() {
        // 统一设置安装路径为基础目录下的Ollama文件夹
        String basePath = EnvironmentConfig.getBasePath();
        ollamaInstallPath = basePath + "\\Ollama";
        
        // 确保Ollama目录存在
        File ollamaDir = new File(ollamaInstallPath);
        if (!ollamaDir.exists()) {
            ollamaDir.mkdirs();
            System.out.println("已创建Ollama安装目录: " + ollamaInstallPath);
        }
        
        System.out.println("Ollama 安装路径已设置为: " + ollamaInstallPath);
    }
    
    /**
     * 获取Ollama可执行文件的完整路径
     * @return Ollama可执行文件路径，如果未找到则返回"ollama"
     */
    private static String getOllamaExecutablePath() {
        // 首先尝试从安装目录获取
        if (ollamaInstallPath != null) {
            String ollamaExePath = ollamaInstallPath + "\\ollama.exe";
            if (new File(ollamaExePath).exists()) {
                return ollamaExePath;
            }
        }
        
        // 尝试常见的安装位置
        String[] possiblePaths = {
            System.getProperty("user.home") + "\\AppData\\Local\\Programs\\Ollama\\ollama.exe",
            "C:\\Program Files\\Ollama\\ollama.exe",
            "C:\\Program Files (x86)\\Ollama\\ollama.exe",
            EnvironmentConfig.getBasePath() + "\\Ollama\\ollama.exe"
        };
        
        for (String path : possiblePaths) {
            if (new File(path).exists()) {
                System.out.println("找到Ollama可执行文件: " + path);
                return path;
            }
        }
        
        // 如果都没找到，返回默认命令（依赖环境变量）
        System.out.println("未找到Ollama可执行文件，使用默认命令");
        return "ollama";
    }

    /**
     * 检查并启动Ollama（主入口方法）
     * @return boolean 返回true表示Ollama运行正常，false表示存在问题
     */
    public static boolean checkAndStartOllama() {
        try {
            updateStatus("正在检查Ollama状态...");
            updateProgress(10);

            // 第一步：检查当前状态
            OllamaStatus currentStatus = getOllamaStatus();
            updateProgress(20);

            if (currentStatus.isInstalled() && currentStatus.isRunning() && currentStatus.isServiceHealthy()) {
                updateStatus("Ollama运行正常 - " + currentStatus.getStatusSummary());
                updateProgress(100);
                return true;
            }

            if (currentStatus.isInstalled()) {
                updateStatus("Ollama已安装，正在启动服务...");
                updateProgress(30);

                if (startOllamaService()) {
                    // 等待服务完全启动
                    Thread.sleep(3000);
                    OllamaStatus newStatus = getOllamaStatus();

                    if (newStatus.isServiceHealthy()) {
                        updateStatus("Ollama启动成功 - " + newStatus.getStatusSummary());
                        updateProgress(100);
                        return true;
                    }
                }

                updateStatus("Ollama启动失败，进行故障排除...");
                showOllamaServiceTroubleshootDialog();
                return false;
            }

            // 第二步：Ollama未安装，检查离线安装文件
            updateStatus("Ollama未安装，检查离线安装文件...");
            updateProgress(40);

            OllamaOfflineFiles offlineFiles = checkOllamaOfflineFiles();

            if (offlineFiles.isComplete()) {
                updateStatus("找到Ollama离线安装文件，开始安装...");
                return installOllamaOfflineAndVerify(offlineFiles);
            } else {
                updateStatus("Ollama离线文件不完整，提供安装选项");
                showOllamaInstallOptions(offlineFiles);
                return false;
            }

        } catch (Exception e) {
            System.err.println("检查和启动Ollama时出错: " + e.getMessage());
            e.printStackTrace();
            updateStatus("Ollama检查失败: " + e.getMessage());
            showErrorDialog("Ollama检查失败",
                    "检查Ollama状态时发生错误:\n" + e.getMessage() +
                            "\n\n请检查系统权限和网络连接。");
            return false;
        }
    }

    /**
     * 离线安装Ollama并验证结果
     * @param files 离线文件信息
     * @return boolean 安装是否成功
     */
    private static boolean installOllamaOfflineAndVerify(OllamaOfflineFiles files) {
        try {
            updateStatus("开始Ollama离线安装...");
            updateProgress(50);

            // 第一阶段：安装Ollama
            updateStatus("正在安装Ollama (" + files.getDetectedArch().getDisplayName() + ")...");
            updateProgress(60);

            if (!installOllama(files.getOllamaInstaller())) {
                updateStatus("Ollama安装失败");
                showErrorDialog("安装失败", "Ollama安装失败，请检查:\n• 安装程序是否完整\n• 是否有管理员权限\n• 磁盘空间是否充足");
                return false;
            }

            // 重新检测安装路径
            detectOllamaInstallPath();

            // 第二阶段：配置Ollama环境
            updateStatus("正在配置Ollama环境...");
            updateProgress(70);
            configureOllamaEnvironment();

            // 第三阶段：验证环境变量生效（configureOllamaEnvironment已经处理了重启）
            updateStatus("验证环境变量配置...");
            updateProgress(75);
            Thread.sleep(2000); // 确保环境变量完全生效

            // 第四阶段：启动Ollama服务
            updateStatus("正在启动Ollama服务...");
            updateProgress(80);

            if (!startOllamaService()) {
                updateStatus("Ollama服务启动失败");
                showOllamaServiceTroubleshootDialog();
                return false;
            }

            // 等待服务完全启动
            Thread.sleep(2000);

            // 第五阶段：加载模型（如果有）
            if (files.hasModels()) {
                updateStatus("正在加载Ollama模型...");
                updateProgress(90);
                loadOllamaModels(files.getModelsDir());
            }

            // 第六阶段：验证安装
            updateStatus("正在验证Ollama安装...");
            updateProgress(95);

            OllamaStatus finalStatus = getOllamaStatus();

            if (finalStatus.isInstalled() && finalStatus.isRunning() && finalStatus.isServiceHealthy()) {
                updateStatus("Ollama安装并配置完成！");
                updateProgress(100);
                showOllamaSuccessDialog(finalStatus);
                return true;
            } else {
                updateStatus("Ollama安装完成但存在问题");
                showOllamaPostInstallDialog(finalStatus);
                return false;
            }

        } catch (Exception e) {
            System.err.println("离线安装Ollama时发生错误: " + e.getMessage());
            e.printStackTrace();
            updateStatus("Ollama安装失败: " + e.getMessage());
            showErrorDialog("安装失败", "安装过程中发生错误:\n" + e.getMessage() +
                    "\n\n请检查系统权限和磁盘空间。");
            return false;
        }
    }

    /**
     * 安装Ollama
     */
    private static boolean installOllama(File installerFile) {
        try {
            updateStatus("正在执行Ollama安装程序...");

            // 使用静默安装参数并指定安装目录
            ProcessBuilder pb = new ProcessBuilder(
                    installerFile.getAbsolutePath(),
                    "/S",  // 静默安装参数
                    "/DIR=" + ollamaInstallPath  // 指定安装目录
            );

            // 设置工作目录
            pb.directory(installerFile.getParentFile());

            Process process = pb.start();

            // 监控安装进度
            CompletableFuture<Void> outputMonitor = CompletableFuture.runAsync(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                     BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {

                    String line;
                    while ((line = reader.readLine()) != null) {
                        System.out.println("Ollama安装: " + line);
                    }

                    while ((line = errorReader.readLine()) != null) {
                        System.err.println("Ollama安装警告: " + line);
                    }
                } catch (IOException e) {
                    System.err.println("读取安装输出时出错: " + e.getMessage());
                }
            });

            // 等待安装完成，最多等待指定时间
            boolean finished = process.waitFor(INSTALLATION_TIMEOUT_MINUTES, TimeUnit.MINUTES);

            if (!finished) {
                process.destroyForcibly();
                System.err.println("Ollama安装超时，强制终止安装进程");
                return false;
            }

            int exitCode = process.exitValue();

            if (exitCode == 0) {
                System.out.println("Ollama安装成功");
                return true;
            } else {
                System.err.println("Ollama安装失败，退出码: " + exitCode);
                return false;
            }

        } catch (Exception e) {
            System.err.println("安装Ollama时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 配置Ollama环境
     * OLLAMA_MODELS: 指定模型下载和存储路径
     * OLLAMA_HOST: 指定服务监听地址和端口
     */
    private static void configureOllamaEnvironment() {
        try {
            String basePath = EnvironmentConfig.getBasePath();
            String ollamaModelsPath = basePath + "\\ollama-models";

            // 创建模型目录
            Path modelsPath = Paths.get(ollamaModelsPath);
            Files.createDirectories(modelsPath);

            System.out.println("设置Ollama环境变量...");
            System.out.println("  OLLAMA_MODELS: " + ollamaModelsPath + " (模型存储路径)");
            System.out.println("  OLLAMA_HOST: 0.0.0.0:" + OLLAMA_DEFAULT_PORT + " (服务监听地址)");

            // 设置关键环境变量：模型路径和服务地址
            ProcessBuilder pb = new ProcessBuilder(
                    "powershell", "-Command",
                    "[Environment]::SetEnvironmentVariable('OLLAMA_MODELS', '" + ollamaModelsPath + "', 'User'); " +
                            "[Environment]::SetEnvironmentVariable('OLLAMA_HOST', '0.0.0.0:" + OLLAMA_DEFAULT_PORT + "', 'User')"
            );
            Process process = pb.start();
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);

            if (finished && process.exitValue() == 0) {
                System.out.println("Ollama环境变量设置成功");

                // 重要：停止现有的Ollama进程以确保环境变量生效
                System.out.println("正在停止现有Ollama进程以使环境变量生效...");
                stopOllama();
                
                // 等待进程完全停止
                Thread.sleep(3000);
                
                System.out.println("Ollama环境配置完成，模型将下载到: " + ollamaModelsPath);
                System.out.println("环境变量已生效，Ollama将在下次启动时使用新配置");
            } else {
                System.err.println("环境变量设置可能失败，请检查系统权限");
            }

        } catch (Exception e) {
            System.err.println("配置Ollama环境时发生错误: " + e.getMessage());
        }
    }

    /**
     * 启动Ollama服务
     */
    private static boolean startOllamaService() {
        try {
            updateStatus("正在启动Ollama服务...");

            // 检查端口是否被占用
            if (isPortInUse(Integer.parseInt(OLLAMA_DEFAULT_PORT))) {
                System.out.println("端口 " + OLLAMA_DEFAULT_PORT + " 已被占用，尝试终止占用进程");
                killProcessUsingPort(Integer.parseInt(OLLAMA_DEFAULT_PORT));
                Thread.sleep(2000);
            }

            // 获取Ollama可执行文件路径并启动服务
            String ollamaPath = getOllamaExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(ollamaPath, "serve");
            pb.redirectErrorStream(true);
            Process process = pb.start();

            // 等待服务启动
            for (int i = 0; i < SERVICE_START_TIMEOUT_SECONDS; i++) {
                Thread.sleep(1000);
                if (isOllamaServiceResponding()) {
                    System.out.println("Ollama服务启动成功");
                    return true;
                }
                updateStatus("等待Ollama服务启动... (" + (i + 1) + "/" + SERVICE_START_TIMEOUT_SECONDS + "s)");
            }

            System.err.println("Ollama服务启动超时");
            return false;

        } catch (Exception e) {
            System.err.println("启动Ollama服务时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查端口是否被占用
     */
    private static boolean isPortInUse(int port) {
        try {
            ProcessBuilder pb = new ProcessBuilder("netstat", "-an");
            Process process = pb.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;

            while ((line = reader.readLine()) != null) {
                if (line.contains(":" + port + " ") && line.contains("LISTENING")) {
                    return true;
                }
            }

            process.waitFor();
            return false;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 终止占用指定端口的进程
     */
    private static void killProcessUsingPort(int port) {
        try {
            ProcessBuilder pb = new ProcessBuilder("powershell", "-Command",
                    "Get-NetTCPConnection -LocalPort " + port + " | Select-Object -ExpandProperty OwningProcess | " +
                            "ForEach-Object { Stop-Process -Id $_ -Force -ErrorAction SilentlyContinue }");
            Process process = pb.start();
            process.waitFor(10, TimeUnit.SECONDS);

        } catch (Exception e) {
            System.err.println("终止端口占用进程时出错: " + e.getMessage());
        }
    }

    /**
     * 加载Ollama模型
     */
    private static void loadOllamaModels(File modelsDir) {
        try {
            File[] modelFiles = modelsDir.listFiles((dir, name) ->
                    name.toLowerCase().endsWith(".gguf") ||
                            name.toLowerCase().endsWith(".bin") ||
                            name.toLowerCase().endsWith(".safetensors"));

            if (modelFiles != null && modelFiles.length > 0) {
                updateStatus("发现 " + modelFiles.length + " 个模型文件");

                for (File modelFile : modelFiles) {
                    updateStatus("正在处理模型: " + modelFile.getName());

                    // 这里可以添加模型导入逻辑
                    // 由于Ollama模型导入比较复杂，这里只是记录
                    System.out.println("发现模型文件: " + modelFile.getName() +
                            " (大小: " + formatFileSize(modelFile.length()) + ")");
                }

                updateStatus("模型文件处理完成");
            } else {
                updateStatus("模型目录为空");
            }

        } catch (Exception e) {
            System.err.println("加载Ollama模型时发生错误: " + e.getMessage());
        }
    }

    /**
     * 格式化文件大小
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }

    /**
     * 显示Ollama安装选项
     */
    private static void showOllamaInstallOptions(OllamaOfflineFiles files) {
        SwingUtilities.invokeLater(() -> {
            String message = "Ollama离线安装文件检查结果：\n\n" + files.getStatus() + "\n\n";

            if (!files.hasInstaller()) {
                message += "缺少Ollama安装程序\n";
                message += "请下载 " + files.getDetectedArch().getInstallerName() + " 到项目目录（与exe同一目录）\n";
                message += "下载地址: https://ollama.com/download\n";
            }

            message += "\n请选择安装方式：";

            String[] options = {"重新检查文件", "手动安装", "跳过Ollama安装"};
            int choice = JOptionPane.showOptionDialog(
                    null,
                    message,
                    "Ollama安装选项",
                    JOptionPane.YES_NO_CANCEL_OPTION,
                    JOptionPane.QUESTION_MESSAGE,
                    null,
                    options,
                    options[0]
            );

            switch (choice) {
                case 0: // 重新检查
                    CompletableFuture.runAsync(() -> checkAndStartOllama());
                    break;
                case 1: // 手动安装
                    showManualInstallDialog();
                    break;
                case 2: // 跳过
                default:
                    updateStatus("跳过Ollama安装");
                    break;
            }
        });
    }

    /**
     * 显示手动安装对话框
     */
    private static void showManualInstallDialog() {
        SwingUtilities.invokeLater(() -> {
            String message = "手动安装Ollama步骤：\n\n" +
                    "1. 访问 https://ollama.com/download\n" +
                    "2. 下载适合您系统的Ollama安装程序\n" +
                    "   • " + systemArch.getDisplayName() + " 架构: " + systemArch.getInstallerName() + "\n" +
                    "3. 运行安装程序并按照提示完成安装\n" +
                    "4. 重启计算机（如果需要）\n" +
                    "5. 启动Ollama服务\n\n" +
                    "安装完成后，点击'重新检查'验证安装。";

            String[] options = {"打开下载页面", "重新检查", "关闭"};
            int choice = JOptionPane.showOptionDialog(
                    null,
                    message,
                    "手动安装Ollama",
                    JOptionPane.YES_NO_CANCEL_OPTION,
                    JOptionPane.INFORMATION_MESSAGE,
                    null,
                    options,
                    options[1]
            );

            switch (choice) {
                case 0: // 打开下载页面
                    try {
                        Desktop.getDesktop().browse(new java.net.URI("https://ollama.com/download"));
                    } catch (Exception e) {
                        System.err.println("无法打开浏览器: " + e.getMessage());
                    }
                    break;
                case 1: // 重新检查
                    CompletableFuture.runAsync(() -> checkAndStartOllama());
                    break;
                case 2: // 关闭
                default:
                    break;
            }
        });
    }

    /**
     * 显示Ollama服务故障排除对话框
     */
    private static void showOllamaServiceTroubleshootDialog() {
        SwingUtilities.invokeLater(() -> {
            String message = "Ollama服务启动失败。\n\n" +
                    "常见原因和解决方案：\n" +
                    "• 端口 " + OLLAMA_DEFAULT_PORT + " 被占用\n" +
                    "  → 检查其他程序是否占用此端口\n" +
                    "• 权限不足\n" +
                    "  → 以管理员身份运行程序\n" +
                    "• 防火墙阻止\n" +
                    "  → 检查Windows防火墙设置\n" +
                    "• 安装不完整\n" +
                    "  → 重新安装Ollama\n\n" +
                    "请选择解决方案：";

            String[] options = {"手动启动服务", "检查端口占用", "重新安装", "详细诊断", "跳过"};
            int choice = JOptionPane.showOptionDialog(
                    null,
                    message,
                    "Ollama服务故障排除",
                    JOptionPane.YES_NO_CANCEL_OPTION,
                    JOptionPane.WARNING_MESSAGE,
                    null,
                    options,
                    options[0]
            );

            switch (choice) {
                case 0: // 手动启动
                    manualStartOllama();
                    break;
                case 1: // 检查端口
                    checkPortUsage();
                    break;
                case 2: // 重新安装
                    CompletableFuture.runAsync(() -> reinstallOllama());
                    break;
                case 3: // 详细诊断
                    showDetailedDiagnosisDialog();
                    break;
                case 4: // 跳过
                default:
                    updateStatus("跳过Ollama启动");
                    break;
            }
        });
    }

    /**
     * 显示Ollama安装后问题对话框
     */
    private static void showOllamaPostInstallDialog(OllamaStatus status) {
        SwingUtilities.invokeLater(() -> {
            String message = "Ollama安装完成，但存在以下问题：\n\n";

            if (!status.isInstalled()) {
                message += "• 安装验证失败\n";
            }
            if (!status.isRunning()) {
                message += "• 服务未运行\n";
            }
            if (!status.isServiceHealthy()) {
                message += "• 服务响应异常\n";
            }

            message += "\n当前状态: " + status.getStatusSummary() + "\n\n";
            message += "建议的解决方案：";

            String[] options = {"重试启动", "重新安装", "手动配置", "查看日志", "跳过"};
            int choice = JOptionPane.showOptionDialog(
                    null,
                    message,
                    "Ollama安装后问题",
                    JOptionPane.YES_NO_CANCEL_OPTION,
                    JOptionPane.WARNING_MESSAGE,
                    null,
                    options,
                    options[0]
            );

            switch (choice) {
                case 0: // 重试启动
                    CompletableFuture.runAsync(() -> checkAndStartOllama());
                    break;
                case 1: // 重新安装
                    CompletableFuture.runAsync(() -> reinstallOllama());
                    break;
                case 2: // 手动配置
                    showManualConfigDialog();
                    break;
                case 3: // 查看日志
                    showOllamaLogsDialog();
                    break;
                case 4: // 跳过
                default:
                    updateStatus("跳过Ollama问题处理");
                    break;
            }
        });
    }

    /**
     * 显示Ollama成功对话框
     */
    private static void showOllamaSuccessDialog(OllamaStatus status) {
        SwingUtilities.invokeLater(() -> {
            System.out.println("Ollama安装并配置完成！");
            System.out.println("安装信息：");
            System.out.println("• 版本: " + status.getVersion());
            System.out.println("• 架构: " + status.getArchitecture().getDisplayName());
            System.out.println("• 模型数量: " + status.getModelCount() + " 个");
            System.out.println("• 服务状态: " + status.getStatusSummary());
            System.out.println("现在可以使用Ollama AI服务了。");
        });
    }

    /**
     * 手动启动Ollama
     */
    private static void manualStartOllama() {
        CompletableFuture.runAsync(() -> {
            try {
                updateStatus("正在手动启动Ollama服务...");

                if (startOllamaService()) {
                    updateStatus("Ollama手动启动成功");
                    updateProgress(100);

                    OllamaStatus status = getOllamaStatus();
                } else {
                    updateStatus("Ollama手动启动失败");
                    showOllamaServiceTroubleshootDialog();
                }

            } catch (Exception e) {
                showErrorDialog("启动失败", "无法启动Ollama服务:\n" + e.getMessage());
            }
        });
    }

    /**
     * 检查端口占用
     */
    private static void checkPortUsage() {
        CompletableFuture.runAsync(() -> {
            try {
                ProcessBuilder pb = new ProcessBuilder("netstat", "-ano");
                Process process = pb.start();

                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                StringBuilder result = new StringBuilder();
                String line;

                result.append("端口 ").append(OLLAMA_DEFAULT_PORT).append(" 占用情况：\n\n");

                boolean found = false;
                while ((line = reader.readLine()) != null) {
                    if (line.contains(":" + OLLAMA_DEFAULT_PORT + " ")) {
                        result.append(line).append("\n");
                        found = true;
                    }
                }

                if (!found) {
                    result.append("端口 ").append(OLLAMA_DEFAULT_PORT).append(" 未被占用\n");
                }

                process.waitFor();

                SwingUtilities.invokeLater(() -> {
                    JTextArea textArea = new JTextArea(result.toString());
                    textArea.setEditable(false);
                    textArea.setRows(10);
                    textArea.setColumns(60);

                    JScrollPane scrollPane = new JScrollPane(textArea);

                    String[] options = {"终止占用进程", "重试启动", "关闭"};
                    int choice = JOptionPane.showOptionDialog(
                            null,
                            scrollPane,
                            "端口占用检查",
                            JOptionPane.YES_NO_CANCEL_OPTION,
                            JOptionPane.INFORMATION_MESSAGE,
                            null,
                            options,
                            options[2]
                    );

                    if (choice == 0) {
                        killProcessUsingPort(Integer.parseInt(OLLAMA_DEFAULT_PORT));
                        CompletableFuture.runAsync(() -> checkAndStartOllama());
                    } else if (choice == 1) {
                        CompletableFuture.runAsync(() -> checkAndStartOllama());
                    }
                });

            } catch (Exception e) {
                showErrorDialog("检查失败", "无法检查端口占用情况:\n" + e.getMessage());
            }
        });
    }

    /**
     * 重新安装Ollama
     */
    private static void reinstallOllama() {
        try {
            updateStatus("正在重新安装Ollama...");
            updateProgress(10);

            // 先停止现有服务
            stopOllama();
            Thread.sleep(2000);

            // 卸载现有安装
            uninstallOllama();
            updateProgress(30);

            // 重新检查并安装
            checkAndStartOllama();

        } catch (Exception e) {
            System.err.println("重新安装Ollama时出错: " + e.getMessage());
            showErrorDialog("重新安装失败", "重新安装过程中发生错误:\n" + e.getMessage());
        }
    }

    /**
     * 卸载Ollama
     */
    private static void uninstallOllama() {
        try {
            // 尝试使用Windows卸载程序
            ProcessBuilder pb = new ProcessBuilder(
                    "powershell", "-Command",
                    "Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like '*Ollama*' } | ForEach-Object { $_.Uninstall() }"
            );
            Process process = pb.start();
            process.waitFor(60, TimeUnit.SECONDS);

            System.out.println("Ollama卸载完成");

        } catch (Exception e) {
            System.err.println("卸载Ollama时出错: " + e.getMessage());
        }
    }

    /**
     * 显示错误对话框
     */
    private static void showErrorDialog(String title, String message) {
        SwingUtilities.invokeLater(() -> {
            JOptionPane.showMessageDialog(
                    null,
                    message,
                    title,
                    JOptionPane.ERROR_MESSAGE
            );
        });
    }

    /**
     * 更新状态标签
     */
    private static void updateStatus(String message) {
        SwingUtilities.invokeLater(() -> {
            if (statusLabel != null) {
                statusLabel.setText(message);
            }
        });
        System.out.println("Ollama状态: " + message);
    }

    /**
     * 更新进度条
     */
    private static void updateProgress(int value) {
        SwingUtilities.invokeLater(() -> {
            if (progressBar != null) {
                progressBar.setValue(value);
                progressBar.setString(value + "%");
            }
        });
    }

    /**
     * 获取Ollama完整状态
     */
    public static OllamaStatus getOllamaStatus() {
        try {
            boolean installed = isOllamaInstalled();
            boolean running = isOllamaRunning();
            boolean serviceHealthy = running && isOllamaServiceResponding();

            String version = installed ? getOllamaVersion() : "未安装";
            String models = running ? getOllamaModels() : "服务未运行";

            // 统计模型信息
            int modelCount = 0;
            long totalModelSize = 0;

            if (running) {
                try {
                    String[] modelLines = models.split("\n");
                    for (String line : modelLines) {
                        if (line.trim().length() > 0 && !line.toLowerCase().contains("name")) {
                            modelCount++;
                            // 这里可以解析模型大小信息
                        }
                    }
                } catch (Exception e) {
                    System.err.println("解析模型信息时出错: " + e.getMessage());
                }
            }

            return new OllamaStatus(installed, running, serviceHealthy, version, models,
                    ollamaInstallPath, systemArch, modelCount, totalModelSize);

        } catch (Exception e) {
            System.err.println("获取Ollama状态时出错: " + e.getMessage());
            return new OllamaStatus(false, false, false, "获取失败", "获取失败",
                    null, systemArch, 0, 0);
        }
    }

    /**
     * 获取Ollama版本信息
     */
    public static String getOllamaVersion() {
        try {
            String ollamaPath = getOllamaExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(ollamaPath, "--version");
            Process process = pb.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String version = reader.readLine();

            process.waitFor(5, TimeUnit.SECONDS);
            return version != null ? version.trim() : "未知版本";
        } catch (Exception e) {
            return "获取版本失败: " + e.getMessage();
        }
    }

    /**
     * 获取Ollama模型列表
     */
    public static String getOllamaModels() {
        try {
            String ollamaPath = getOllamaExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(ollamaPath, "list");
            Process process = pb.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder models = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                models.append(line).append("\n");
            }

            process.waitFor(10, TimeUnit.SECONDS);
            return models.toString();
        } catch (Exception e) {
            return "获取模型列表失败: " + e.getMessage();
        }
    }

    /**
     * 停止Ollama服务和所有相关进程
     */
    public static void stopOllama() {
        System.out.println("正在停止Ollama服务及相关进程...");
        try {
            // 步骤 1: 强制终止 ollama.exe 进程
            System.out.println("正在终止 ollama.exe 进程...");
            terminateProcessByName("ollama.exe");

            // 步骤 2: 强制终止 Ollama 进程 (可能是GUI或辅助进程)
            System.out.println("正在终止 Ollama 进程...");
            terminateProcessByName("Ollama");

        } catch (Exception e) {
            System.err.println("停止Ollama服务时发生严重错误: " + e.getMessage());
        } finally {
            System.out.println("Ollama服务停止流程完成。");
        }
    }

    /**
     * 根据进程名强制终止进程的辅助方法
     * @param processName 要终止的进程名
     */
    private static void terminateProcessByName(String processName) {
        try {
            ProcessBuilder taskkill = new ProcessBuilder("taskkill", "/F", "/IM", processName);
            Process process = taskkill.start();
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                System.out.printf("成功终止了名为 '%s' 的进程。\n", processName);
            } else if (exitCode == 128) {
                // 错误码128表示"找不到指定的进程"，这是正常情况。
                System.out.printf("名为 '%s' 的进程未在运行。\n", processName);
            } else {
                System.out.printf("终止进程 '%s' 时返回了非预期的代码: %d\n", processName, exitCode);
            }
        } catch (IOException | InterruptedException e) {
            System.err.printf("执行taskkill终止进程 '%s' 时出错: %s\n", processName, e.getMessage());
            // 在并发环境中，恢复中断状态
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 检查Ollama服务是否正在运行（通过API和进程）
     */
    public static boolean isOllamaRunning() {
        try {
            // 方法1：检查进程
            ProcessBuilder pb = new ProcessBuilder("tasklist", "/FI", "IMAGENAME eq ollama.exe");
            Process process = pb.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            boolean processFound = false;

            while ((line = reader.readLine()) != null) {
                if (line.toLowerCase().contains("ollama.exe")) {
                    processFound = true;
                    break;
                }
            }

            process.waitFor(5, TimeUnit.SECONDS);

            // 方法2：检查服务响应
            if (processFound) {
                return isOllamaServiceResponding();
            }

            return false;

        } catch (Exception e) {
            System.err.println("检查Ollama运行状态时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查Ollama服务是否响应
     */
    private static boolean isOllamaServiceResponding() {
        try {
            URL url = new URL(OLLAMA_SERVICE_URL + "/api/tags");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(2000);
            connection.setReadTimeout(2000);

            int responseCode = connection.getResponseCode();
            connection.disconnect();

            return responseCode == 200;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查Ollama是否已安装
     */
    public static boolean isOllamaInstalled() {
        try {
            // 方法1：检查安装路径
            if (ollamaInstallPath != null) {
                File ollamaExe = new File(ollamaInstallPath, "ollama.exe");
                if (ollamaExe.exists()) {
                    return true;
                }
            }

            // 方法2：尝试运行版本命令
            String ollamaPath = getOllamaExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(ollamaPath, "--version");
            Process process = pb.start();
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);

            return finished && process.exitValue() == 0;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查Ollama离线安装文件
     */
    private static OllamaOfflineFiles checkOllamaOfflineFiles() {
        try {
            String basePath = EnvironmentConfig.getBasePath();
            // 所有安装文件与exe在同一目录，不是scripts子目录

            // 根据系统架构寻找对应的安装程序（在当前目录中）
            String[] possibleInstallers = {
                    "OllamaSetup.exe",  // 主要安装程序，与脚本一致
                    systemArch.getInstallerName(),
                    "ollama-windows-amd64.exe",
                    "ollama-setup.exe",
                    "ollama.exe"
            };

            File ollamaInstaller = null;
            for (String installerName : possibleInstallers) {
                File candidate = new File(basePath, installerName);
                if (candidate.exists()) {
                    ollamaInstaller = candidate;
                    break;
                }
            }

            // 寻找模型目录（在同一目录中）
            File modelsDir = new File(basePath, "ollama-models");
            // File configFile = new File(basePath, "ollama-config.json"); // Removed

            OllamaOfflineFiles files = new OllamaOfflineFiles(ollamaInstaller, modelsDir, /* configFile, */ systemArch); // Config argument removed

            updateStatus("Ollama离线文件检查: " + files.getStatus());

            System.out.println("Ollama离线文件检查详情:");
            System.out.println("  项目目录: " + basePath);
            System.out.println("  系统架构: " + systemArch.getDisplayName());
            System.out.println("  安装程序: " + (ollamaInstaller != null ? ollamaInstaller.getAbsolutePath() : "未找到") +
                    " - " + (files.hasInstaller() ? "存在" : "不存在"));
            System.out.println("  模型目录: " + modelsDir.getAbsolutePath() + " - " + (files.hasModels() ? "存在" : "不存在"));
            // System.out.println("  配置文件: " + configFile.getAbsolutePath() + " - " + (files.hasConfig() ? "存在" : "不存在")); // Removed

            return files;

        } catch (Exception e) {
            System.err.println("检查Ollama离线文件时发生错误: " + e.getMessage());
            return new OllamaOfflineFiles(null, null, systemArch);
        }
    }

    /**
     * 显示模型列表对话框
     */
    private static void showModelListDialog(OllamaStatus status) {
        System.out.println("当前Ollama模型列表：");
        String modelInfo = status.getModels();
        if (modelInfo == null || modelInfo.trim().isEmpty() || modelInfo.contains("失败")) {
            System.out.println("无法获取模型列表，请确保Ollama服务正在运行");
        } else {
            System.out.println(modelInfo);
        }
    }

    /**
     * 显示推荐模型对话框
     */
    private static void showRecommendedModelsDialog() {
        System.out.println("推荐的Ollama模型（请手动安装）：");
        System.out.println("轻量级模型（适合消费级电脑）：");
        System.out.println("• llama3.2:1b - 1B参数，约1GB");
        System.out.println("• llama3.2:3b - 3B参数，约2GB");
        System.out.println("• qwen2.5:1.5b - 1.5B参数，约1GB");
        System.out.println("中等模型（推荐）：");
        System.out.println("• llama3.2:7b - 7B参数，约4GB");
        System.out.println("• qwen2.5:7b - 7B参数，约4GB");
        System.out.println("• deepseek-r1:7b - 7B参数，约4GB");
        System.out.println("请使用命令行手动下载：ollama pull <model_name>");
    }

    /**
     * 下载Ollama模型（禁用 - 仅记录日志）
     */
    private static void downloadOllamaModel(String modelName) {
        System.out.println("模型下载功能已禁用，请使用命令行手动下载模型：");
        System.out.println("ollama pull " + modelName);
    }

    /**
     * 显示详细诊断对话框
     */
    private static void showDetailedDiagnosisDialog() {
        CompletableFuture.runAsync(() -> {
            StringBuilder diagnosis = new StringBuilder();
            diagnosis.append("Ollama详细诊断报告\n");
            diagnosis.append("=".repeat(50)).append("\n\n");

            // 系统信息
            diagnosis.append("系统信息:\n");
            diagnosis.append("• 操作系统: ").append(System.getProperty("os.name")).append("\n");
            diagnosis.append("• 系统架构: ").append(systemArch.getDisplayName()).append("\n");
            diagnosis.append("• Java版本: ").append(System.getProperty("java.version")).append("\n\n");

            // Ollama状态
            diagnosis.append("Ollama状态:\n");
            OllamaStatus status = getOllamaStatus();
            diagnosis.append("• 已安装: ").append(status.isInstalled() ? "是" : "否").append("\n");
            diagnosis.append("• 正在运行: ").append(status.isRunning() ? "是" : "否").append("\n");
            diagnosis.append("• 服务健康: ").append(status.isServiceHealthy() ? "是" : "否").append("\n");
            diagnosis.append("• 版本: ").append(status.getVersion()).append("\n");
            diagnosis.append("• 安装路径: ").append(status.getInstallPath() != null ? status.getInstallPath() : "未知").append("\n\n");

            // 端口检查
            diagnosis.append("端口检查:\n");
            try {
                ProcessBuilder pb = new ProcessBuilder("netstat", "-an");
                Process process = pb.start();
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                boolean portFound = false;

                while ((line = reader.readLine()) != null) {
                    if (line.contains(":" + OLLAMA_DEFAULT_PORT + " ")) {
                        diagnosis.append("• ").append(line.trim()).append("\n");
                        portFound = true;
                    }
                }

                if (!portFound) {
                    diagnosis.append("• 端口 ").append(OLLAMA_DEFAULT_PORT).append(" 未被占用\n");
                }

                process.waitFor();
            } catch (Exception e) {
                diagnosis.append("• 端口检查失败: ").append(e.getMessage()).append("\n");
            }

            diagnosis.append("\n");

            // 进程检查
            diagnosis.append("进程检查:\n");
            try {
                ProcessBuilder pb = new ProcessBuilder("tasklist", "/FI", "IMAGENAME eq ollama.exe");
                Process process = pb.start();
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                boolean processFound = false;

                while ((line = reader.readLine()) != null) {
                    if (line.toLowerCase().contains("ollama.exe")) {
                        diagnosis.append("• ").append(line.trim()).append("\n");
                        processFound = true;
                    }
                }

                if (!processFound) {
                    diagnosis.append("• 未发现ollama.exe进程\n");
                }

                process.waitFor();
            } catch (Exception e) {
                diagnosis.append("• 进程检查失败: ").append(e.getMessage()).append("\n");
            }

            SwingUtilities.invokeLater(() -> {
                JTextArea textArea = new JTextArea(diagnosis.toString());
                textArea.setEditable(false);
                textArea.setRows(20);
                textArea.setColumns(70);
                textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));

                JScrollPane scrollPane = new JScrollPane(textArea);

                String[] options = {"保存报告", "重试启动", "关闭"};
                int choice = JOptionPane.showOptionDialog(
                        null,
                        scrollPane,
                        "Ollama详细诊断",
                        JOptionPane.YES_NO_CANCEL_OPTION,
                        JOptionPane.INFORMATION_MESSAGE,
                        null,
                        options,
                        options[2]
                );

                switch (choice) {
                    case 0: // 保存报告
                        saveDiagnosisReport(diagnosis.toString());
                        break;
                    case 1: // 重试启动
                        CompletableFuture.runAsync(() -> checkAndStartOllama());
                        break;
                    case 2: // 关闭
                    default:
                        break;
                }
            });
        });
    }

    /**
     * 保存诊断报告
     */
    private static void saveDiagnosisReport(String report) {
        try {
            String fileName = "ollama_diagnosis_" + System.currentTimeMillis() + ".txt";
            Path reportPath = Paths.get(System.getProperty("user.home"), "Desktop", fileName);
            Files.write(reportPath, report.getBytes());

            JOptionPane.showMessageDialog(null,
                    "诊断报告已保存到:\n" + reportPath.toString(),
                    "报告保存成功",
                    JOptionPane.INFORMATION_MESSAGE);

        } catch (Exception e) {
            showErrorDialog("保存失败", "无法保存诊断报告:\n" + e.getMessage());
        }
    }

    /**
     * 显示手动配置对话框
     */
    private static void showManualConfigDialog() {
        SwingUtilities.invokeLater(() -> {
            String message = "手动配置Ollama步骤：\n\n" +
                    "1. 环境变量配置：\n" +
                    "   • OLLAMA_HOME: " + System.getenv("USERPROFILE") + "\\.ollama\n" +
                    "   • OLLAMA_HOST: 0.0.0.0:" + OLLAMA_DEFAULT_PORT + "\n\n" +
                    "2. 服务配置：\n" +
                    "   • 确保端口 " + OLLAMA_DEFAULT_PORT + " 未被占用\n" +
                    "   • 检查防火墙设置\n" +
                    "   • 以管理员权限运行\n\n" +
                    "3. 手动启动命令：\n" +
                    "   • 打开命令提示符\n" +
                    "   • 运行: ollama serve\n\n" +
                    "是否自动配置环境变量？";

            int choice = JOptionPane.showConfirmDialog(
                    null,
                    message,
                    "手动配置Ollama",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.QUESTION_MESSAGE
            );

            if (choice == JOptionPane.YES_OPTION) {
                CompletableFuture.runAsync(() -> {
                    configureOllamaEnvironment();
                    updateStatus("环境变量配置完成，请重启应用程序");
                });
            }
        });
    }

    /**
     * 显示Ollama日志对话框
     */
    private static void showOllamaLogsDialog() {
        CompletableFuture.runAsync(() -> {
            StringBuilder logs = new StringBuilder();

            try {
                // 尝试获取Ollama日志
                String ollamaHome = System.getenv("USERPROFILE") + "\\.ollama";
                Path logsDir = Paths.get(ollamaHome, "logs");

                if (Files.exists(logsDir)) {
                    Files.list(logsDir)
                            .filter(path -> path.toString().endsWith(".log"))
                            .forEach(logFile -> {
                                try {
                                    logs.append("=== ").append(logFile.getFileName()).append(" ===\n");
                                    List<String> lines = Files.readAllLines(logFile);
                                    // 只显示最后50行
                                    int start = Math.max(0, lines.size() - 50);
                                    for (int i = start; i < lines.size(); i++) {
                                        logs.append(lines.get(i)).append("\n");
                                    }
                                    logs.append("\n");
                                } catch (Exception e) {
                                    logs.append("读取日志文件失败: ").append(e.getMessage()).append("\n");
                                }
                            });
                } else {
                    logs.append("未找到Ollama日志目录: ").append(logsDir).append("\n");
                }

                // 如果没有找到日志，尝试从系统事件日志获取
                if (logs.length() == 0) {
                    logs.append("未找到Ollama日志文件\n\n");
                    logs.append("建议检查以下位置：\n");
                    logs.append("• ").append(ollamaHome).append("\\logs\n");
                    logs.append("• Windows事件查看器\n");
                    logs.append("• 命令行运行 ollama serve 查看实时日志\n");
                }

            } catch (Exception e) {
                logs.append("获取日志时出错: ").append(e.getMessage()).append("\n");
            }

            SwingUtilities.invokeLater(() -> {
                JTextArea textArea = new JTextArea(logs.toString());
                textArea.setEditable(false);
                textArea.setRows(20);
                textArea.setColumns(80);
                textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 10));

                JScrollPane scrollPane = new JScrollPane(textArea);

                String[] options = {"刷新日志", "保存日志", "关闭"};
                int choice = JOptionPane.showOptionDialog(
                        null,
                        scrollPane,
                        "Ollama日志查看",
                        JOptionPane.YES_NO_CANCEL_OPTION,
                        JOptionPane.INFORMATION_MESSAGE,
                        null,
                        options,
                        options[2]
                );

                switch (choice) {
                    case 0: // 刷新日志
                        showOllamaLogsDialog();
                        break;
                    case 1: // 保存日志
                        saveDiagnosisReport(logs.toString());
                        break;
                    case 2: // 关闭
                    default:
                        break;
                }
            });
        });
    }
}