package com.zibbava.edgemind.cortex.controller.api;

import com.zibbava.edgemind.cortex.common.Result;
import com.zibbava.edgemind.cortex.common.enums.ResultCode;
import com.zibbava.edgemind.cortex.common.exception.BusinessException;
import com.zibbava.edgemind.cortex.dto.LicenseStatusDTO;
import com.zibbava.edgemind.cortex.service.LicenseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 许可证API控制器
 * 处理许可证相关的API请求
 */
@RestController
@RequestMapping("/api/license")
@RequiredArgsConstructor
@Slf4j
public class LicenseController {

    private final LicenseService licenseService;

    /**
     * 获取硬件指纹
     *
     * @return 硬件指纹
     */
    @GetMapping("/fingerprint")
    public Result<String> getHardwareFingerprint() {
        try {
            String fingerprint = licenseService.getHardwareFingerprint();
            return Result.success(fingerprint);
        } catch (Exception e) {
            log.error("获取硬件指纹失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取硬件指纹失败: " + e.getMessage());
        }
    }

    /**
     * 获取详细的硬件信息（用于调试）
     *
     * @return 硬件信息
     */
    @GetMapping("/hardware-info")
    public Result<String> getDetailedHardwareInfo() {
        try {
            String hardwareInfo = licenseService.getDetailedHardwareInfo();
            return Result.success(hardwareInfo);
        } catch (Exception e) {
            log.error("获取硬件信息失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取硬件信息失败: " + e.getMessage());
        }
    }

    /**
     * 验证许可证
     *
     * @param licenseKey 许可证密钥
     * @return 验证结果
     */
    @PostMapping("/verify")
    public Result<Boolean> verifyLicense(@RequestParam String licenseKey) {
        try {
            boolean result = licenseService.verifyLicense(licenseKey);
            return Result.success(result);
        } catch (Exception e) {
            log.error("验证许可证失败", e);
            throw new BusinessException(ResultCode.OPERATION_FAILED, "验证许可证失败: " + e.getMessage());
        }
    }

    /**
     * 激活许可证
     *
     * @param licenseKey 许可证密钥
     * @return 激活结果
     */
    @PostMapping("/activate")
    public Result<Boolean> activateLicense(@RequestParam String licenseKey) {
        try {
            boolean result = licenseService.activateLicense(licenseKey);
            if (result) {
                return Result.success(true);
            } else {
                return Result.error(ResultCode.OPERATION_FAILED, "许可证激活失败，请检查许可证密钥是否正确");
            }
        } catch (Exception e) {
            log.error("激活许可证失败", e);
            throw new BusinessException(ResultCode.OPERATION_FAILED, "激活许可证失败: " + e.getMessage());
        }
    }

    /**
     * 获取许可证状态
     *
     * @return 许可证状态
     */
    @GetMapping("/status")
    public Result<LicenseStatusDTO> getLicenseStatus() {
        try {
            LicenseStatusDTO status = licenseService.getLicenseStatus();
            return Result.success(status);
        } catch (Exception e) {
            log.error("获取许可证状态失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取许可证状态失败: " + e.getMessage());
        }
    }

}
