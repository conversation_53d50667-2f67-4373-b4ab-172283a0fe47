package com.zibbava.edgemind.cortex.aspect;

import cn.dev33.satoken.stp.StpUtil;
import com.zibbava.edgemind.cortex.annotation.DataPermission;
import com.zibbava.edgemind.cortex.entity.Role;
import com.zibbava.edgemind.cortex.entity.User;
import com.zibbava.edgemind.cortex.service.RoleService;
import com.zibbava.edgemind.cortex.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Aspect
@Component
public class DataPermissionAspect {

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    /**
     * 数据权限切点
     */
    @Pointcut("@annotation(com.zibbava.edgemind.cortex.annotation.DataPermission)")
    public void dataPermissionPointcut() {}

    /**
     * 数据权限处理
     */
    @Before("dataPermissionPointcut() && @annotation(dataPermission)")
    public void handleDataPermission(JoinPoint joinPoint, DataPermission dataPermission) {
        try {
            // 检查是否登录
            if (!StpUtil.isLogin()) {
                return;
            }

            Long userId = StpUtil.getLoginIdAsLong();
            User currentUser = userService.getById(userId);
            if (currentUser == null) {
                return;
            }

            // 获取用户角色
            List<Role> roles = roleService.findRolesByUserId(userId);
            
            // 检查是否是超级管理员（拥有所有数据权限）
            boolean isSuperAdmin = roles.stream()
                .anyMatch(role -> "ROLE_SUPER_ADMIN".equals(role.getRoleCode()));
            
            if (isSuperAdmin) {
                // 超级管理员拥有所有数据权限，不需要过滤
                return;
            }

            // 根据数据权限范围构建SQL条件
            String sqlCondition = buildDataPermissionCondition(dataPermission, currentUser, roles);
            
            if (sqlCondition != null && !sqlCondition.isEmpty()) {
                // 将SQL条件存储到ThreadLocal中，供MyBatis拦截器使用
                DataPermissionContextHolder.setDataPermissionSql(sqlCondition);
            }

        } catch (Exception e) {
            log.error("数据权限处理失败", e);
        }
    }

    /**
     * 构建数据权限SQL条件
     */
    private String buildDataPermissionCondition(DataPermission dataPermission, User currentUser, List<Role> roles) {
        StringBuilder condition = new StringBuilder();
        
        switch (dataPermission.dataScope()) {
            case ALL:
                // 全部数据权限，不添加任何条件
                return null;
                
            case SELF:
                // 仅本人数据权限
                condition.append(" AND ").append(dataPermission.userAlias())
                    .append(".id = ").append(currentUser.getId());
                break;
                
            case DEPT:
                // 本部门数据权限
                if (currentUser.getDeptId() != null) {
                    condition.append(" AND ").append(dataPermission.deptAlias())
                        .append(".").append(dataPermission.permission())
                        .append(" = ").append(currentUser.getDeptId());
                }
                break;
                
            case DEPT_AND_CHILD:
                // 本部门及子部门数据权限
                if (currentUser.getDeptId() != null) {
                    // 这里需要查询部门及其所有子部门ID
                    List<Long> deptIds = getDeptAndChildIds(currentUser.getDeptId());
                    if (!deptIds.isEmpty()) {
                        String deptIdStr = deptIds.stream()
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                        condition.append(" AND ").append(dataPermission.deptAlias())
                            .append(".").append(dataPermission.permission())
                            .append(" IN (").append(deptIdStr).append(")");
                    }
                }
                break;
                
            case CUSTOM:
                // 自定义数据权限，根据角色的数据权限配置
                String customCondition = buildCustomDataPermission(dataPermission, currentUser, roles);
                if (customCondition != null) {
                    condition.append(customCondition);
                }
                break;
        }
        
        return condition.toString();
    }

    /**
     * 获取部门及其所有子部门ID
     */
    private List<Long> getDeptAndChildIds(Long deptId) {
        // 这里需要实现递归查询部门及其子部门的逻辑
        // 暂时返回当前部门ID
        return List.of(deptId);
    }

    /**
     * 构建自定义数据权限条件
     */
    private String buildCustomDataPermission(DataPermission dataPermission, User currentUser, List<Role> roles) {
        // 这里可以根据角色的自定义数据权限配置来构建SQL条件
        // 暂时返回部门权限
        if (currentUser.getDeptId() != null) {
            return " AND " + dataPermission.deptAlias() + "." + dataPermission.permission() 
                + " = " + currentUser.getDeptId();
        }
        return null;
    }

    /**
     * 数据权限上下文持有者
     */
    public static class DataPermissionContextHolder {
        private static final ThreadLocal<String> DATA_PERMISSION_SQL = new ThreadLocal<>();

        public static void setDataPermissionSql(String sql) {
            DATA_PERMISSION_SQL.set(sql);
        }

        public static String getDataPermissionSql() {
            return DATA_PERMISSION_SQL.get();
        }

        public static void clear() {
            DATA_PERMISSION_SQL.remove();
        }
    }
}
