package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.entity.OperationLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface OperationLogService extends IService<OperationLog> {

    /**
     * 记录操作日志
     * @param userId 用户ID
     * @param username 用户名
     * @param module 操作模块
     * @param operationType 操作类型
     * @param description 操作描述
     * @param method 请求方法
     * @param requestUrl 请求URL
     * @param requestParams 请求参数
     * @param responseResult 响应结果
     * @param status 操作状态
     * @param errorMessage 错误信息
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @param executionTime 执行时间
     */
    void recordLog(Long userId, String username, String module, String operationType, 
                   String description, String method, String requestUrl, String requestParams,
                   String responseResult, Integer status, String errorMessage, 
                   String ipAddress, String userAgent, Long executionTime);

    /**
     * 简化的记录操作日志方法
     * @param userId 用户ID
     * @param username 用户名
     * @param module 操作模块
     * @param operationType 操作类型
     * @param description 操作描述
     * @param status 操作状态
     */
    void recordLog(Long userId, String username, String module, String operationType, 
                   String description, Integer status);

    /**
     * 分页查询操作日志
     * @param page 分页参数
     * @param queryParams 查询参数
     * @return 分页结果
     */
    IPage<OperationLog> getLogPage(Page<OperationLog> page, Map<String, Object> queryParams);

    /**
     * 获取操作日志统计信息
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取操作频率统计
     * @param hours 统计小时数
     * @return 频率统计
     */
    List<Map<String, Object>> getOperationFrequency(int hours);

    /**
     * 获取用户操作排行
     * @param days 统计天数
     * @param limit 返回数量限制
     * @return 用户操作排行
     */
    List<Map<String, Object>> getUserOperationRanking(int days, int limit);

    /**
     * 获取模块操作统计
     * @param days 统计天数
     * @return 模块统计
     */
    List<Map<String, Object>> getModuleStatistics(int days);

    /**
     * 清理过期日志
     * @param days 保留天数
     * @return 清理的记录数
     */
    int cleanupExpiredLogs(int days);

    /**
     * 获取用户最近的操作记录
     * @param userId 用户ID
     * @param limit 返回数量限制
     * @return 操作记录列表
     */
    List<OperationLog> getUserRecentOperations(Long userId, int limit);

    /**
     * 获取失败操作记录
     * @param days 统计天数
     * @param limit 返回数量限制
     * @return 失败操作记录
     */
    List<OperationLog> getFailedOperations(int days, int limit);

    /**
     * 导出操作日志
     * @param queryParams 查询参数
     * @return 日志列表
     */
    List<OperationLog> exportLogs(Map<String, Object> queryParams);

    /**
     * 批量删除操作日志
     * @param ids 日志ID列表
     */
    void batchDeleteLogs(List<Long> ids);

    /**
     * 获取今日操作统计
     * @return 今日统计信息
     */
    Map<String, Object> getTodayStatistics();

    /**
     * 获取操作趋势数据
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getOperationTrend(int days);
}
