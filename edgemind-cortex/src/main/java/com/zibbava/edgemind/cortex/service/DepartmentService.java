package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.entity.Department;
import com.zibbava.edgemind.cortex.entity.User;

import java.util.List;
import java.util.Map;

public interface DepartmentService extends IService<Department> {

    /**
     * 获取部门树
     * @return 部门树结构的列表
     */
    List<Department> getDepartmentTree();

    /**
     * 分页查询部门列表
     * @param page 分页参数
     * @param queryParams 查询参数
     * @return 分页结果
     */
    IPage<Department> getDepartmentPage(Page<Department> page, Map<String, Object> queryParams);

    /**
     * 获取部门下的用户列表
     * @param deptId 部门ID
     * @return 用户列表
     */
    List<User> getDepartmentUsers(Long deptId);

    /**
     * 批量删除部门
     * @param ids 部门ID列表
     */
    void batchRemove(List<Long> ids);

    /**
     * 移动部门到新的父部门
     * @param deptId 部门ID
     * @param newParentId 新父部门ID
     */
    void moveDepartment(Long deptId, Long newParentId);

    /**
     * 获取部门统计信息
     * @return 统计信息
     */
    Map<String, Object> getDepartmentStatistics();

    /**
     * 设置部门负责人
     * @param deptId 部门ID
     * @param managerId 负责人ID
     */
    void setDepartmentManager(Long deptId, Long managerId);

    /**
     * 获取部门层级路径
     * @param deptId 部门ID
     * @return 从根部门到当前部门的路径
     */
    List<Department> getDepartmentPath(Long deptId);

    /**
     * 更新部门状态
     * @param deptId 部门ID
     * @param status 状态
     */
    void updateDepartmentStatus(Long deptId, Integer status);

    /**
     * 根据用户ID查询所属部门
     * @param userId 用户ID
     * @return 部门信息
     */
    Department getDepartmentByUserId(Long userId);

    /**
     * 检查部门编码是否唯一
     * @param deptCode 部门编码
     * @param excludeId 排除的部门ID（用于更新时检查）
     * @return 是否唯一
     */
    boolean isDeptCodeUnique(String deptCode, Long excludeId);

    /**
     * 检查是否可以删除部门（没有子部门和用户）
     * @param deptId 部门ID
     * @return 是否可以删除
     */
    boolean canDeleteDepartment(Long deptId);
}