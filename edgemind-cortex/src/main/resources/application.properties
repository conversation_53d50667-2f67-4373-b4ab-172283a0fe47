spring.application.name=edgemind-cortex
server.port=8080

# ================= AI 模型配置 =================
ai.model.type=ollama
ai.model.name=qwen3:14b
ai.model.baseUrl=http://localhost:11434
ai.model.apiKey=your-api-key

# ================= 许可证服务器配置 =================
app.license.server.url=https://duanzhi.cc
app.version=1.0.0

# ================= 运行环境配置 =================
run.env=dev

# ================= Weaviate 向量数据库配置 =================
# 基础连接配置
weaviate.host=localhost
weaviate.port=8090
weaviate.class.name=Wgk_document_prod
weaviate.dimension=1024

# 性能优化配置
weaviate.batch.size=100
weaviate.connection.timeout=30000
weaviate.retry.attempts=1

# ================= Weaviate 混合搜索配置 =================
# 启用混合搜索（Dense + BM25 Sparse）
weaviate.hybrid.enabled=true
# 向量搜索权重（语义搜索）- 针对消费级电脑优化
weaviate.hybrid.alpha=0.3
# 混合搜索重排策略
weaviate.hybrid.rank-strategy=WEIGHTED

# ================= RAG 搜索优化配置 =================
# 搜索参数
weaviate.optimization.search.max-results=10
# 提高最小分数阈值，过滤低质量匹配
weaviate.optimization.search.min-score=0.6

# ================= 文件上传配置 =================
spring.servlet.multipart.max-file-size=30MB
spring.servlet.multipart.max-request-size=30MB

# ================= 服务器配置 =================
server.tomcat.max-swallow-size=30MB
server.servlet.context-path=/wkg

# ================= 开发工具配置 =================
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
spring.thymeleaf.cache=false
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.cache.period=0

# ================= 数据库配置 =================
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# 数据库连接URL (请替换 your_db_name)
spring.datasource.url=**********************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=ENC(ZsG+BLcamXysQdYt03YcTOUxP0AWcVqo)

jasypt.encryptor.algorithm=PBEWithMD5AndDES
jasypt.encryptor.password=0f7b0a5d-46bc-40fd-b8ed-3181d21d644f
jasypt.encryptor.iv-generator-classname=org.jasypt.iv.NoIvGenerator

# ================= Redis 配置 =================
spring.data.redis.host=127.0.0.1
spring.data.redis.port=6379
spring.data.redis.database=0
spring.data.redis.timeout=10000

# ================= Sa-Token 配置 =================
sa-token.token-name=satoken
sa-token.is-log=true
sa-token.token-style=uuid
sa-token.is-concurrent=false
sa-token.is-share=false
sa-token.max-login-count=1

# ================= MyBatis Plus 配置 =================
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.zibbava.entity

# ================= ONLYOFFICE 配置 =================
# Document Server 地址
onlyoffice.server-url=http://localhost:8055/
#onlyoffice.server-url=https://duanzhi.cc/onlyoffice/
onlyoffice.api-js-url=${onlyoffice.server-url}web-apps/apps/api/documents/api.js
onlyoffice.security-key=wkg123456

# 语言与区域配置
onlyoffice.language=zh-CN
onlyoffice.region=zh-CN

# 编辑器模式配置
onlyoffice.mode=view
onlyoffice.type=desktop
onlyoffice.co-editing-mode=fast
onlyoffice.co-editing-mode-changeable=false

# 功能配置
onlyoffice.show-comments=false
onlyoffice.show-chat=false
onlyoffice.compact-toolbar=true
onlyoffice.show-feedback-button=false
onlyoffice.show-help-button=false
onlyoffice.hide-toolbar=true
onlyoffice.hide-statusbar=true
onlyoffice.hide-right-menu=true

# ================= 企业级对话策略配置 =================

# 对话风格配置
chat.style.professional=true
chat.style.friendly=true
chat.style.structured=true

# 功能开关配置
chat.context.analysis=true
chat.thinking.enabled=true
chat.history.aware=true

# 对话质量配置
chat.quality.accuracy-first=true
chat.quality.completeness-check=true
chat.quality.relevance-filter=true

# 增强检索配置
# 启用增强检索功能（查询扩展）
enhanced.retrieval.enabled=true
# 子查询数量
enhanced.retrieval.sub-queries=3
# 假设文档数量
enhanced.retrieval.hypothetical-docs=2

# ================= 历史消息处理配置 =================
chat.history.max-messages=10

