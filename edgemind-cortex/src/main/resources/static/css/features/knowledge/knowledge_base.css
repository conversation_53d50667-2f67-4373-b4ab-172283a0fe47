/* ============================
   Knowledge Base Page Styles
   ============================ */

body {
    /* display: flex; REMOVE THIS if loaded within another flex container like layout */
    /* min-height: 100vh; */
    overflow-x: hidden; /* Prevent horizontal scroll */
}

.page-content {
    display: flex;
    width: 100%;
    height: 100vh; /* Use viewport height */
    overflow: hidden; /* Prevent overflow on the container */
}

/* Left Document Tree Sidebar */
.knowledge-sidebar {
    background-color: #f8f9fa !important;
    border-right: 1px solid #dee2e6 !important;
    width: 280px !important;
    flex-shrink: 0 !important;
    display: flex !important;
    flex-direction: column;
    padding: 1rem 0.25rem 1rem 0.5rem; /* 再次减少右侧内边距，使其更紧凑 */
    height: 100% !important;
    overflow-x: hidden;
    overflow-y: hidden; /* 确保侧边栏本身不滚动，而是内部的 doc-tree 滚动 */
    position: relative; /* sidebar 本身可以是 relative，这没关系 */
    z-index: 10; /* 或其他合适的侧边栏层级，要低于下拉菜单 */
}

/* 文档树基础样式 */
.knowledge-sidebar .doc-tree {
    list-style: none;
    padding-left: 0;
    margin-top: 1rem;
    flex-grow: 1;
    overflow-y: auto; /* 允许垂直滚动 */
    overflow-x: hidden !important;
    /* Firefox 浏览器的滚动条样式 - 永久隐藏 */
    scrollbar-width: none;
    /* 确保文档树占满整个可用空间 */
    height: 100%;
}

/* WebKit 浏览器的滚动条样式 - 永久隐藏 */
.knowledge-sidebar .doc-tree::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
    background-color: transparent;
}

.knowledge-sidebar .doc-tree::-webkit-scrollbar-thumb {
    background-color: transparent;
    display: none;
}

.knowledge-sidebar .doc-tree::-webkit-scrollbar-track {
    background-color: transparent;
    display: none;
}

/* 文档树子节点样式 */
.knowledge-sidebar .doc-tree ul {
    list-style: none;
    padding-left: 1rem;
    min-width: auto;
    max-width: 100%;
}

/* 下拉菜单样式覆盖，确保与文档树分离 */
.node-dropdown {
    padding-left: 0 !important; /* 覆盖文档树的padding-left */
    min-width: 8rem;
}

.node-dropdown li {
    text-align: center;
}

.node-dropdown .dropdown-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
}

.node-dropdown .dropdown-item i {
    margin-right: 0.5rem;
}

.knowledge-sidebar .doc-tree > ul {
    padding-left: 0;
}

.knowledge-sidebar .doc-tree li {
    position: relative;
    margin-bottom: 2px;
}

/* 节点外层容器样式 */
.node-outer-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    width: 100%;
    /* padding: 0.3rem 0.5rem; */ /* 原样式 */
    padding: 0.3rem 0 0.3rem 0.5rem; /* 移除右侧内边距 */
    border-radius: 0.25rem;
    margin-bottom: 0.1rem;
    transition: background-color 0.15s ease;
    cursor: pointer;
    max-width: 100%;
    overflow: hidden;
}

.node-outer-container:hover {
    background-color: #e9ecef;
}

/* 选中状态下的悬停效果保持蓝色背景 */
.file-node.active > .node-outer-container:hover,
.folder-node.active > .node-outer-container:hover {
    background-color: #cfe2ff;
}

/* 节点容器样式 */
.knowledge-sidebar .doc-tree .nav-link-container {
    flex-grow: 1;
    flex-shrink: 1;
    min-width: 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    position: relative;
}

.knowledge-sidebar .doc-tree .nav-link-container:hover {
    background-color: #e9ecef;
}

/* 选中状态下容器的悬停效果保持蓝色背景 */
.file-node.active .nav-link-container:hover,
.folder-node.active .nav-link-container:hover {
    background-color: transparent; /* 保持透明，依赖父元素的背景色 */
}

/* 节点选中样式 - 兼容原有模式 */
.knowledge-sidebar .doc-tree .nav-link-container.active {
    color: #0d6efd;
}

/* 文件节点选中时的样式 */
.file-node.active > .node-outer-container {
    background-color: #cfe2ff;
    border-radius: 0.25rem;
}

.file-node.active > .node-outer-container .node-link {
    color: #0d6efd;
}

/* 文件夹节点选中时的样式 */
.folder-node.active > .node-outer-container {
    background-color: #cfe2ff;
    border-radius: 0.25rem;
}

.folder-node.active > .node-outer-container .node-link {
    color: #0d6efd;
}

.knowledge-sidebar .doc-tree .nav-link-container.options-active {
    background-color: #e9ecef;
}

/* 节点链接样式 */
.knowledge-sidebar .doc-tree .node-link {
    flex-grow: 1;
    flex-shrink: 1;
    min-width: 0;
    text-decoration: none;
    color: #495057;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    /* padding-right: 5px; */ /* 移除右侧内边距 */
    padding-right: 0;
}

.knowledge-sidebar .doc-tree .node-link i {
    margin-right: 0.5rem;
}

/* 文件图标的浅蓝色样式 */
.file-node > .node-outer-container .node-link .bi-file-earmark-text {
    color: #7eb8ff;
}

/* 未选中状态的文件夹图标蓝色样式 */
.folder-node > .node-outer-container .node-link .bi-folder {
    color: #7eb8ff;
}

.knowledge-sidebar .node-name-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 节点操作区域 */
.node-actions {
    display: flex; /* 保持 display:flex，通过 opacity 控制显隐 */
    align-items: center;
    gap: 4px;
    position: relative;
    /* margin-left: 5px; */ /* 移除左外边距，使其紧贴右侧 */
    margin-left: 0;
    flex-shrink: 0;
    opacity: 0; /* 默认通过透明度隐藏 */
    transition: opacity 0.15s ease, visibility 0.15s ease; /* 添加 visibility 过渡 */
    visibility: hidden; /* 配合 opacity 实现完全隐藏和无交互 */
    z-index: 5;
    white-space: nowrap;
}

/* 鼠标悬停在外层容器上时、外层容器激活时、或其内部下拉菜单打开时显示操作按钮 */
.knowledge-sidebar .doc-tree .node-outer-container:hover .node-actions,
.knowledge-sidebar .doc-tree .node-outer-container.active .node-actions,
.knowledge-sidebar .doc-tree .node-outer-container.has-open-dropdown .node-actions { /* 新增这个选择器部分 */
    opacity: 1;
    visibility: visible; /* 确保可见且可交互 */
}

/* 可选: 当下拉菜单打开时，给 node-outer-container 也保持一个高亮（如果需要）*/
.knowledge-sidebar .doc-tree .node-outer-container.has-open-dropdown {
    background-color: #e9ecef; /* 与 :hover 效果一致或自定义 */
}

/* 操作按钮共享样式 */
.node-actions .btn {
    padding: 0.15rem 0.3rem;
    font-size: 0.75rem;
    background-color: transparent;
    border: none;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 聊天按钮 */
.node-chat-btn {
    color: #0d6efd;
}

.node-chat-btn:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

/* 重命名按钮 */
.node-rename-btn {
    color: #6c757d;
}

.node-rename-btn:hover {
    background-color: rgba(108, 117, 125, 0.1);
    color: #495057;
}

/* 删除按钮 */
.node-delete-btn {
    color: #dc3545;
}

.node-delete-btn:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: #b02a37;
}

/* 三点菜单按钮 */
.node-options-dropdown {
    position: relative;
}

.node-options-btn {
    padding: 0.15rem 0.3rem;
    font-size: 0.75rem;
    background-color: transparent;
    border: none;
    color: #6c757d;
}

.node-options-btn:hover {
    color: #495057;
    background-color: rgba(0, 0, 0, 0.05);
}

/* 三点更多选项按钮样式 */
.node-more-btn {
    padding: 0.15rem 0.3rem;
    font-size: 0.75rem;
    background-color: transparent;
    border: none;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1055;
    position: relative;
}

.node-more-btn:hover,
.node-more-btn[aria-expanded="true"] {
    background-color: rgba(108, 117, 125, 0.1);
    color: #495057;
}

/* 当下拉菜单激活时保持节点高亮 */
.node-more-btn[aria-expanded="true"] ~ .node-dropdown {
    display: block;
}

.dropdown-menu-end.show {
    display: block !important;
}

/* 下拉菜单位置调整 */
.dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
    transform-origin: top right;
}

/* 确保节点操作按钮显示在右侧且不会消失 */
.nav-link-container .node-actions {
    gap: 2px;
    right: 4px;
}

/* 调整知识库节点下拉菜单的位置 */
.node-dropdown.show { /* 仅在显示时应用 */
    /* 移除之前的 transform */
    /* transform: translateX(10px) !important; */

    /* 尝试使用负的 right 值将其推出容器右边界 */
    right: -15px !important; /* 负值将其向右推离右边界 */

    /* 确保 z-index 足够高 */
    z-index: 1051;
}

/* 确保包含按钮的 actionsDiv 不会意外限制下拉菜单的位置 */
.node-actions {
    /* 恢复为 relative，看是否是定位上下文问题 */
     position: relative;
}

/* 确保最外层节点容器有相对定位，并允许溢出 */
.tree-node > .d-flex {
    position: relative;
    overflow: visible !important; /* 确保下拉菜单不会被父元素裁剪 */
}

/* 尝试简化下拉菜单定位，让 Bootstrap 和 Popper.js 处理更多工作 */
/* 如果以下简化导致下拉菜单被父级 overflow:hidden 裁剪，则 fixed 定位可能是必要的，但需小心 */
.node-dropdown {
    min-width: 160px; /* 或者你期望的宽度 */
    font-size: 0.875rem;
    /* 【重要】确保 z-index 足够高，能覆盖侧边栏和页面其他内容 */
    /* Bootstrap dropdown 默认 z-index 约 1000, offcanvas 约 1040-1045, modal 约 1050-1055, toast 约 1080-1090 */
    /* 如果希望它在所有这些之上，可以设为 1091+ */
    z-index: 1060; /* 比如，比 offcanvas 和 modal 高一点，但通常不需要这么高，1000-1030 就够了，除非有特殊覆盖需求 */
    padding: 0.4rem 0;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); /* 增加一些阴影效果 */
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.375rem;
    background-color: #fff;

}

.node-dropdown .dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.4rem 0.8rem;
    color: #333;
    font-weight: normal;
    white-space: nowrap;
}

.node-dropdown .dropdown-item i {
    margin-right: 0.5rem;
    font-size: 0.875rem;
    width: 1.2rem;
    text-align: center;
}

.node-dropdown .dropdown-item:hover,
.node-dropdown .dropdown-item:focus {
    background-color: #f8f9fa;
    color: #1e2125;
}

.node-dropdown .dropdown-item.text-danger {
    color: #dc3545;
}

.node-dropdown .dropdown-item.text-danger:hover,
.node-dropdown .dropdown-item.text-danger:focus {
    background-color: #f8d7da;
    color: #b02a37;
}

.node-dropdown .dropdown-divider {
    margin: 0.3rem 0;
}

/* 下拉菜单位置和行为控制 */
.dropdown-menu.show.node-dropdown {
    position: fixed !important;
    z-index: 9999 !important;
    margin: 0 !important;
    display: block !important;
}

/* 防止下拉菜单被父容器限制 */
.node-actions .dropdown {
    position: static;
}

/* 确保下拉菜单显示在上方 */
.node-dropdown.show {
    display: block !important;
}

/* 聊天侧边栏样式 */
#aiChatSidebarCenter {
    width: 0;
    overflow: hidden;
    transition: width 0.3s, padding 0.3s, border 0.3s;
    background-color: #fff;
    flex-shrink: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
}

#aiChatSidebarCenter.active {
    width: 320px;
    padding: 1rem;
    border-left: 1px solid #dee2e6;
    border-right: 1px solid #dee2e6;
}

#aiChatSidebarCenter .offcanvas-header {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* 限制聊天侧边栏标题长度，避免挤压关闭按钮 */
#aiChatSidebarCenter .offcanvas-title {
    max-width: 80%; /* 限制标题最大宽度为80% */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-shrink: 1;
    margin-bottom: 0;
}

#aiChatSidebarCenter .offcanvas-body {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

/* 确保聊天组件在侧边栏中正确显示 */
#aiChatSidebarCenter .chat-component {
    height: 100%;
    display: flex;
    flex-direction: column;
}

#aiChatSidebarCenter .chat-messages {
    overflow-y: auto;
}

/* 知识库AI聊天区域使用通用滚动条样式 */
#aiChatSidebarCenter .chat-messages {
    /* 通用滚动条样式将自动应用 */
}

#aiChatSidebarCenter .chat-container {
    flex: 1;
    min-height: 0;
}

/* 确保聊天输入区域正确显示 */
#aiChatSidebarCenter .chat-input-area {
    margin-top: auto;
}

/* Right Main Content Area */
.knowledge-main {
    flex: 1;
    overflow-y: hidden; /* 隐藏垂直滚动条 */
    overflow-x: hidden; /* 同时隐藏水平滚动条（可选，通常不需要） */
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.knowledge-main-header {
    padding: 1rem;
    max-width: 50%; /* 与documentAreaWrapper保持一致 */
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    transition: max-width 0.3s ease, margin 0.3s ease;
}

/* 限制标题长度，避免挤压右侧按钮 */
#mainViewTitle {
    max-width: 60%; /* 限制标题最大宽度为60% */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-shrink: 1;
}

/* Wrapper for Document Area */
#documentAreaWrapper {
    flex-grow: 1; /* Takes remaining vertical space */
    overflow-y: auto; /* Enable vertical scroll for the document content */
    /* Default state: centered */
    max-width: 50%; /* Or a fixed px value */
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    background-color: #ffffff;
    transition: max-width 0.3s ease, margin 0.3s ease, border 0.3s ease;
    /* Removed vertical borders as requested */
}

.document-content-area {
    padding: 1.5rem;
}

/* Editing State for Document Area */
#documentAreaWrapper.editing {
    background-color: #f9f9f9;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#documentEdit textarea {
    width: 100%;
    min-height: calc(100vh - 200px); /* Example height calculation */
    border: 1px solid #ced4da;
    border-radius: .25rem;
    padding: .5rem;
    font-family: monospace;
    resize: vertical; /* Allow vertical resize */
}

/* Layout Adjustments When Chat Sidebar is Active */
.page-content.chat-active #aiChatSidebarCenter {
    width: calc((100% - 300px) * 0.5);
    flex-basis: calc((100% - 300px) * 0.5);
    padding: 0;
    flex-shrink: 0;
    border-left: 1px solid #dee2e6;
    border-right: 1px solid #dee2e6;
    z-index: 1;
    display: flex;
    flex-direction: column;
}

.page-content:not(.chat-active) #aiChatSidebarCenter {
    width: 0;
    padding: 0;
    border: none;
    overflow: hidden;
}

.page-content.chat-active .knowledge-main {
    flex-basis: calc((100% - 300px) * 0.5);
    background-color: transparent;
}

.page-content.chat-active #documentAreaWrapper,
.page-content.chat-active .knowledge-main-header {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
}

/* Shared Message Styles */
.ai-chat-messages-center .user-message,
.ai-chat-messages-center .ai-message {
    margin-bottom: 0.75rem; /* Increased spacing */
    max-width: 85%; /* Limit message width */
    word-wrap: break-word; /* Break long words */
}

.ai-chat-messages-center .user-message {
    text-align: right;
    margin-left: auto; /* Align to right */
}

.ai-chat-messages-center .user-message span {
    background-color: #cfe2ff; /* Bootstrap primary light */
    padding: 0.4rem 0.8rem;
    border-radius: 0.8rem;
    display: inline-block;
    text-align: left; /* Align text left within the bubble */
}

.ai-chat-messages-center .ai-message {
    text-align: left;
    margin-right: auto; /* Align to left */
}

.ai-chat-messages-center .ai-message span {
    background-color: #e2e3e5; /* Bootstrap gray-200 */
    padding: 0.4rem 0.8rem;
    border-radius: 0.8rem;
    display: inline-block;
}

/* AI Message Reference Styles (Example) */
.ai-message .reference {
    font-size: 0.75rem;
    margin-top: 0.3rem;
    display: block;
    color: #6c757d; /* Bootstrap secondary text */
}

.ai-message .reference a {
    color: #0d6efd; /* Bootstrap primary */
    text-decoration: none;
}

.ai-message .reference a:hover {
    text-decoration: underline;
}

/* Chat Input Area */
.ai-chat-input-center {
    flex-shrink: 0; /* Prevent input area from shrinking */
    border-top: 1px solid #dee2e6;
    padding-top: 0.75rem;
}
#aiChatInputCenter {
    overflow-y: auto; /* Allow textarea scroll if needed */
    max-height: 150px; /* Limit growth */
}

/* 确保下拉菜单和模态框交互时的层级管理 */
.z-index-high {
    z-index: 1060 !important; /* 确保高于普通元素 */
    position: relative !important;
}

/* 节点容器在激活选项时的样式 */
.nav-link-container.options-active {
    background-color: #e9ecef !important;
    border-radius: 0.25rem;
}

/* 文件节点选中时的样式 */
.file-node.active > .node-outer-container {
    background-color: #cfe2ff;
    border-radius: 0.25rem;
}

.file-node.active > .node-outer-container .node-link {
    color: #0d6efd;
}

/* 确保下拉菜单相对于容器定位 */
.node-actions {
    position: relative;
}

/* 确保下拉菜单不会被裁剪 */
.folder-node > ul {
    z-index: auto;
}

/* 文件夹展开/折叠相关样式 */
/* 默认隐藏文件夹的子节点 */
.folder-node .node-children {
    display: none;
}

/* 仅当文件夹有 expanded 类时显示子节点 */
.folder-node.expanded > .node-children {
    display: block;
}

/* 文件夹展开/折叠状态切换 */
/* 默认折叠状态下的文件夹图标 */
.folder-node > .node-outer-container .node-link .bi-folder {
    display: inline-block;
}

.folder-node > .node-outer-container .node-link .bi-folder-fill {
    display: none;
}

/* 展开状态下显示打开的文件夹图标 */
.folder-node.expanded > .node-outer-container .node-link .bi-folder {
    display: none;
}

.folder-node.expanded > .node-outer-container .node-link .bi-folder-fill {
    display: inline-block;
    color: #0d6efd;
}

/* 文件节点选中时的样式 */
.node-link.selected {
    background-color: #e2e3e5; /* Bootstrap gray-200 */
    border-radius: 0.25rem;
}

/* 文件夹展开时变更图标颜色 */
.folder-node.expanded > .node-outer-container .folder-icon:before {
    content: "\f07c"; /* Bootstrap Icons 的打开的文件夹图标 Unicode */
    color: #0d6efd;
}
