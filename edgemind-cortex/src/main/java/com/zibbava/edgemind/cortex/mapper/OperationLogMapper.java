package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.cortex.entity.OperationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface OperationLogMapper extends BaseMapper<OperationLog> {

    /**
     * 分页查询操作日志（包含用户信息）
     */
    @Select("SELECT ol.*, u.nickname, u.email " +
            "FROM sys_operation_log ol " +
            "LEFT JOIN sys_user u ON ol.user_id = u.id " +
            "WHERE 1=1 " +
            "${ew.customSqlSegment}")
    IPage<OperationLog> selectLogPageWithUser(Page<OperationLog> page, @Param("ew") Object queryWrapper);

    /**
     * 根据时间范围统计操作日志
     */
    @Select("SELECT " +
            "COUNT(*) as total, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as failed, " +
            "COUNT(DISTINCT user_id) as userCount, " +
            "COUNT(DISTINCT module) as moduleCount " +
            "FROM sys_operation_log " +
            "WHERE operation_time BETWEEN #{startTime} AND #{endTime}")
    Map<String, Object> getStatistics(@Param("startTime") LocalDateTime startTime, 
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 获取操作频率统计（按小时）
     */
    @Select("SELECT " +
            "DATE_FORMAT(operation_time, '%Y-%m-%d %H:00:00') as hour, " +
            "COUNT(*) as count " +
            "FROM sys_operation_log " +
            "WHERE operation_time >= #{startTime} " +
            "GROUP BY DATE_FORMAT(operation_time, '%Y-%m-%d %H:00:00') " +
            "ORDER BY hour")
    List<Map<String, Object>> getOperationFrequency(@Param("startTime") LocalDateTime startTime);

    /**
     * 获取用户操作排行
     */
    @Select("SELECT " +
            "ol.user_id, " +
            "ol.username, " +
            "u.nickname, " +
            "COUNT(*) as operationCount, " +
            "SUM(CASE WHEN ol.status = 1 THEN 1 ELSE 0 END) as successCount, " +
            "SUM(CASE WHEN ol.status = 0 THEN 1 ELSE 0 END) as failedCount " +
            "FROM sys_operation_log ol " +
            "LEFT JOIN sys_user u ON ol.user_id = u.id " +
            "WHERE ol.operation_time >= #{startTime} " +
            "GROUP BY ol.user_id, ol.username, u.nickname " +
            "ORDER BY operationCount DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getUserOperationRanking(@Param("startTime") LocalDateTime startTime, 
                                                      @Param("limit") int limit);

    /**
     * 获取模块操作统计
     */
    @Select("SELECT " +
            "module, " +
            "COUNT(*) as count, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as successCount, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as failedCount " +
            "FROM sys_operation_log " +
            "WHERE operation_time >= #{startTime} " +
            "GROUP BY module " +
            "ORDER BY count DESC")
    List<Map<String, Object>> getModuleStatistics(@Param("startTime") LocalDateTime startTime);

    /**
     * 清理指定时间之前的日志
     */
    @Select("DELETE FROM sys_operation_log WHERE operation_time < #{beforeTime}")
    int cleanupLogs(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 获取用户最近的操作记录
     */
    @Select("SELECT * FROM sys_operation_log " +
            "WHERE user_id = #{userId} " +
            "ORDER BY operation_time DESC " +
            "LIMIT #{limit}")
    List<OperationLog> getUserRecentOperations(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 获取失败操作记录
     */
    @Select("SELECT ol.*, u.nickname, u.email " +
            "FROM sys_operation_log ol " +
            "LEFT JOIN sys_user u ON ol.user_id = u.id " +
            "WHERE ol.status = 0 " +
            "AND ol.operation_time >= #{startTime} " +
            "ORDER BY ol.operation_time DESC " +
            "LIMIT #{limit}")
    List<OperationLog> getFailedOperations(@Param("startTime") LocalDateTime startTime, 
                                          @Param("limit") int limit);
}
