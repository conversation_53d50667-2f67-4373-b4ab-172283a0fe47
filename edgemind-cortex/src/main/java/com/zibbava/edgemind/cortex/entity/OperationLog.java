package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作日志实体类
 */
@Data
@TableName("sys_operation_log")
public class OperationLog {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 操作用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 操作用户名
     */
    @TableField("username")
    private String username;

    /**
     * 操作模块
     */
    @TableField("module")
    private String module;

    /**
     * 操作类型
     */
    @TableField("operation_type")
    private String operationType;

    /**
     * 操作描述
     */
    @TableField("description")
    private String description;

    /**
     * 请求方法
     */
    @TableField("method")
    private String method;

    /**
     * 请求URL
     */
    @TableField("request_url")
    private String requestUrl;

    /**
     * 请求参数
     */
    @TableField("request_params")
    private String requestParams;

    /**
     * 响应结果
     */
    @TableField("response_result")
    private String responseResult;

    /**
     * 操作状态：0-失败，1-成功
     */
    @TableField("status")
    private Integer status;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 操作IP
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 执行时间（毫秒）
     */
    @TableField("execution_time")
    private Long executionTime;

    /**
     * 操作时间
     */
    @TableField(value = "operation_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operationTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    // ----- 非数据库字段 -----

    /**
     * 操作用户信息（查询时填充）
     */
    @TableField(exist = false)
    private User user;

    // ----- 枚举类型 -----
    
    /**
     * 操作类型枚举
     */
    public enum OperationType {
        CREATE("新增"),
        UPDATE("修改"),
        DELETE("删除"),
        QUERY("查询"),
        LOGIN("登录"),
        LOGOUT("登出"),
        EXPORT("导出"),
        IMPORT("导入"),
        ASSIGN("分配"),
        RESET("重置"),
        ENABLE("启用"),
        DISABLE("禁用"),
        MOVE("移动"),
        COPY("复制"),
        UPLOAD("上传"),
        DOWNLOAD("下载"),
        OTHER("其他");

        private final String description;

        OperationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 操作模块枚举
     */
    public enum Module {
        USER("用户管理"),
        ROLE("角色管理"),
        PERMISSION("权限管理"),
        DEPARTMENT("部门管理"),
        SYSTEM("系统管理"),
        KNOWLEDGE("知识库"),
        CHAT("AI对话"),
        LICENSE("许可证"),
        OTHER("其他");

        private final String description;

        Module(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
