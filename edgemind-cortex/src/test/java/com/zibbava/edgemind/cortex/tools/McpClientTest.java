package com.zibbava.edgemind.cortex.tools;

import com.zibbava.edgemind.cortex.service.Assistant;
import dev.langchain4j.mcp.McpToolProvider;
import dev.langchain4j.mcp.client.DefaultMcpClient;
import dev.langchain4j.mcp.client.McpClient;
import dev.langchain4j.mcp.client.transport.McpTransport;
import dev.langchain4j.mcp.client.transport.stdio.StdioMcpTransport;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.tool.ToolProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.junit.jupiter.api.Test;

import java.util.List;

@SpringBootTest
class McpClientTest {

    // 默认的 @modelcontextprotocol/server-filesystem 端口
    private static final String MCP_SERVER_URL = "http://localhost:8676";

    // 定义一个 AI 服务接口
    interface FileSystemAgent {

        @SystemMessage({
                "You are a helpful assistant that can interact with the local file system using available tools.",
                "You can read files, write files, list directories, create directories, etc., within the allowed paths.",
                "The allowed paths are configured when the local file server was started.",
                "When asked to write a file, confirm the target directory is allowed and proceed."
                // "When listing directories, present the results clearly." // 可以根据需要添加更多指令
        })
        String interact(String userMessage);
    }


    @Autowired
    private ChatModel chatModel;

    @Test
    void contextLoads() {


//        System.out.println("--- LangChain4j Local MCP FileSystem Client ---");
//        System.out.println("确保本地 MCP 文件系统服务器正在运行 (via npx @modelcontextprotocol/server-filesystem <allowed_dirs>...)");
//        System.out.println("客户端将尝试连接到: " + MCP_SERVER_URL);
//        System.out.println("--------------------------------------------------");


        // 2. 创建 RemoteToolExecutor 连接到本地 MCP 服务器
        try {
            McpTransport transport = new StdioMcpTransport.Builder()
                    .command(List.of("cmd", "/c", "npx", "-y", "@modelcontextprotocol/server-puppeteer"))
                    .logEvents(true) // only if you want to see the traffic in the log
                    .build();
            //文件系统
//            McpTransport transport = new StdioMcpTransport.Builder()
//                    .command(List.of("cmd", "/c", "npx", "-y", "@modelcontextprotocol/server-filesystem", "D:\\文档"))
//                    .logEvents(true) // only if you want to see the traffic in the log
//                    .build();
            //uv run --with mcp[cli] mcp run D:\test\mcp-server-demo\mcp_server_baidu_maps\map.py
//            McpTransport transport = new StdioMcpTransport.Builder()
//                    .command(List.of("uv", "run", "--with", "mcp[cli]", "mcp", "run", "D:\\test\\mcp-server-demo\\mcp_server_baidu_maps\\map.py"))
//                    .logEvents(true) // only if you want to see the traffic in the log
//                    .build();
            McpClient mcpClient = new DefaultMcpClient.Builder()
                    .transport(transport)
                    .build();
            ToolProvider toolProvider = McpToolProvider.builder()
                    .mcpClients(List.of(mcpClient))
                    .build();
            Assistant assistant = AiServices.builder(Assistant.class)
                    .chatModel(chatModel)
                    .toolProvider(toolProvider)
                    .build();

//            System.out.println("成功创建 RemoteToolExecutor 并连接到 " + MCP_SERVER_URL);
            String chat = assistant.chat("打开baidu.com");
            System.out.println(chat);
            // RemoteToolExecutor 会自动发现服务器提供的工具 (如 readFile, writeFile, listDirectory 等)
            // 可以在调试时查看 fileSystemToolExecutor.getToolSpecifications() 来确认发现的工具
            // System.out.println("发现的工具: " + fileSystemToolExecutor.getToolSpecifications());

        } catch (Exception e) {
            System.err.println("创建 RemoteToolExecutor 时发生未知错误:");
            e.printStackTrace();
            return;
        }

    }
}
