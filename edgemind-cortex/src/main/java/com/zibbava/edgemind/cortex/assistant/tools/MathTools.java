package com.zibbava.edgemind.cortex.assistant.tools;

import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import org.springframework.stereotype.Component;

/**
 * 数学工具类，提供基本的数学计算功能
 */
@Component
public class MathTools implements Tools {

    @Tool("计算两个数字的和")
    public double add(
            @P("第一个加数") double a,
            @P("第二个加数") double b
    ) {
        System.out.println("计算+");
        return a + b;
    }

    @Tool("计算两个数字的差")
    public double subtract(
            @P("被减数") double a,
            @P("减数") double b
    ) {
        System.out.println("计算-");
        return a - b;
    }

    @Tool("计算两个数字的乘积")
    public double multiply(
            @P("第一个因数") double a,
            @P("第二个因数") double b
    ) {
        return a * b;
    }

    @Tool("计算两个数字的商")
    public double divide(
            @P("被除数") double a,
            @P("除数") double b
    ) {
        if (b == 0) {
            throw new IllegalArgumentException("除数不能为零");
        }
        return a / b;
    }

    @Tool("计算一个数的平方根")
    public double sqrt(
            @P("需要计算平方根的数字") double x
    ) {
        if (x < 0) {
            throw new IllegalArgumentException("不能计算负数的平方根");
        }
        return Math.sqrt(x);
    }

    @Tool("计算一个数的平方")
    public double square(
            @P("需要计算平方的数字") double x
    ) {
        return x * x;
    }
}