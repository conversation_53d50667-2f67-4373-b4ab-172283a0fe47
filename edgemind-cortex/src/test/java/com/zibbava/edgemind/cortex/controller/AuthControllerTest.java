package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.zibbava.edgemind.cortex.base.BaseControllerTest;
import com.zibbava.edgemind.cortex.dto.LoginRequest;
import com.zibbava.edgemind.cortex.dto.LoginResponse;
import com.zibbava.edgemind.cortex.dto.RegisterRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * AuthController 单元测试
 * 认证相关接口测试，包括注册、登录、登出等功能
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
@DisplayName("认证控制器测试")
class AuthControllerTest extends BaseControllerTest {

    @Test
    @DisplayName("用户注册 - 成功")
    void register_Success() throws Exception {
        // Given
        RegisterRequest request = new RegisterRequest();
        request.setUsername("testuser");
        request.setPassword("password123");
        
        // 不需要mock userService.register，因为它被注释了

        // When & Then
        mockMvc.perform(post("/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("注册成功"));
    }

    @Test
    @DisplayName("用户登录 - 成功")
    void login_Success() throws Exception {
        // Given
        LoginRequest request = new LoginRequest();
        request.setUsername("testuser");
        request.setPassword("password123");
        
        LoginResponse loginResponse = new LoginResponse();
        loginResponse.setToken("test-token");
        loginResponse.setUserId(1L);
        loginResponse.setUsername("testuser");
        
        // 重新设置Mock，覆盖BaseControllerTest中的设置
        when(userService.login("testuser", "password123")).thenReturn(loginResponse);

        // When & Then
        mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("登录成功"))
                .andExpect(jsonPath("$.data.token").value("test-token"));
        
        verify(userService).login("testuser", "password123");
    }

    @Test
    @DisplayName("用户登录 - 用户名为空")
    void login_EmptyUsername() throws Exception {
        // Given
        LoginRequest request = new LoginRequest();
        request.setUsername("");
        request.setPassword("password123");

        // When & Then - 空用户名应该被Service层处理并抛出异常，被全局异常处理器捕获
        mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError());
    }

    @Test
    @DisplayName("用户登录 - 密码为空")
    void login_EmptyPassword() throws Exception {
        // Given
        LoginRequest request = new LoginRequest();
        request.setUsername("testuser");
        request.setPassword("");

        // When & Then - 空密码应该被Service层处理并抛出异常，被全局异常处理器捕获
        mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError());
    }

    @Test
    @DisplayName("用户登出 - 成功")
    void logout_Success() throws Exception {
        // Given
        try (MockedStatic<StpUtil> mockedStpUtil = mockStatic(StpUtil.class)) {
            // Mock Sa-Token认证状态
            mockedStpUtil.when(StpUtil::isLogin).thenReturn(true);
            mockedStpUtil.when(StpUtil::getLoginIdAsLong).thenReturn(1L);
            doNothing().when(userService).logout();

            // When & Then
            mockMvc.perform(withAuth(post("/auth/logout"))
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.message").value("登出成功"));
            
            verify(userService).logout();
        }
    }

    @Test
    @DisplayName("检查登录状态 - 已登录")
    void checkLogin_LoggedIn() throws Exception {
        // Given
        try (MockedStatic<StpUtil> mockedStpUtil = mockStatic(StpUtil.class)) {
            mockedStpUtil.when(StpUtil::isLogin).thenReturn(true);
            mockedStpUtil.when(StpUtil::getLoginIdAsLong).thenReturn(1L);
            
            // Mock Session
            cn.dev33.satoken.session.SaSession mockSession = mock(cn.dev33.satoken.session.SaSession.class);
            when(mockSession.getString("username")).thenReturn("testuser");
            mockedStpUtil.when(StpUtil::getSession).thenReturn(mockSession);

            // When & Then
            mockMvc.perform(withAuth(get("/auth/check-login"))
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.data.isLogin").value(true))
                    .andExpect(jsonPath("$.data.userId").value(1))
                    .andExpect(jsonPath("$.data.username").value("testuser"));
        }
    }

    @Test
    @DisplayName("检查登录状态 - 未登录")
    void checkLogin_NotLoggedIn() throws Exception {
        // Given
        try (MockedStatic<StpUtil> mockedStpUtil = mockStatic(StpUtil.class)) {
            mockedStpUtil.when(StpUtil::isLogin).thenReturn(false);

            // When & Then
            mockMvc.perform(get("/auth/check-login")
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.data.isLogin").value(false));
        }
    }

    @Test
    @DisplayName("检查登录状态 - 已登录但无用户名")
    void checkLogin_LoggedInNoUsername() throws Exception {
        // Given
        try (MockedStatic<StpUtil> mockedStpUtil = mockStatic(StpUtil.class)) {
            mockedStpUtil.when(StpUtil::isLogin).thenReturn(true);
            mockedStpUtil.when(StpUtil::getLoginIdAsLong).thenReturn(1L);
            
            // Mock Session without username
            cn.dev33.satoken.session.SaSession mockSession = mock(cn.dev33.satoken.session.SaSession.class);
            when(mockSession.getString("username")).thenReturn(null);
            mockedStpUtil.when(StpUtil::getSession).thenReturn(mockSession);

            // When & Then
            mockMvc.perform(withAuth(get("/auth/check-login"))
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.data.isLogin").value(true))
                    .andExpect(jsonPath("$.data.userId").value(1));
        }
    }
} 