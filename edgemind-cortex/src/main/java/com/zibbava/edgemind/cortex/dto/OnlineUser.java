package com.zibbava.edgemind.cortex.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 在线用户DTO
 */
@Data
public class OnlineUser {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 登录IP
     */
    private String ipAddress;

    /**
     * 登录地点
     */
    private String location;

    /**
     * 浏览器
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 最后活动时间
     */
    private LocalDateTime lastActiveTime;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * Token值
     */
    private String tokenValue;

    /**
     * 在线时长（分钟）
     */
    private Long onlineDuration;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 用户代理
     */
    private String userAgent;
}
