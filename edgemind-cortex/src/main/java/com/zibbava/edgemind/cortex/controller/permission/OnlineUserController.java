package com.zibbava.edgemind.cortex.controller.permission;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zibbava.edgemind.cortex.annotation.OperationLog;
import com.zibbava.edgemind.cortex.dto.OnlineUser;
import com.zibbava.edgemind.cortex.service.OnlineUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/permission/online-users")
public class OnlineUserController {

    @Autowired
    private OnlineUserService onlineUserService;

    /**
     * 获取在线用户列表
     */
    @GetMapping
    @SaCheckPermission("online:user:list")
    public ResponseEntity<List<OnlineUser>> getOnlineUsers(@RequestParam(required = false) Map<String, Object> queryParams) {
        List<OnlineUser> onlineUsers = onlineUserService.getOnlineUsers(queryParams);
        return ResponseEntity.ok(onlineUsers);
    }

    /**
     * 获取在线用户统计信息
     */
    @GetMapping("/statistics")
    @SaCheckPermission("online:user:statistics")
    public ResponseEntity<Map<String, Object>> getOnlineUserStatistics() {
        Map<String, Object> statistics = onlineUserService.getOnlineUserStatistics();
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取在线用户数量
     */
    @GetMapping("/count")
    @SaCheckPermission("online:user:list")
    public ResponseEntity<Map<String, Object>> getOnlineUserCount() {
        long count = onlineUserService.getOnlineUserCount();
        return ResponseEntity.ok(Map.of("count", count));
    }

    /**
     * 获取用户在线详情
     */
    @GetMapping("/{userId}")
    @SaCheckPermission("online:user:list")
    public ResponseEntity<OnlineUser> getOnlineUserDetail(@PathVariable Long userId) {
        OnlineUser onlineUser = onlineUserService.getOnlineUserDetail(userId);
        if (onlineUser != null) {
            return ResponseEntity.ok(onlineUser);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 检查用户是否在线
     */
    @GetMapping("/{userId}/status")
    @SaCheckPermission("online:user:list")
    public ResponseEntity<Map<String, Object>> checkUserOnlineStatus(@PathVariable Long userId) {
        boolean isOnline = onlineUserService.isUserOnline(userId);
        return ResponseEntity.ok(Map.of("userId", userId, "isOnline", isOnline));
    }

    /**
     * 强制下线用户
     */
    @PostMapping("/{userId}/kickout")
    @SaCheckPermission("online:user:kickout")
    @OperationLog(module = "在线用户", operationType = "强制下线", description = "强制下线用户ID: {userId}")
    public ResponseEntity<Void> kickoutUser(@PathVariable Long userId) {
        onlineUserService.kickoutUser(userId);
        return ResponseEntity.ok().build();
    }

    /**
     * 批量强制下线用户
     */
    @PostMapping("/batch-kickout")
    @SaCheckPermission("online:user:kickout")
    @OperationLog(module = "在线用户", operationType = "批量强制下线", description = "批量强制下线用户")
    public ResponseEntity<Void> batchKickoutUsers(@RequestBody List<Long> userIds) {
        onlineUserService.batchKickoutUsers(userIds);
        return ResponseEntity.ok().build();
    }

    /**
     * 根据Token强制下线
     */
    @PostMapping("/kickout-by-token")
    @SaCheckPermission("online:user:kickout")
    @OperationLog(module = "在线用户", operationType = "Token下线", description = "根据Token强制下线")
    public ResponseEntity<Void> kickoutByToken(@RequestParam String tokenValue) {
        onlineUserService.kickoutByToken(tokenValue);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取用户登录历史
     */
    @GetMapping("/{userId}/login-history")
    @SaCheckPermission("online:user:list")
    public ResponseEntity<List<Map<String, Object>>> getUserLoginHistory(@PathVariable Long userId,
                                                                         @RequestParam(defaultValue = "10") int limit) {
        List<Map<String, Object>> history = onlineUserService.getUserLoginHistory(userId, limit);
        return ResponseEntity.ok(history);
    }

    /**
     * 获取用户会话信息
     */
    @GetMapping("/{userId}/sessions")
    @SaCheckPermission("online:user:list")
    public ResponseEntity<List<Map<String, Object>>> getUserSessions(@PathVariable Long userId) {
        List<Map<String, Object>> sessions = onlineUserService.getUserSessions(userId);
        return ResponseEntity.ok(sessions);
    }

    /**
     * 更新用户最后活动时间
     */
    @PostMapping("/{userId}/update-active-time")
    @SaCheckPermission("online:user:update")
    public ResponseEntity<Void> updateLastActiveTime(@PathVariable Long userId) {
        onlineUserService.updateLastActiveTime(userId);
        return ResponseEntity.ok().build();
    }

    /**
     * 清理过期的在线用户信息
     */
    @PostMapping("/cleanup")
    @SaCheckPermission("online:user:manage")
    @OperationLog(module = "在线用户", operationType = "清理", description = "清理过期的在线用户信息")
    public ResponseEntity<Void> cleanupExpiredOnlineUsers() {
        onlineUserService.cleanupExpiredOnlineUsers();
        return ResponseEntity.ok().build();
    }
}
