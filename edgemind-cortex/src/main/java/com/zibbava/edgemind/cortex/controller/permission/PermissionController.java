package com.zibbava.edgemind.cortex.controller.permission;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zibbava.edgemind.cortex.entity.Permission;
import com.zibbava.edgemind.cortex.service.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/permission/permissions") // 统一前缀
public class PermissionController {

    @Autowired
    private PermissionService permissionService;

    /**
     * 获取权限树
     */
    @GetMapping("/tree")
    @SaCheckPermission("perm:manage:list") // 需要 'perm:manage:list' 权限
    public ResponseEntity<List<Permission>> getPermissionTree() {
        List<Permission> tree = permissionService.getPermissionTree();
        return ResponseEntity.ok(tree);
    }

    /**
     * 获取所有权限列表 (扁平结构)
     */
    @GetMapping
    @SaCheckPermission("perm:manage:list")
    public ResponseEntity<List<Permission>> getAllPermissions() {
        List<Permission> list = permissionService.list();
        return ResponseEntity.ok(list);
    }

    /**
     * 新增权限
     */
    @PostMapping
    @SaCheckPermission("perm:manage:create")
    public ResponseEntity<Void> addPermission(@RequestBody Permission permission) {
        // TODO: 添加校验逻辑 (e.g., permissionCode 是否唯一)
        permissionService.save(permission);
        return ResponseEntity.ok().build();
    }

    /**
     * 修改权限
     */
    @PutMapping("/{id}")
    @SaCheckPermission("perm:manage:update")
    public ResponseEntity<Void> updatePermission(@PathVariable Long id, @RequestBody Permission permission) {
        // TODO: 添加校验逻辑
        permission.setId(id); // 确保 ID 一致
        permissionService.updateById(permission);
        return ResponseEntity.ok().build();
    }

    /**
     * 删除权限
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission("perm:manage:delete")
    public ResponseEntity<Void> deletePermission(@PathVariable Long id) {
        // TODO: 考虑是否有子权限或角色关联，是否允许删除
        permissionService.removeById(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取单个权限详情 (如果需要)
     */
    @GetMapping("/{id}")
    @SaCheckPermission("perm:manage:list") // 或更具体的权限
    public ResponseEntity<Permission> getPermissionById(@PathVariable Long id) {
        Permission permission = permissionService.getById(id);
        if (permission != null) {
            return ResponseEntity.ok(permission);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
} 