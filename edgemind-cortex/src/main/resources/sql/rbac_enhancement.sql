-- RBAC功能增强SQL脚本

-- 1. 操作日志表
CREATE TABLE IF NOT EXISTS `sys_operation_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
    `username` varchar(50) DEFAULT NULL COMMENT '操作用户名',
    `module` varchar(50) NOT NULL COMMENT '操作模块',
    `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
    `description` varchar(500) DEFAULT NULL COMMENT '操作描述',
    `method` varchar(10) DEFAULT NULL COMMENT '请求方法',
    `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
    `request_params` text COMMENT '请求参数',
    `response_result` text COMMENT '响应结果',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '操作状态：0-失败，1-成功',
    `error_message` varchar(1000) DEFAULT NULL COMMENT '错误信息',
    `ip_address` varchar(50) DEFAULT NULL COMMENT '操作IP',
    `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
    `execution_time` bigint DEFAULT NULL COMMENT '执行时间（毫秒）',
    `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_username` (`username`),
    KEY `idx_module` (`module`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_status` (`status`),
    KEY `idx_operation_time` (`operation_time`),
    KEY `idx_ip_address` (`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 2. 系统设置表增强
ALTER TABLE `sys_settings` 
ADD COLUMN IF NOT EXISTS `setting_group` varchar(50) DEFAULT NULL COMMENT '设置分组' AFTER `description`,
ADD COLUMN IF NOT EXISTS `setting_type` varchar(20) DEFAULT 'STRING' COMMENT '设置类型' AFTER `setting_group`,
ADD COLUMN IF NOT EXISTS `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统内置' AFTER `setting_type`,
ADD COLUMN IF NOT EXISTS `sort_order` int DEFAULT '0' COMMENT '排序' AFTER `is_system`,
ADD COLUMN IF NOT EXISTS `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用' AFTER `sort_order`,
ADD COLUMN IF NOT EXISTS `create_by` bigint DEFAULT NULL COMMENT '创建人' AFTER `update_time`,
ADD COLUMN IF NOT EXISTS `update_by` bigint DEFAULT NULL COMMENT '更新人' AFTER `create_by`;

-- 3. 为部门表添加索引优化
ALTER TABLE `sys_department` 
ADD INDEX IF NOT EXISTS `idx_parent_id` (`parent_id`),
ADD INDEX IF NOT EXISTS `idx_manager_id` (`manager_id`),
ADD INDEX IF NOT EXISTS `idx_status` (`status`),
ADD INDEX IF NOT EXISTS `idx_sort_order` (`sort_order`);

-- 4. 为用户表添加索引优化
ALTER TABLE `sys_user` 
ADD INDEX IF NOT EXISTS `idx_dept_id` (`dept_id`),
ADD INDEX IF NOT EXISTS `idx_email` (`email`),
ADD INDEX IF NOT EXISTS `idx_phone` (`phone`),
ADD INDEX IF NOT EXISTS `idx_create_time` (`create_time`);

-- 5. 为角色表添加索引优化
ALTER TABLE `sys_role` 
ADD INDEX IF NOT EXISTS `idx_role_code` (`role_code`),
ADD INDEX IF NOT EXISTS `idx_status` (`status`);

-- 6. 为权限表添加索引优化
ALTER TABLE `sys_permission` 
ADD INDEX IF NOT EXISTS `idx_permission_code` (`permission_code`),
ADD INDEX IF NOT EXISTS `idx_parent_id` (`parent_id`),
ADD INDEX IF NOT EXISTS `idx_type_status` (`type`, `status`);

-- 7. 插入默认的系统设置
INSERT IGNORE INTO `sys_settings` (`setting_key`, `setting_value`, `description`, `setting_group`, `setting_type`, `is_system`, `sort_order`, `status`) VALUES
('system.name', 'EdgeMind智能知识管理系统', '系统名称', 'SYSTEM', 'STRING', 1, 1, 1),
('system.version', '1.0.0', '系统版本', 'SYSTEM', 'STRING', 1, 2, 1),
('system.logo', '/wkg/images/logo.png', '系统Logo', 'APPEARANCE', 'IMAGE', 1, 1, 1),
('system.favicon', '/wkg/images/favicon.ico', '网站图标', 'APPEARANCE', 'IMAGE', 1, 2, 1),
('security.password.min_length', '6', '密码最小长度', 'SECURITY', 'NUMBER', 1, 1, 1),
('security.password.require_special', 'false', '密码是否需要特殊字符', 'SECURITY', 'BOOLEAN', 1, 2, 1),
('security.login.max_attempts', '5', '登录最大尝试次数', 'SECURITY', 'NUMBER', 1, 3, 1),
('security.session.timeout', '30', '会话超时时间（分钟）', 'SECURITY', 'NUMBER', 1, 4, 1),
('log.operation.retention_days', '90', '操作日志保留天数', 'LOG', 'NUMBER', 1, 1, 1),
('log.operation.auto_cleanup', 'true', '是否自动清理过期日志', 'LOG', 'BOOLEAN', 1, 2, 1),
('notification.email.enabled', 'false', '是否启用邮件通知', 'NOTIFICATION', 'BOOLEAN', 1, 1, 1),
('notification.sms.enabled', 'false', '是否启用短信通知', 'NOTIFICATION', 'BOOLEAN', 1, 2, 1);

-- 8. 插入增强的权限数据
INSERT IGNORE INTO `sys_permission` (`permission_name`, `permission_code`, `type`, `parent_id`, `description`, `status`) VALUES
-- 部门管理权限
('部门管理', 'dept:manage', 'MENU', 4, '部门管理菜单', 1),
('部门列表', 'dept:manage:list', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'dept:manage' LIMIT 1), '查看部门列表', 1),
('部门新增', 'dept:manage:create', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'dept:manage' LIMIT 1), '新增部门', 1),
('部门修改', 'dept:manage:update', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'dept:manage' LIMIT 1), '修改部门', 1),
('部门删除', 'dept:manage:delete', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'dept:manage' LIMIT 1), '删除部门', 1),

-- 操作日志权限
('操作日志', 'log:operation', 'MENU', 4, '操作日志菜单', 1),
('日志列表', 'log:operation:list', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'log:operation' LIMIT 1), '查看操作日志', 1),
('日志统计', 'log:operation:statistics', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'log:operation' LIMIT 1), '查看日志统计', 1),
('日志导出', 'log:operation:export', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'log:operation' LIMIT 1), '导出操作日志', 1),
('日志删除', 'log:operation:delete', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'log:operation' LIMIT 1), '删除操作日志', 1),

-- 在线用户权限
('在线用户', 'online:user', 'MENU', 4, '在线用户管理', 1),
('在线用户列表', 'online:user:list', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'online:user' LIMIT 1), '查看在线用户', 1),
('在线用户统计', 'online:user:statistics', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'online:user' LIMIT 1), '在线用户统计', 1),
('强制下线', 'online:user:kickout', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'online:user' LIMIT 1), '强制用户下线', 1),
('在线用户管理', 'online:user:manage', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'online:user' LIMIT 1), '在线用户管理', 1),
('在线用户更新', 'online:user:update', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'online:user' LIMIT 1), '更新在线用户信息', 1),

-- 系统设置权限
('系统设置', 'system:setting', 'MENU', 4, '系统设置管理', 1),
('设置列表', 'system:setting:list', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'system:setting' LIMIT 1), '查看系统设置', 1),
('设置修改', 'system:setting:update', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'system:setting' LIMIT 1), '修改系统设置', 1),
('设置新增', 'system:setting:create', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'system:setting' LIMIT 1), '新增系统设置', 1),
('设置删除', 'system:setting:delete', 'BUTTON', (SELECT id FROM sys_permission WHERE permission_code = 'system:setting' LIMIT 1), '删除系统设置', 1);

-- 9. 为超级管理员角色分配新权限
INSERT IGNORE INTO `sys_role_permission` (`role_id`, `permission_id`)
SELECT 1, p.id FROM `sys_permission` p 
WHERE p.permission_code IN (
    'dept:manage', 'dept:manage:list', 'dept:manage:create', 'dept:manage:update', 'dept:manage:delete',
    'log:operation', 'log:operation:list', 'log:operation:statistics', 'log:operation:export', 'log:operation:delete',
    'online:user', 'online:user:list', 'online:user:statistics', 'online:user:kickout', 'online:user:manage', 'online:user:update',
    'system:setting', 'system:setting:list', 'system:setting:update', 'system:setting:create', 'system:setting:delete'
) AND NOT EXISTS (
    SELECT 1 FROM `sys_role_permission` rp WHERE rp.role_id = 1 AND rp.permission_id = p.id
);

-- 10. 创建数据权限配置表（可选，用于更复杂的数据权限控制）
CREATE TABLE IF NOT EXISTS `sys_data_permission` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `permission_type` varchar(20) NOT NULL COMMENT '权限类型：ALL,DEPT,DEPT_AND_CHILD,SELF,CUSTOM',
    `dept_ids` text COMMENT '部门ID列表（JSON格式）',
    `user_ids` text COMMENT '用户ID列表（JSON格式）',
    `custom_condition` text COMMENT '自定义条件SQL',
    `description` varchar(255) DEFAULT NULL COMMENT '描述',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_permission_type` (`permission_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据权限配置表';

-- 11. 创建登录日志表
CREATE TABLE IF NOT EXISTS `sys_login_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint DEFAULT NULL COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `login_type` varchar(20) NOT NULL DEFAULT 'WEB' COMMENT '登录类型：WEB,MOBILE,API',
    `ip_address` varchar(50) DEFAULT NULL COMMENT '登录IP',
    `location` varchar(100) DEFAULT NULL COMMENT '登录地点',
    `browser` varchar(100) DEFAULT NULL COMMENT '浏览器',
    `os` varchar(100) DEFAULT NULL COMMENT '操作系统',
    `device_type` varchar(20) DEFAULT NULL COMMENT '设备类型：Desktop,Mobile,Tablet',
    `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
    `login_status` tinyint NOT NULL COMMENT '登录状态：0-失败，1-成功',
    `failure_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
    `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
    `token_value` varchar(200) DEFAULT NULL COMMENT 'Token值',
    `login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    `logout_time` datetime DEFAULT NULL COMMENT '登出时间',
    `online_duration` bigint DEFAULT NULL COMMENT '在线时长（分钟）',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_username` (`username`),
    KEY `idx_login_status` (`login_status`),
    KEY `idx_login_time` (`login_time`),
    KEY `idx_ip_address` (`ip_address`),
    KEY `idx_session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';

-- 12. 优化现有表结构
-- 为用户表添加更多字段
ALTER TABLE `sys_user` 
ADD COLUMN IF NOT EXISTS `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间' AFTER `remark`,
ADD COLUMN IF NOT EXISTS `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP' AFTER `last_login_time`,
ADD COLUMN IF NOT EXISTS `password_update_time` datetime DEFAULT NULL COMMENT '密码更新时间' AFTER `last_login_ip`,
ADD COLUMN IF NOT EXISTS `account_locked` tinyint(1) DEFAULT '0' COMMENT '账号是否锁定' AFTER `password_update_time`,
ADD COLUMN IF NOT EXISTS `lock_time` datetime DEFAULT NULL COMMENT '锁定时间' AFTER `account_locked`,
ADD COLUMN IF NOT EXISTS `failed_login_attempts` int DEFAULT '0' COMMENT '登录失败次数' AFTER `lock_time`;

-- 为角色表添加数据权限字段
ALTER TABLE `sys_role` 
ADD COLUMN IF NOT EXISTS `data_scope` varchar(20) DEFAULT 'DEPT' COMMENT '数据权限范围' AFTER `description`,
ADD COLUMN IF NOT EXISTS `dept_ids` text COMMENT '数据权限部门ID列表' AFTER `data_scope`;

-- 13. 创建视图用于统计查询
CREATE OR REPLACE VIEW `v_user_role_permission` AS
SELECT 
    u.id as user_id,
    u.username,
    u.nickname,
    u.dept_id,
    d.dept_name,
    r.id as role_id,
    r.role_name,
    r.role_code,
    p.id as permission_id,
    p.permission_name,
    p.permission_code,
    p.type as permission_type
FROM sys_user u
LEFT JOIN sys_department d ON u.dept_id = d.id
LEFT JOIN sys_user_role ur ON u.id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.id
LEFT JOIN sys_role_permission rp ON r.id = rp.role_id
LEFT JOIN sys_permission p ON rp.permission_id = p.id
WHERE u.status = 1 AND (r.status IS NULL OR r.status = 1) AND (p.status IS NULL OR p.status = 1);

-- 14. 创建存储过程用于清理过期日志
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `CleanupExpiredLogs`(IN retention_days INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE affected_rows INT DEFAULT 0;
    
    -- 清理操作日志
    DELETE FROM sys_operation_log 
    WHERE operation_time < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    SET affected_rows = affected_rows + ROW_COUNT();
    
    -- 清理登录日志
    DELETE FROM sys_login_log 
    WHERE login_time < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    SET affected_rows = affected_rows + ROW_COUNT();
    
    SELECT CONCAT('清理了 ', affected_rows, ' 条过期日志记录') as result;
END //
DELIMITER ;

-- 15. 创建触发器用于自动记录重要操作
DELIMITER //
CREATE TRIGGER IF NOT EXISTS `tr_user_update_log` 
AFTER UPDATE ON `sys_user`
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status OR OLD.dept_id != NEW.dept_id THEN
        INSERT INTO sys_operation_log (
            user_id, username, module, operation_type, description, status, operation_time
        ) VALUES (
            NEW.id, NEW.username, '用户管理', '状态变更', 
            CONCAT('用户状态或部门发生变更: ', OLD.status, '->', NEW.status, ', 部门: ', OLD.dept_id, '->', NEW.dept_id),
            1, NOW()
        );
    END IF;
END //
DELIMITER ;
