<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zibbava.edgemind.cortex.mapper.RoleMapper">

    <!-- 根据用户ID查询角色列表 -->
    <select id="findRolesByUserId" resultType="com.zibbava.edgemind.cortex.entity.Role">
        SELECT
            r.id,
            r.role_name,
            r.role_code,
            r.description,
            r.status,
            r.create_time,
            r.update_time
        FROM
            sys_role r
        JOIN
            sys_user_role ur ON r.id = ur.role_id
        WHERE
            ur.user_id = #{userId}
          <!-- 可以考虑只查询启用的角色 -->
          <!-- AND r.status = 1 -->
    </select>

    <!-- 根据用户ID列表查询所有相关角色 (包含 userId 用于后续分组) -->
    <!-- 注意: resultType 仍是 Role, 但我们需要 userId -->
    <!-- 方案1: 使用 Map 返回 (稍复杂) -->
    <!-- 
    <resultMap id="UserRolesMap" type="java.util.HashMap">
        <id property="key" column="user_id"/>
        <collection property="value" ofType="entity.com.zibbava.edgemind.cortex.Role">
             <id property="id" column="role_id"/>
             <result property="roleName" column="role_name"/>
             <result property="roleCode" column="role_code"/>
             ... other role fields ...
        </collection>
    </resultMap>
    <select id="findUserRolesMapByUserIds" resultMap="UserRolesMap">
         SELECT
            ur.user_id as user_id,
            r.id as role_id,
            r.role_name,
            r.role_code,
             ...
         FROM sys_role r JOIN sys_user_role ur ON r.id = ur.role_id
         WHERE ur.user_id IN
         <foreach item="userId" collection="userIds" open="(" separator="," close=")">#{userId}</foreach>
    </select> 
    -->
    
    <!-- 方案2: 返回包含 userId 的 Role 列表 (需要在 Role 实体中临时添加 userId 字段) -->
    <!-- 
    <select id="findRolesByUserIdsWithUserId" resultType="entity.com.zibbava.edgemind.cortex.Role">
        SELECT
            r.id, r.role_name, r.role_code, ... , ur.user_id as temp_user_id 
        FROM sys_role r JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id IN
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">#{userId}</foreach>
    </select>
    -->

     <!-- 方案3: 只查询所有相关 Role，在 Service 层根据 UserRole 关联进行分组 (相对简单) -->
      <select id="findRolesByUserIds" resultType="com.zibbava.edgemind.cortex.entity.Role">
        SELECT DISTINCT
            r.id,
            r.role_name,
            r.role_code,
            r.description,
            r.status,
            r.create_time,
            r.update_time
        FROM
            sys_role r
        JOIN
            sys_user_role ur ON r.id = ur.role_id
        WHERE
            ur.user_id IN
            <foreach item="userId" collection="userIds" open="(" separator="," close=")">
                #{userId}
            </foreach>
          <!-- AND r.status = 1 -->
    </select>

</mapper> 