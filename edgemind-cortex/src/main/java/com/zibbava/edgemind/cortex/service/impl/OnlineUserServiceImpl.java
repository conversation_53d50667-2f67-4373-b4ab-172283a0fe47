package com.zibbava.edgemind.cortex.service.impl;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import com.zibbava.edgemind.cortex.dto.OnlineUser;
import com.zibbava.edgemind.cortex.entity.User;
import com.zibbava.edgemind.cortex.service.DepartmentService;
import com.zibbava.edgemind.cortex.service.OnlineUserService;
import com.zibbava.edgemind.cortex.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OnlineUserServiceImpl implements OnlineUserService {

    @Autowired
    private UserService userService;

    @Autowired
    private DepartmentService departmentService;

    @Override
    public List<OnlineUser> getOnlineUsers() {
        return getOnlineUsers(null);
    }

    @Override
    public List<OnlineUser> getOnlineUsers(Map<String, Object> queryParams) {
        List<OnlineUser> onlineUsers = new ArrayList<>();
        
        try {
            // 获取所有在线用户的登录ID
            List<String> loginIds = StpUtil.searchTokenValue("", 0, -1, false);
            
            for (String loginId : loginIds) {
                try {
                    Long userId = Long.parseLong(loginId);
                    OnlineUser onlineUser = buildOnlineUser(userId);
                    if (onlineUser != null && matchesQuery(onlineUser, queryParams)) {
                        onlineUsers.add(onlineUser);
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid login ID format: {}", loginId);
                } catch (Exception e) {
                    log.warn("Error building online user for ID: {}", loginId, e);
                }
            }
        } catch (Exception e) {
            log.error("Error getting online users", e);
        }
        
        // 按登录时间倒序排列
        onlineUsers.sort((u1, u2) -> u2.getLoginTime().compareTo(u1.getLoginTime()));
        
        return onlineUsers;
    }

    private OnlineUser buildOnlineUser(Long userId) {
        try {
            // 检查用户是否在线
            if (!StpUtil.isLogin(userId)) {
                return null;
            }

            // 获取用户信息
            User user = userService.getById(userId);
            if (user == null) {
                return null;
            }

            // 获取会话信息
            SaSession session = StpUtil.getSessionByLoginId(userId);
            if (session == null) {
                return null;
            }

            OnlineUser onlineUser = new OnlineUser();
            onlineUser.setUserId(userId);
            onlineUser.setUsername(user.getUsername());
            onlineUser.setNickname(user.getNickname());
            
            // 获取部门信息
            if (user.getDeptId() != null) {
                try {
                    onlineUser.setDeptName(departmentService.getById(user.getDeptId()).getDeptName());
                } catch (Exception e) {
                    log.warn("Error getting department for user: {}", userId, e);
                }
            }

            // 从会话中获取登录信息
            onlineUser.setIpAddress((String) session.get("loginIp"));
            onlineUser.setUserAgent((String) session.get("userAgent"));
            onlineUser.setBrowser(parseBrowser((String) session.get("userAgent")));
            onlineUser.setOs(parseOs((String) session.get("userAgent")));
            onlineUser.setDeviceType(parseDeviceType((String) session.get("userAgent")));
            
            // 获取登录时间
            Object loginTimeObj = session.get("loginTime");
            if (loginTimeObj instanceof LocalDateTime) {
                onlineUser.setLoginTime((LocalDateTime) loginTimeObj);
            } else if (loginTimeObj instanceof Long) {
                onlineUser.setLoginTime(LocalDateTime.now().minusSeconds((System.currentTimeMillis() - (Long) loginTimeObj) / 1000));
            } else {
                onlineUser.setLoginTime(LocalDateTime.now());
            }

            // 获取最后活动时间
            Object lastActiveObj = session.get("lastActiveTime");
            if (lastActiveObj instanceof LocalDateTime) {
                onlineUser.setLastActiveTime((LocalDateTime) lastActiveObj);
            } else {
                onlineUser.setLastActiveTime(LocalDateTime.now());
            }

            // 计算在线时长
            if (onlineUser.getLoginTime() != null) {
                long duration = ChronoUnit.MINUTES.between(onlineUser.getLoginTime(), LocalDateTime.now());
                onlineUser.setOnlineDuration(duration);
            }

            // 获取Token信息
            onlineUser.setTokenValue(StpUtil.getTokenValueByLoginId(userId));
            onlineUser.setSessionId(session.getId());

            return onlineUser;
        } catch (Exception e) {
            log.error("Error building online user for ID: {}", userId, e);
            return null;
        }
    }

    private boolean matchesQuery(OnlineUser onlineUser, Map<String, Object> queryParams) {
        if (queryParams == null || queryParams.isEmpty()) {
            return true;
        }

        // 用户名查询
        if (queryParams.containsKey("username") && StringUtils.hasText((String) queryParams.get("username"))) {
            String username = (String) queryParams.get("username");
            if (onlineUser.getUsername() == null || !onlineUser.getUsername().contains(username)) {
                return false;
            }
        }

        // 昵称查询
        if (queryParams.containsKey("nickname") && StringUtils.hasText((String) queryParams.get("nickname"))) {
            String nickname = (String) queryParams.get("nickname");
            if (onlineUser.getNickname() == null || !onlineUser.getNickname().contains(nickname)) {
                return false;
            }
        }

        // 部门查询
        if (queryParams.containsKey("deptName") && StringUtils.hasText((String) queryParams.get("deptName"))) {
            String deptName = (String) queryParams.get("deptName");
            if (onlineUser.getDeptName() == null || !onlineUser.getDeptName().contains(deptName)) {
                return false;
            }
        }

        // IP地址查询
        if (queryParams.containsKey("ipAddress") && StringUtils.hasText((String) queryParams.get("ipAddress"))) {
            String ipAddress = (String) queryParams.get("ipAddress");
            if (onlineUser.getIpAddress() == null || !onlineUser.getIpAddress().contains(ipAddress)) {
                return false;
            }
        }

        return true;
    }

    @Override
    public void kickoutUser(Long userId) {
        if (userId != null) {
            try {
                StpUtil.kickout(userId);
                log.info("User {} has been kicked out", userId);
            } catch (Exception e) {
                log.error("Error kicking out user: {}", userId, e);
            }
        }
    }

    @Override
    public void batchKickoutUsers(List<Long> userIds) {
        if (!CollectionUtils.isEmpty(userIds)) {
            for (Long userId : userIds) {
                kickoutUser(userId);
            }
        }
    }

    @Override
    public void kickoutByToken(String tokenValue) {
        if (StringUtils.hasText(tokenValue)) {
            try {
                StpUtil.kickoutByTokenValue(tokenValue);
                log.info("Token {} has been kicked out", tokenValue);
            } catch (Exception e) {
                log.error("Error kicking out token: {}", tokenValue, e);
            }
        }
    }

    @Override
    public Map<String, Object> getOnlineUserStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        List<OnlineUser> onlineUsers = getOnlineUsers();
        
        // 总在线用户数
        statistics.put("totalOnline", onlineUsers.size());
        
        // 按部门统计
        Map<String, Long> deptStats = onlineUsers.stream()
            .filter(user -> user.getDeptName() != null)
            .collect(Collectors.groupingBy(OnlineUser::getDeptName, Collectors.counting()));
        statistics.put("deptStatistics", deptStats);
        
        // 按设备类型统计
        Map<String, Long> deviceStats = onlineUsers.stream()
            .filter(user -> user.getDeviceType() != null)
            .collect(Collectors.groupingBy(OnlineUser::getDeviceType, Collectors.counting()));
        statistics.put("deviceStatistics", deviceStats);
        
        // 按浏览器统计
        Map<String, Long> browserStats = onlineUsers.stream()
            .filter(user -> user.getBrowser() != null)
            .collect(Collectors.groupingBy(OnlineUser::getBrowser, Collectors.counting()));
        statistics.put("browserStatistics", browserStats);
        
        // 平均在线时长
        double avgDuration = onlineUsers.stream()
            .filter(user -> user.getOnlineDuration() != null)
            .mapToLong(OnlineUser::getOnlineDuration)
            .average()
            .orElse(0.0);
        statistics.put("averageOnlineDuration", Math.round(avgDuration));
        
        return statistics;
    }

    @Override
    public OnlineUser getOnlineUserDetail(Long userId) {
        if (userId == null) {
            return null;
        }
        return buildOnlineUser(userId);
    }

    @Override
    public boolean isUserOnline(Long userId) {
        if (userId == null) {
            return false;
        }
        try {
            return StpUtil.isLogin(userId);
        } catch (Exception e) {
            log.warn("Error checking if user is online: {}", userId, e);
            return false;
        }
    }

    @Override
    public long getOnlineUserCount() {
        try {
            return StpUtil.searchTokenValue("", 0, -1, false).size();
        } catch (Exception e) {
            log.error("Error getting online user count", e);
            return 0;
        }
    }

    @Override
    public List<Map<String, Object>> getUserLoginHistory(Long userId, int limit) {
        // 这里需要结合操作日志来实现
        // 暂时返回空列表，实际实现需要查询操作日志表
        return new ArrayList<>();
    }

    @Override
    public void cleanupExpiredOnlineUsers() {
        // Sa-Token会自动处理过期的会话，这里可以做一些额外的清理工作
        log.info("Cleaning up expired online users");
    }

    @Override
    public void updateLastActiveTime(Long userId) {
        if (userId != null && StpUtil.isLogin(userId)) {
            try {
                SaSession session = StpUtil.getSessionByLoginId(userId);
                if (session != null) {
                    session.set("lastActiveTime", LocalDateTime.now());
                }
            } catch (Exception e) {
                log.warn("Error updating last active time for user: {}", userId, e);
            }
        }
    }

    @Override
    public List<Map<String, Object>> getUserSessions(Long userId) {
        List<Map<String, Object>> sessions = new ArrayList<>();
        
        if (userId != null) {
            try {
                // 获取用户的所有Token
                List<String> tokenValues = StpUtil.getTokenValueListByLoginId(userId);
                
                for (String tokenValue : tokenValues) {
                    Map<String, Object> sessionInfo = new HashMap<>();
                    sessionInfo.put("tokenValue", tokenValue);
                    sessionInfo.put("userId", userId);
                    
                    // 可以添加更多会话信息
                    sessions.add(sessionInfo);
                }
            } catch (Exception e) {
                log.error("Error getting user sessions for: {}", userId, e);
            }
        }
        
        return sessions;
    }

    // 解析浏览器信息的辅助方法
    private String parseBrowser(String userAgent) {
        if (userAgent == null) return "Unknown";
        
        if (userAgent.contains("Chrome")) return "Chrome";
        if (userAgent.contains("Firefox")) return "Firefox";
        if (userAgent.contains("Safari")) return "Safari";
        if (userAgent.contains("Edge")) return "Edge";
        if (userAgent.contains("Opera")) return "Opera";
        
        return "Other";
    }

    // 解析操作系统信息的辅助方法
    private String parseOs(String userAgent) {
        if (userAgent == null) return "Unknown";
        
        if (userAgent.contains("Windows")) return "Windows";
        if (userAgent.contains("Mac")) return "macOS";
        if (userAgent.contains("Linux")) return "Linux";
        if (userAgent.contains("Android")) return "Android";
        if (userAgent.contains("iOS")) return "iOS";
        
        return "Other";
    }

    // 解析设备类型的辅助方法
    private String parseDeviceType(String userAgent) {
        if (userAgent == null) return "Unknown";
        
        if (userAgent.contains("Mobile") || userAgent.contains("Android") || userAgent.contains("iPhone")) {
            return "Mobile";
        }
        if (userAgent.contains("Tablet") || userAgent.contains("iPad")) {
            return "Tablet";
        }
        
        return "Desktop";
    }
}
