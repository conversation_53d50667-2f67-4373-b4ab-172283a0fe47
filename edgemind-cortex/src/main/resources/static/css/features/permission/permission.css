/* permission.css - Styles specific to the permission management module */

/* General enhancements for permission pages */
.container-fluid.p-4 {
    background-color: var(--bs-body-bg);
}

/* Consistent card styling */
.card {
    border: 1px solid var(--bs-border-color-translucent);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.2s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
}

/* Toolbar styling */
.toolbar-row {
    margin-bottom: 1.5rem;
}

/* Table refinements */
.table thead th {
    background-color: var(--bs-light);
    border-bottom-width: 1px;
    font-weight: 600;
    color: var(--bs-emphasis-color);
}

.table-hover > tbody > tr:hover > * {
    background-color: var(--bs-tertiary-bg);
}

.table .btn-group-sm .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.8rem;
}

/* Modal styling */
.modal-header {
    background-color: var(--bs-light);
    border-bottom: 1px solid var(--bs-border-color);
}

.modal-title {
    font-weight: 600;
}

/* Form styling enhancements */
.form-label {
    font-weight: 500;
}

/* Specific styles for departments.html tree view */
.department-tree-container {
    max-height: 600px;
    overflow-y: auto;
}

.tree-node {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.15s ease-in-out;
}

.tree-node:hover {
    background-color: var(--bs-tertiary-bg);
}

.tree-node.active > .node-content {
    background-color: var(--bs-primary-bg-subtle);
    border-radius: 0.25rem;
    font-weight: 600;
}

.node-content {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
}

.node-toggle {
    width: 20px;
    text-align: center;
    cursor: pointer;
    color: var(--bs-secondary-color);
}

.node-icon {
    margin-left: 0.25rem;
    margin-right: 0.5rem;
    color: var(--bs-secondary-color);
}

.node-text {
    flex-grow: 1;
}

.node-actions {
    display: none;
    margin-left: auto;
    padding-left: 0.5rem;
}

.tree-node:hover .node-actions {
    display: inline-block;
}

.children {
    padding-left: 1.5rem; /* Slightly reduced indent */
    border-left: 1px dashed var(--bs-border-color-translucent);
    margin-left: 0.75rem;
    margin-top: 0.25rem;
}

.dept-details {
    border-left: none; /* Remove border as card provides separation */
    padding-left: 0; 
}

.dept-info-card {
    background-color: var(--bs-body-bg);
}

.employee-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--bs-secondary-bg);
    color: var(--bs-secondary-color);
    display: inline-flex; /* Use inline-flex */
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    margin-right: 0.5rem; /* Add margin for spacing */
}

.table td, .table th {
    vertical-align: middle;
}

/* Specific styles for permissions.html */
.permission-block {
    margin-bottom: 1.5rem;
    border-radius: 0.375rem; /* Bootstrap's default card radius */
    border: 1px solid var(--bs-border-color-translucent);
    background-color: var(--bs-body-bg);
    overflow: hidden; /* Ensure header/items stay within rounded corners */
}

.permission-block-header {
    padding: 0.75rem 1.25rem;
    background-color: var(--bs-light);
    border-bottom: 1px solid var(--bs-border-color-translucent);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.permission-block-header h5 {
    margin-bottom: 0.1rem; 
}

.permission-items {
    padding: 0; /* Remove padding as items have their own */
}

.permission-item {
    padding: 0.75rem 1.25rem;
    border-bottom: 1px solid var(--bs-border-color-translucent);
    display: flex;
    align-items: center;
    gap: 0.75rem; /* Add gap between elements */
}

.permission-item:last-child {
    border-bottom: none;
}

.permission-item .form-check {
    margin-bottom: 0; /* Remove default bottom margin */
    min-width: 150px; /* Ensure alignment */
}

.permission-name {
    flex-grow: 1;
    color: var(--bs-body-color);
}

.permission-code {
    color: var(--bs-secondary-color);
    font-size: 0.8rem;
    font-family: var(--bs-font-monospace);
    background-color: var(--bs-secondary-bg);
    padding: 0.1rem 0.4rem;
    border-radius: 0.25rem;
    margin-left: auto; /* Push code and actions to the right */
}

.permission-actions {
    margin-left: 0.5rem;
}

/* Styles for logs.html */
.log-filter-card .card-body {
    padding: 1.25rem;
} 