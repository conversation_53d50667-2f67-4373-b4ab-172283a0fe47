/**
 * modal-component.js
 * 提供通用的 Bootstrap Modal 和 Toast 功能的模块
 */

// 检查 Bootstrap 是否已加载
if (typeof bootstrap === 'undefined') {
    console.warn("ModalComponent Warning: Bootstrap is not loaded! Some functionality may be limited.");
}

const defaultModalOptions = {
    modalId: 'globalModal',
    title: '提示',
    bodyHtml: '',
    confirmText: '确认',
    cancelText: '取消',
    confirmButtonClass: 'btn-primary',
    cancelButtonClass: 'btn-secondary',
    backdrop: true, // boolean or 'static'
    backdropZIndex: 1050, // 设置backdrop的z-index
    keyboard: true,
    focus: true,
    size: null, // 'sm', 'lg', 'xl'
    scrollable: false,
    centered: false,
    modalClass: '', // 额外的CSS类
    onConfirm: null, // async function(modalInstance) => boolean (return false to prevent close)
    onCancel: null,  // function(modalInstance)
    onShown: null,   // function(modalInstance)
    onHidden: null   // function(modalInstance)
};

// 创建或获取 Modal DOM
function getOrCreateModalElement(options) {
    let modalElement = document.getElementById(options.modalId);
    if (!modalElement) {
        const sizeClass = options.size ? `modal-${options.size}` : '';
        const scrollableClass = options.scrollable ? 'modal-dialog-scrollable' : '';
        const centeredClass = options.centered ? 'modal-dialog-centered' : '';

        const modalHtml = `
            <div class="modal fade ${options.modalClass}" id="${options.modalId}" tabindex="-1" aria-labelledby="${options.modalId}Label" aria-hidden="true" style="z-index: 1060;">
              <div class="modal-dialog ${sizeClass} ${scrollableClass} ${centeredClass}">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="${options.modalId}Label">${options.title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    ${options.bodyHtml}
                  </div>
                  <div class="modal-footer">
                    ${options.cancelText ? `<button type="button" class="btn ${options.cancelButtonClass} modal-cancel-btn" data-bs-dismiss="modal">${options.cancelText}</button>` : ''}
                    ${options.confirmText ? `<button type="button" class="btn ${options.confirmButtonClass} modal-confirm-btn">${options.confirmText}</button>` : ''}
                  </div>
                </div>
              </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        modalElement = document.getElementById(options.modalId);
    }
    return modalElement;
}

// 显示 Modal
async function show(userOptions = {}) {
    if (!bootstrap || !bootstrap.Modal) {
        console.error("ModalComponent.show Error: Bootstrap Modal component not found.");
        return null;
    }

    const options = { ...defaultModalOptions, ...userOptions };
    const modalElement = getOrCreateModalElement(options);

    // 更新 Modal 内容 (如果已存在，则更新)
    modalElement.querySelector('.modal-title').textContent = options.title;
    modalElement.querySelector('.modal-body').innerHTML = options.bodyHtml;
    
    // 更新模态框CSS类
    if (options.modalClass) {
        // 移除之前的统一样式类
        modalElement.classList.remove('modal-unified', 'modal-rename', 'modal-delete');
        // 添加新的CSS类
        const classes = options.modalClass.split(' ').filter(cls => cls.trim());
        modalElement.classList.add(...classes);
    }
    const confirmBtn = modalElement.querySelector('.modal-confirm-btn');
    const cancelBtn = modalElement.querySelector('.modal-cancel-btn');
    const footer = modalElement.querySelector('.modal-footer');

    if (footer) {
        footer.innerHTML = ''; // Clear previous buttons
        if (options.cancelText) {
             footer.innerHTML += `<button type="button" class="btn ${options.cancelButtonClass} modal-cancel-btn" data-bs-dismiss="modal">${options.cancelText}</button>`;
        }
        if (options.confirmText) {
             footer.innerHTML += `<button type="button" class="btn ${options.confirmButtonClass} modal-confirm-btn">${options.confirmText}</button>`;
        }
    }

    // 应用其他选项 (size, scrollable, centered 通常在创建时设置)
    const modalDialog = modalElement.querySelector('.modal-dialog');
    if(modalDialog) {
        modalDialog.classList.toggle('modal-dialog-scrollable', options.scrollable);
        modalDialog.classList.toggle('modal-dialog-centered', options.centered);
        modalDialog.classList.remove('modal-sm', 'modal-lg', 'modal-xl');
        if(options.size) modalDialog.classList.add(`modal-${options.size}`);
    }

    const modalInstance = bootstrap.Modal.getOrCreateInstance(modalElement, {
        backdrop: options.backdrop,
        keyboard: options.keyboard,
        focus: options.focus
    });

    // 移除旧监听器，添加新监听器
    const confirmButton = modalElement.querySelector('.modal-confirm-btn');
    const cancelButton = modalElement.querySelector('.modal-cancel-btn');
    let confirmHandler, cancelHandler, shownHandler, hiddenHandler;

    if (confirmButton && options.onConfirm) {
        confirmHandler = async () => {
            confirmButton.disabled = true; confirmButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';
            let shouldClose = true;
            try {
                shouldClose = await options.onConfirm(modalInstance);
            } catch (e) {
                console.error("Modal onConfirm error:", e);
                // Optionally show error to user
            } finally {
                confirmButton.disabled = false; confirmButton.textContent = options.confirmText;
                if (shouldClose !== false) { // Allow closing if true, undefined, or null
                    modalInstance.hide();
                }
            }
        };
        confirmButton.addEventListener('click', confirmHandler);
    }
    if (cancelButton && options.onCancel) {
        cancelHandler = () => options.onCancel(modalInstance);
        cancelButton.addEventListener('click', cancelHandler);
    }
    if (options.onShown) {
        shownHandler = () => options.onShown(modalInstance);
        modalElement.addEventListener('shown.bs.modal', shownHandler, { once: true });
    }
    if (options.onHidden) {
        hiddenHandler = () => options.onHidden(modalInstance);
        modalElement.addEventListener('hidden.bs.modal', hiddenHandler, { once: true });
    }

    // 清理事件监听器
    modalElement.addEventListener('hidden.bs.modal', () => {
        if (confirmButton && confirmHandler) confirmButton.removeEventListener('click', confirmHandler);
        if (cancelButton && cancelHandler) cancelButton.removeEventListener('click', cancelHandler);
        // shown 和 hidden 是一次性的，不需要移除
        // 考虑是否在隐藏后销毁 modalInstance 或 DOM，取决于 modalId 是否全局复用
        // bootstrap.Modal.getInstance(modalElement)?.dispose();
    }, { once: true });

    modalInstance.show();

    // 调整backdrop的z-index以避免遮挡问题
    setTimeout(() => {
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop && options.backdropZIndex) {
            backdrop.style.zIndex = options.backdropZIndex;
        }
    }, 10);

    return modalInstance;
}

// Toast 功能 - 极简白色风格
function showToast(message, type = 'info', delay = 1500) {
    if (!bootstrap || !bootstrap.Toast) {
        console.warn("showToast Warning: Bootstrap Toast component not found. Using alert fallback.");
        alert(`${type.toUpperCase()}: ${message}`);
        return;
    }

    const toastContainer = document.querySelector('.toast-container'); // Assumes container exists in layout.html
    if (!toastContainer) {
        console.error("showToast Error: Toast container (.toast-container) not found!");
        alert(`${type.toUpperCase()}: ${message}`);
        return;
    }

    const toastId = 'global-toast-' + Date.now();
    
    // 根据类型设置样式
    const typeConfig = {
        info: {
            bg: '#ffffff',
            border: '#e3f2fd',
            text: '#1976d2'
        },
        success: {
            bg: '#ffffff',
            border: '#e8f5e8',
            text: '#2e7d32'
        },
        warning: {
            bg: '#ffffff',
            border: '#fff3e0',
            text: '#f57c00'
        },
        error: {
            bg: '#ffffff',
            border: '#ffebee',
            text: '#d32f2f'
        }
    };

    const config = typeConfig[type] || typeConfig.info;

    const toastHtml = `
        <div id="${toastId}" class="toast border-0" role="alert" aria-live="assertive" aria-atomic="true" 
             style="background-color: ${config.bg}; border: 1px solid ${config.border}; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 8px; min-width: 300px; max-width: 400px;" 
             data-bs-delay="${delay}">
            <div class="d-flex align-items-center p-3">
                <div class="toast-body flex-grow-1" style="color: ${config.text}; font-size: 14px; padding: 0; margin: 0; line-height: 1.5; font-weight: 400;">
                    ${message}
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close" 
                        style="width: 18px; height: 18px; font-size: 10px; opacity: 0.3; flex-shrink: 0; margin-left: 12px; background-size: 8px; border-radius: 50%; transition: all 0.2s ease;" 
                        onmouseover="this.style.opacity='0.6'; this.style.backgroundColor='rgba(0,0,0,0.08)'" 
                        onmouseout="this.style.opacity='0.3'; this.style.backgroundColor='transparent'"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    const toastElement = document.getElementById(toastId);

    if (toastElement) {
        // 显式设置自动隐藏选项和延迟时间
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: delay
        });
        // Remove the element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => toastElement.remove(), { once: true });
        toast.show();
    }
}

// 导出模块接口
const ModalComponent = {
    show: show
};

export { ModalComponent, showToast };