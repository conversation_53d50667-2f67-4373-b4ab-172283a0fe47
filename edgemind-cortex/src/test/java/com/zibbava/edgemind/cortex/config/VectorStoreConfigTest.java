package com.zibbava.edgemind.cortex.config;

import com.zibbava.edgemind.cortex.store.WeaviateEmbeddingStore;
import dev.langchain4j.store.embedding.EmbeddingStore;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VectorStoreConfig 测试类
 * 验证从 Milvus 切换到 Weaviate 的配置是否正确
 */
@SpringBootTest
@TestPropertySource(properties = {
    "weaviate.host=localhost",
    "weaviate.port=8080",
    "weaviate.class.name=TestDocument",
    "weaviate.dimension=1024",
    "weaviate.hybrid.enabled=true",
    "weaviate.hybrid.alpha=0.7"
})
class VectorStoreConfigTest {

    @Test
    void testVectorStoreConfigCreation() {
        // 测试配置类能够正确创建
        VectorStoreConfig config = new VectorStoreConfig();
        assertNotNull(config);
    }

    @Test
    void testWeaviateEmbeddingStoreBuilder() {
        // 测试 WeaviateEmbeddingStore 的 builder 模式
        WeaviateEmbeddingStore store = WeaviateEmbeddingStore.builder()
                .host("localhost")
                .port(8080)
                .className("TestDocument")
                .dimension(1024)
                .enableHybridSearch(true)
                .alpha(0.7f)
                .build();
        
        assertNotNull(store);
        assertTrue(store instanceof EmbeddingStore);
        
        // 验证统计信息
        var stats = store.getStats();
        assertEquals("TestDocument", stats.get("className"));
        assertEquals(1024, stats.get("dimension"));
        assertEquals(true, stats.get("enableHybridSearch"));
        assertEquals(0.7f, stats.get("alpha"));
        
        // 清理资源
        store.close();
    }
} 