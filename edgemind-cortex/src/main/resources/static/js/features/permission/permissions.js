// src/main/resources/static/js/features/permission/permissions.js

document.addEventListener('DOMContentLoaded', () => {
    

    const permissionListContainer = document.getElementById('permissionListContainer');
    const addPermissionModalElement = document.getElementById('addPermissionModal');
    const addPermissionModal = new bootstrap.Modal(addPermissionModalElement);
    const editPermissionModalElement = document.getElementById('editPermissionModal');
    const editPermissionModal = new bootstrap.Modal(editPermissionModalElement);
    const addPermissionForm = document.getElementById('addPermissionForm');
    const editPermissionForm = document.getElementById('editPermissionForm');

    // --- Function to fetch permission data --- 
    async function fetchPermissions() {
        permissionListContainer.innerHTML = `<div class="text-center p-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>`;
        try {
            const response = await fetch('/wkg/api/permission/permissions');
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const permissions = await response.json();
            renderPermissionList(permissions || []);
        } catch (error) {
            console.error('Error fetching permissions:', error);
            showToast('Error fetching permission list.', 'error');
            permissionListContainer.innerHTML = '<div class="alert alert-danger">Failed to load permissions.</div>';
        }
    }

    // --- Function to render the permission list (Grouped) --- 
    function renderPermissionList(permissions) {
        permissionListContainer.innerHTML = '';
        if (!permissions || permissions.length === 0) {
            permissionListContainer.innerHTML = '<div class="text-center text-muted p-3">No permissions found.</div>';
            return;
        }
        const groupedPermissions = permissions.reduce((acc, perm) => {
            const groupKey = perm.type || 'OTHER';
            if (!acc[groupKey]) acc[groupKey] = [];
            acc[groupKey].push(perm);
            return acc;
        }, {});
        const groupDetails = {
            MENU: { title: '菜单访问权限', color: 'primary', icon: 'bi-list-ul' },
            BUTTON: { title: '按钮操作权限', color: 'info', icon: 'bi-hand-index-thumb' },
            API: { title: '接口访问权限', color: 'warning', icon: 'bi-hdd-network' },
            DATA: { title: '数据范围权限', color: 'danger', icon: 'bi-database-lock' },
            OTHER: { title: '其他权限', color: 'secondary', icon: 'bi-gear' }
        };
        const groupOrder = ['MENU', 'BUTTON', 'API', 'DATA', 'OTHER'];
        groupOrder.forEach(groupKey => {
            if (groupedPermissions[groupKey]) {
                const groupInfo = groupDetails[groupKey] || groupDetails.OTHER;
                const groupPermissions = groupedPermissions[groupKey];
                // Sort permissions within the group, e.g., by code
                groupPermissions.sort((a, b) => (a.permissionCode || '').localeCompare(b.permissionCode || ''));
                const block = document.createElement('div');
                block.classList.add('permission-block');
                block.innerHTML = `
                    <div class="permission-block-header">
                        <div>
                            <h5 class="mb-0 fw-bold"><i class="${groupInfo.icon} me-2"></i>${groupInfo.title}</h5>
                            <small class="text-muted">${groupPermissions.length} 项</small>
                        </div>
                        <div>
                            <span class="badge text-bg-${groupInfo.color}">${groupKey}</span>
                        </div>
                    </div>
                    <div class="permission-items list-group list-group-flush"> 
                        ${groupPermissions.map(perm => renderPermissionItem(perm)).join('')}                 
                    </div>
                `;
                permissionListContainer.appendChild(block);
            }
        });
        addListItemButtonListeners();
    }

    // --- Function to render a single permission item --- 
    function renderPermissionItem(perm) {
        const statusBadgeClass = perm.status === 1 ? 'text-success' : 'text-danger';
        const statusIcon = perm.status === 1 ? 'bi-check-circle-fill' : 'bi-x-circle-fill';
        return `
            <div class="list-group-item permission-item d-flex align-items-start">
                <div class="w-100">
                     <i class="${statusIcon} ${statusBadgeClass} me-2" title="${perm.status === 1 ? '启用' : '禁用'}"></i>
                     <span class="permission-name fw-medium">${perm.permissionName || '-'}</span>
                     <code class="permission-code ms-2">${perm.permissionCode || '-'}</code>
                     <p class="text-muted small mb-0 ps-4">${perm.description || '无描述'}</p>
                     ${perm.parentId ? `<small class="text-muted d-block ps-4">父级 ID: ${perm.parentId}</small>` : ''} 
                </div>
            </div>
        `;
    }

    // --- Add listeners for list item buttons --- 
    function addListItemButtonListeners() {
        // 按钮已移除，此函数保留以备将来需要
    }
    
    // --- Function to fetch parent permissions (for modal dropdown) ---
    async function fetchParentPermissionOptions() {
        try {
            const response = await fetch('/wkg/api/permission/permissions');
            if (!response.ok) throw new Error('Failed to fetch parent options');
            const permissions = await response.json();
            return permissions.filter(p => p.type !== 'API' && p.type !== 'DATA'); 
        } catch (error) {
            console.error('Error fetching parent permissions:', error);
            showToast('Error loading parent options', 'error');
            return [];
        }
    }

    // --- Function to populate select options (reuse if possible) --- 
    function populateSelect(selectElementId, options, valueField = 'id', textField = 'name', descriptionField = null) {
        const selectElement = document.getElementById(selectElementId);
        const label = document.querySelector(`label[for='${selectElementId}']`);
        const placeholderText = label ? `请选择${label.innerText.replace('*','').trim()}` : '请选择';
        selectElement.innerHTML = `<option value="">${placeholderText}</option>`;
        if (options && options.length > 0) {
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option[valueField];
                let text = option[textField];
                if (descriptionField && option[descriptionField]) {
                     text += ` (${option[descriptionField]})`;
                }
                optionElement.textContent = text;
                selectElement.appendChild(optionElement);
            });
        }
    }

    // --- Event Handlers ---
    async function handleEditClick(event) {
        const permissionId = event.currentTarget.dataset.permissionId;

        editPermissionForm.reset();
        editPermissionForm.classList.remove('was-validated');
        editPermissionModal.show(); 
        try {
            const response = await fetch(`/wkg/api/permission/permissions/${permissionId}`);
            if (!response.ok) {
                if (response.status === 404) throw new Error(`权限 ID ${permissionId} 未找到.`);
                throw new Error(`获取权限详情失败: ${response.status}`);
            }
            const permData = await response.json();
            document.getElementById('editPermissionId').value = permData.id;
            document.getElementById('editPermissionName').value = permData.permissionName || '';
            document.getElementById('editPermissionCode').value = permData.permissionCode || '';
            document.getElementById('editPermissionType').value = permData.type || '';
            document.getElementById('editPermissionDescription').value = permData.description || '';
            document.getElementById('editPermissionStatus').value = permData.status !== null ? permData.status : '1';
            const parentSelect = document.getElementById('editParentPermission'); 
            const parentOptions = await fetchParentPermissionOptions();
            const filteredParentOptions = parentOptions.filter(p => p.id !== permData.id); 
            populateSelect(parentSelect.id, filteredParentOptions, 'id', 'permissionName', 'permissionCode');
            parentSelect.value = permData.parentId || ''; 
        } catch (error) {
            console.error('Error preparing edit permission modal:', error);
            showToast(`加载编辑数据出错: ${error.message}`, 'error');
            editPermissionModal.hide();
        }
    }

    async function handleDeleteClick(event) {
        const permissionId = event.currentTarget.dataset.permissionId;
        const permissionName = event.currentTarget.dataset.permissionName;

        if (!confirm(`确定要删除权限 "${permissionName || permissionId}" 吗？\n注意：此操作可能影响角色分配，且无法撤销。`)) {
            return;
        }
        event.currentTarget.disabled = true;
        event.currentTarget.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
        try {
            const response = await fetch(`/wkg/api/permission/permissions/${permissionId}`, {
                method: 'DELETE',
                headers: { /* 'Sa-Token': ... */ }
            });
            if (response.ok) {
                showToast(`权限 "${permissionName || permissionId}" 删除成功!`, 'success');
                fetchPermissions(); // Refresh list
            } else {
                const errorData = await response.json().catch(() => ({ message: '删除权限失败，请检查是否有关联或子权限' }));
                showToast(`错误: ${errorData.message || response.statusText}`, 'error');
                event.currentTarget.disabled = false;
                event.currentTarget.innerHTML = '<i class="bi bi-trash"></i>';
            }
        } catch (error) {
             console.error("Error deleting permission:", error); // Add console log
             showToast('发生意外错误', 'error');
             event.currentTarget.disabled = false;
             event.currentTarget.innerHTML = '<i class="bi bi-trash"></i>';
        }
    }
    
    // --- Handle Add Permission Modal --- 
    addPermissionModalElement?.addEventListener('show.bs.modal', async () => {
        addPermissionForm.reset();
        addPermissionForm.classList.remove('was-validated');
        const parentSelect = document.getElementById('addParentPermission');
        const parentOptions = await fetchParentPermissionOptions();
        populateSelect(parentSelect.id, parentOptions, 'id', 'permissionName', 'permissionCode');
        parentSelect.value = '';
    });

    // --- Add Permission Form Submission --- 
    addPermissionForm?.addEventListener('submit', async (event) => {
        event.preventDefault();
        event.stopPropagation();
        if (!addPermissionForm.checkValidity()) {
            addPermissionForm.classList.add('was-validated');
            addPermissionForm.querySelector(':invalid')?.focus();
            return;
        }
        addPermissionForm.classList.add('was-validated');
        const formData = new FormData(addPermissionForm);
        const permissionData = Object.fromEntries(formData.entries());
        permissionData.status = parseInt(permissionData.status || '1');
        permissionData.parentId = permissionData.parentId ? parseInt(permissionData.parentId) : null;
        const submitButton = addPermissionForm.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
        try {
            const response = await fetch('/wkg/api/permission/permissions', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' /*, 'Sa-Token': ... */ },
                body: JSON.stringify(permissionData),
            });
            if (response.ok) {
                addPermissionModal.hide();
                showToast('权限添加成功!', 'success');
                fetchPermissions();
            } else {
                const errorData = await response.json().catch(() => ({ message: '添加权限失败，请检查编码是否唯一' }));
                showToast(`错误: ${errorData.message || response.statusText}`, 'error');
            }
        } catch(error) {
             showToast('发生意外错误', 'error');
        } finally {
            submitButton.disabled = false;
             submitButton.innerHTML = '保存';
        }
    });
    
     // --- Edit Permission Form Submission --- 
     editPermissionForm?.addEventListener('submit', async (event) => {
         event.preventDefault();
         event.stopPropagation();
         if (!editPermissionForm.checkValidity()) {
              editPermissionForm.classList.add('was-validated');
              editPermissionForm.querySelector(':invalid')?.focus();
              return;
         }
         editPermissionForm.classList.add('was-validated');
         const formData = new FormData(editPermissionForm);
         const permissionData = Object.fromEntries(formData.entries());
         permissionData.id = parseInt(permissionData.id);
         permissionData.status = parseInt(permissionData.status || '1');
         permissionData.parentId = permissionData.parentId ? parseInt(permissionData.parentId) : null;
         const submitButton = editPermissionForm.querySelector('button[type="submit"]');
         submitButton.disabled = true;
         submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
         try {
             const response = await fetch(`/wkg/api/permission/permissions/${permissionData.id}`, {
                 method: 'PUT',
                 headers: { 'Content-Type': 'application/json' /*, 'Sa-Token': ... */ },
                 body: JSON.stringify(permissionData),
             });
             if (response.ok) {
                 editPermissionModal.hide();
                 showToast('权限信息更新成功!', 'success');
                 fetchPermissions();
             } else {
                 const errorData = await response.json().catch(() => ({ message: '更新权限失败，请检查输入' }));
                 showToast(`错误: ${errorData.message || response.statusText}`, 'error');
             }
         } catch (error) {
             console.error("Error updating permission:", error); // Add console log
             showToast('发生意外错误', 'error');
         } finally {
             submitButton.disabled = false;
             submitButton.innerHTML = '保存更改';
         }
     });

    // --- Toast Notification Helper - 使用全局组件 ---
    function showToast(message, type = 'info') {
        // 使用全局Toast组件显示消息
        window.parent?.postMessage({
            type: 'SHOW_TOAST',
            payload: { message: message, type: type }
        }, '*');
    }

    // --- Initial Load --- 
    fetchPermissions();

});