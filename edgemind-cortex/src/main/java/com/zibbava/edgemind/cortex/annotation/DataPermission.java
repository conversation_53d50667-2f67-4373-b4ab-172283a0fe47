package com.zibbava.edgemind.cortex.annotation;

import java.lang.annotation.*;

/**
 * 数据权限注解
 * 用于标记需要进行数据权限控制的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataPermission {

    /**
     * 数据权限类型
     */
    DataScope dataScope() default DataScope.ALL;

    /**
     * 用户表的别名
     */
    String userAlias() default "u";

    /**
     * 部门表的别名
     */
    String deptAlias() default "d";

    /**
     * 权限字段名称
     */
    String permission() default "dept_id";

    /**
     * 数据权限范围枚举
     */
    enum DataScope {
        /**
         * 全部数据权限
         */
        ALL,
        
        /**
         * 自定义数据权限
         */
        CUSTOM,
        
        /**
         * 部门数据权限
         */
        DEPT,
        
        /**
         * 部门及以下数据权限
         */
        DEPT_AND_CHILD,
        
        /**
         * 仅本人数据权限
         */
        SELF
    }
}
