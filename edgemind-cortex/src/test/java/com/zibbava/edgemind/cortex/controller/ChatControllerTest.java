package com.zibbava.edgemind.cortex.controller;

import com.zibbava.edgemind.cortex.base.BaseControllerTest;
import com.zibbava.edgemind.cortex.entity.ChatConversation;
import com.zibbava.edgemind.cortex.entity.ChatMessage;
import com.zibbava.edgemind.cortex.service.ChatService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ChatController 单元测试
 * 测试聊天相关接口功能，包括对话管理、消息处理等
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
@DisplayName("聊天控制器测试")
class ChatControllerTest extends BaseControllerTest {

    @MockBean
    private ChatService chatService;

    private ChatConversation conversation;
    private ChatMessage message;
    private List<ChatConversation> conversations;
    private List<ChatMessage> messages;

    @Override
    protected void setUp() {
        conversation = new ChatConversation();
        conversation.setId(1L);
        conversation.setUserId(getMockUserId());
        conversation.setTitle("测试对话");
        conversation.setCreateTime(LocalDateTime.now());

        message = new ChatMessage();
        message.setId(1L);
        message.setConversationId(1L);
        message.setContent("测试消息");
        message.setSender("user");
        message.setCreateTime(LocalDateTime.now());

        conversations = Arrays.asList(conversation);
        messages = Arrays.asList(message);
    }

    @Test
    @DisplayName("获取用户对话列表 - 成功")
    void getUserConversations_Success() throws Exception {
        // Given
        when(chatService.getUserConversationsCount(getMockUserId())).thenReturn(1);
        when(chatService.getUserConversationsPaged(getMockUserId(), 0, 20)).thenReturn(conversations);

        // When & Then
        mockMvc.perform(withAuth(get("/api/conversations"))
                        .param("page", "0")
                        .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].id").value(1))
                .andExpect(jsonPath("$.content[0].title").value("测试对话"))
                .andExpect(jsonPath("$.totalElements").value(1))
                .andExpect(jsonPath("$.totalPages").value(1))
                .andExpect(jsonPath("$.size").value(20))
                .andExpect(jsonPath("$.page").value(0));

        verify(chatService).getUserConversationsCount(getMockUserId());
        verify(chatService).getUserConversationsPaged(getMockUserId(), 0, 20);
    }

    @Test
    @DisplayName("创建新对话 - 成功")
    void createConversation_Success() throws Exception {
        // Given
        when(chatService.createConversation(getMockUserId(), "新对话")).thenReturn(conversation);

        Map<String, Object> params = new HashMap<>();
        params.put("title", "新对话");

        // When & Then
        mockMvc.perform(withAuth(post("/api/conversations"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(params)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.title").value("测试对话"));

        verify(chatService).createConversation(getMockUserId(), "新对话");
    }

    @Test
    @DisplayName("创建新对话 - 标题过长")
    void createConversation_TitleTooLong() throws Exception {
        // Given
        Map<String, Object> params = new HashMap<>();
        params.put("title", "这是一个非常长的标题超过了十个字符的限制");

        // When & Then
        mockMvc.perform(withAuth(post("/api/conversations"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(params)))
                .andExpect(status().isBadRequest());

        verifyNoInteractions(chatService);
    }

    @Test
    @DisplayName("获取对话消息 - 成功")
    void getConversationMessages_Success() throws Exception {
        // Given
        when(chatService.getConversation(1L)).thenReturn(conversation);
        when(chatService.getConversationMessagesCount(1L)).thenReturn(1);
        when(chatService.getConversationMessagesPaged(1L, 0, 20)).thenReturn(messages);

        // When & Then
        mockMvc.perform(withAuth(get("/api/conversations/1/messages"))
                        .param("page", "0")
                        .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].id").value(1))
                .andExpect(jsonPath("$.content[0].content").value("测试消息"))
                .andExpect(jsonPath("$.totalElements").value(1))
                .andExpect(jsonPath("$.totalPages").value(1));

        verify(chatService).getConversation(1L);
        verify(chatService).getConversationMessagesCount(1L);
        verify(chatService).getConversationMessagesPaged(1L, 0, 20);
    }

    @Test
    @DisplayName("获取对话消息 - 无权限访问")
    void getConversationMessages_Forbidden() throws Exception {
        // Given
        ChatConversation otherUserConversation = new ChatConversation();
        otherUserConversation.setId(1L);
        otherUserConversation.setUserId(999L); // 不同的用户ID
        when(chatService.getConversation(1L)).thenReturn(otherUserConversation);

        // When & Then
        mockMvc.perform(withAuth(get("/api/conversations/1/messages")))
                .andExpect(status().isForbidden());

        verify(chatService).getConversation(1L);
        verify(chatService, never()).getConversationMessagesCount(anyLong());
        verify(chatService, never()).getConversationMessagesPaged(anyLong(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("更新对话标题 - 成功")
    void updateConversationTitle_Success() throws Exception {
        // Given
        when(chatService.getConversation(1L)).thenReturn(conversation);
        when(chatService.updateConversationTitle(1L, "新标题")).thenReturn(true);

        Map<String, Object> params = new HashMap<>();
        params.put("title", "新标题");

        // When & Then
        mockMvc.perform(withAuth(put("/api/conversations/1/title"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(params)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("对话标题更新成功"));

        verify(chatService).getConversation(1L);
        verify(chatService).updateConversationTitle(1L, "新标题");
    }

    @Test
    @DisplayName("更新对话标题 - 标题为空")
    void updateConversationTitle_EmptyTitle() throws Exception {
        // Given
        Map<String, Object> params = new HashMap<>();
        params.put("title", "");

        // When & Then
        mockMvc.perform(withAuth(put("/api/conversations/1/title"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(params)))
                .andExpect(status().isBadRequest());

        verifyNoInteractions(chatService);
    }

    @Test
    @DisplayName("更新对话标题 - 标题过长")
    void updateConversationTitle_TitleTooLong() throws Exception {
        // Given
        Map<String, Object> params = new HashMap<>();
        params.put("title", "这是一个非常长的标题超过了十个字符的限制");

        // When & Then
        mockMvc.perform(withAuth(put("/api/conversations/1/title"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(params)))
                .andExpect(status().isBadRequest());

        verifyNoInteractions(chatService);
    }

    @Test
    @DisplayName("删除对话 - 成功")
    void deleteConversation_Success() throws Exception {
        // Given
        when(chatService.getConversation(1L)).thenReturn(conversation);
        when(chatService.deleteConversation(1L)).thenReturn(true);

        // When & Then
        mockMvc.perform(withAuth(delete("/api/conversations/1")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("对话删除成功"));

        verify(chatService).getConversation(1L);
        verify(chatService).deleteConversation(1L);
    }

    @Test
    @DisplayName("删除对话 - 无权限访问")
    void deleteConversation_Forbidden() throws Exception {
        // Given
        ChatConversation otherUserConversation = new ChatConversation();
        otherUserConversation.setId(1L);
        otherUserConversation.setUserId(999L); // 不同的用户ID
        when(chatService.getConversation(1L)).thenReturn(otherUserConversation);

        // When & Then
        mockMvc.perform(withAuth(delete("/api/conversations/1")))
                .andExpect(status().isForbidden());

        verify(chatService).getConversation(1L);
        verify(chatService, never()).deleteConversation(anyLong());
    }

    @Test
    @DisplayName("保存消息 - 成功")
    void saveMessages_Success() throws Exception {
        // Given
        Map<String, Object> params = new HashMap<>();
        params.put("conversationId", 1L);
        params.put("userMessage", "用户消息");
        params.put("assistantMessage", "助手回复");

        when(chatService.getConversation(1L)).thenReturn(conversation);

        // When & Then
        mockMvc.perform(withAuth(post("/api/conversations/messages"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(params)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("消息保存成功"));

        verify(chatService).getConversation(1L);
        verify(chatService).saveUserMessage(eq(1L), eq("用户消息"), isNull());
        verify(chatService).saveAiMessage(eq(1L), eq("助手回复"), isNull(), isNull(), isNull());
    }

    @Test
    @DisplayName("初始化对话 - 成功")
    void initializeConversations_Success() throws Exception {
        // Given
        when(chatService.getRecentUserConversations(getMockUserId(), 30)).thenReturn(conversations);

        // When & Then
        mockMvc.perform(withAuth(get("/api/conversations/init"))
                        .param("limit", "30"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.conversations").isArray())
                .andExpect(jsonPath("$.conversations[0].id").value(1))
                .andExpect(jsonPath("$.conversations[0].title").value("测试对话"));

        verify(chatService).getRecentUserConversations(getMockUserId(), 30);
    }
} 