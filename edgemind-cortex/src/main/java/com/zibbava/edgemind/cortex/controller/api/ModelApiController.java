package com.zibbava.edgemind.cortex.controller.api;

import cn.dev33.satoken.annotation.SaIgnore;
import com.zibbava.edgemind.cortex.common.Result;
import com.zibbava.edgemind.cortex.dto.ModelDownloadRequest;
import com.zibbava.edgemind.cortex.dto.SystemInfoResponse;
import com.zibbava.edgemind.cortex.service.ModelService;
import com.zibbava.edgemind.cortex.service.SystemInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 模型API控制器
 * 提供模型相关REST API接口
 */
@RestController
@RequestMapping("/api/models")
@RequiredArgsConstructor
@Slf4j
public class ModelApiController {

    private final ModelService modelService;
    private final SystemInfoService systemInfoService;

    /**
     * 获取系统硬件信息
     *
     * @return 系统硬件信息
     */
    @GetMapping("/system-info")
    public Result<SystemInfoResponse> getSystemInfo() {
        log.info("获取系统硬件信息");
        return Result.success(systemInfoService.getSystemInfo());
    }

    /**
     * 获取可用模型列表
     *
     * @return 模型列表
     */
    @GetMapping("/available")
    public Result<List<Map<String, Object>>> getAvailableModels() {
        log.info("获取可用模型列表");
        return Result.success(modelService.getAvailableModels());
    }

    /**
     * 获取已安装的模型列表
     *
     * @return 模型列表
     */
    @GetMapping("/installed")
    public Result<List<Map<String, Object>>> getInstalledModels() {
        log.info("获取已安装模型列表");
        return Result.success(modelService.getInstalledModels());
    }

    /**
     * 下载模型
     *
     * @param request 下载请求
     * @return 操作结果
     */
    @PostMapping("/download")
    public Result<Map<String, Object>> downloadModel(@RequestBody ModelDownloadRequest request) {
        log.info("下载模型: {}", request.getModelName());
        return Result.success(modelService.startModelDownload(request));
    }

    /**
     * 获取模型下载进度
     *
     * @param taskId 下载任务ID
     * @return 下载进度信息
     */
    @GetMapping("/download/progress/{taskId}")
    public Result<Map<String, Object>> getDownloadProgress(@PathVariable String taskId) {
        return Result.success(modelService.getDownloadProgress(taskId));
    }

    /**
     * 获取模型下载实时进度（使用SSE）
     *
     * @param taskId 下载任务ID
     * @return 事件流
     */
    @SaIgnore
    @GetMapping(value = "/download/stream/{taskId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public org.springframework.web.servlet.mvc.method.annotation.SseEmitter streamDownloadProgress(@PathVariable String taskId) {
        return modelService.streamDownloadProgress(taskId);
    }
    
    /**
     * 删除模型
     *
     * @return 操作结果
     */
    @PostMapping("/delete")
    public Result<Boolean> deleteModel(@RequestBody Map<String, String> request) {
        String modelName = request.get("modelName");
        log.info("删除模型: {}", modelName);
        return Result.success(modelService.deleteModel(modelName));
    }
}
