<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限设置</title>
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/features/permission/permission.css">
    <style>
        .permission-tree .card {
            transition: all 0.2s;
        }
        .permission-tree .card:hover {
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
        }
        .resource-badge {
            font-size: 0.75rem;
            padding: 0.15rem 0.5rem;
            border-radius: 1rem;
        }
        .permission-block {
            margin-bottom: 1.5rem;
            border-radius: 0.375rem;
            border: 1px solid var(--bs-border-color-translucent);
            background-color: var(--bs-body-bg);
            overflow: hidden;
        }
        .permission-block-header {
            padding: 0.75rem 1.25rem;
            background-color: var(--bs-light);
            border-bottom: 1px solid var(--bs-border-color-translucent);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .permission-block-header h5 {
            margin-bottom: 0.1rem;
        }
        .permission-items {
            padding: 0;
        }
        .permission-item {
            padding: 0.75rem 1.25rem;
            border-bottom: 1px solid var(--bs-border-color-translucent);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        .permission-item:last-child {
            border-bottom: none;
        }
        .permission-item .form-check {
            margin-bottom: 0;
            min-width: 150px;
        }
        .permission-name {
            flex-grow: 1;
            color: var(--bs-body-color);
        }
        .permission-code {
            color: var(--bs-secondary-color);
            font-size: 0.8rem;
            font-family: var(--bs-font-monospace);
            background-color: var(--bs-secondary-bg);
            padding: 0.1rem 0.4rem;
            border-radius: 0.25rem;
            margin-left: auto;
        }
        .permission-actions {
            margin-left: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <h2 class="mb-4 text-primary">权限设置</h2>
        
        <!-- 权限列表 -->
        <div class="permission-list" id="permissionListContainer">
            <!-- Loading Indicator -->
            <div class="text-center p-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <!-- Permission blocks will be rendered here by JS -->
        </div>
    </div>
    
    <!-- 新增权限模态框 -->
    <div class="modal fade" id="addPermissionModal" tabindex="-1" aria-labelledby="addPermissionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPermissionModalLabel">新增权限</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addPermissionForm">
                        <div class="mb-3">
                            <label for="addPermissionName" class="form-label">权限名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="addPermissionName" name="permissionName" required>
                        </div>
                        <div class="mb-3">
                            <label for="addPermissionCode" class="form-label">权限编码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="addPermissionCode" name="permissionCode" required pattern="^[a-zA-Z0-9_:]+$">
                            <div class="form-text">建议格式: `模块:功能:操作` (全小写), 如 `user:manage:create`</div>
                        </div>
                        <div class="mb-3">
                            <label for="addPermissionType" class="form-label">权限类型 <span class="text-danger">*</span></label>
                            <select class="form-select" id="addPermissionType" name="type" required>
                                <option value="">请选择</option>
                                <option value="MENU">菜单</option>
                                <option value="BUTTON">按钮</option>
                                <option value="API">接口</option>
                                <option value="DATA">数据</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="addParentPermission" class="form-label">父级权限</label>
                            <select class="form-select" id="addParentPermission" name="parentId">
                                <option value="">无 (作为顶级权限)</option>
                                <!-- Options populated by JS -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="addPermissionDescription" class="form-label">权限描述</label>
                            <textarea class="form-control" id="addPermissionDescription" name="description" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="addPermissionStatus" class="form-label">状态</label>
                            <select class="form-select" id="addPermissionStatus" name="status">
                                <option value="1" selected>启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="addPermissionForm" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 编辑权限模态框 -->
    <div class="modal fade" id="editPermissionModal" tabindex="-1" aria-labelledby="editPermissionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editPermissionModalLabel">编辑权限</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editPermissionForm">
                        <input type="hidden" id="editPermissionId" name="id">
                        <div class="mb-3">
                            <label for="editPermissionName" class="form-label">权限名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editPermissionName" name="permissionName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editPermissionCode" class="form-label">权限编码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editPermissionCode" name="permissionCode" required pattern="^[a-zA-Z0-9_:]+$">
                            <div class="form-text">建议格式: `模块:功能:操作` (全小写), 如 `user:manage:create`</div>
                        </div>
                        <div class="mb-3">
                            <label for="editPermissionType" class="form-label">权限类型 <span class="text-danger">*</span></label>
                            <select class="form-select" id="editPermissionType" name="type" required>
                                <option value="">请选择</option>
                                <option value="MENU">菜单</option>
                                <option value="BUTTON">按钮</option>
                                <option value="API">接口</option>
                                <option value="DATA">数据</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editParentPermission" class="form-label">父级权限</label>
                            <select class="form-select" id="editParentPermission" name="parentId">
                                <option value="">无 (作为顶级权限)</option>
                                <!-- Options populated by JS -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editPermissionDescription" class="form-label">权限描述</label>
                            <textarea class="form-control" id="editPermissionDescription" name="description" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editPermissionStatus" class="form-label">状态</label>
                            <select class="form-select" id="editPermissionStatus" name="status">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="editPermissionForm" class="btn btn-primary">保存更改</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Toast Container (should be placed in layout.html ideally) -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toastPlacement">
        <!-- Toasts will appear here -->
    </div>

    <!-- JavaScript -->
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script type="module" src="/wkg/js/features/permission/permissions.js"></script>
</body>
</html> 