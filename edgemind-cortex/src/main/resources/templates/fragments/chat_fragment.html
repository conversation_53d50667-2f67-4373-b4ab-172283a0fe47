<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>

<!-- 通用聊天组件片段 -->
<div th:fragment="chat_area(chatId, messagesDivId, userInputId, modelSelectId, uploadButtonId, sendButtonId, pauseButtonId, imageUploadId, imagePreviewId, welcomeMessage)" class="chat-component">
    <!-- 聊天消息区 -->
    <div class="chat-container flex-grow-1 d-flex flex-column" style="min-height:0;">
        <div th:id="${messagesDivId}" class="chat-messages">
            <!-- 欢迎消息 -->
            <div class="message welcome-message">
                <p th:text="${welcomeMessage}">你好！我是AI助手，有什么可以帮你的吗？</p>
            </div>
        </div>
    </div>
    
    <!-- 聊天输入区 -->
    <footer class="chat-input-area">
        <div class="chat-input-content">
            <!-- 整体输入框容器 -->
            <div class="chat-input-box"> 
                <!-- 文本输入区域 -->
                <textarea th:id="${userInputId}" class="form-control chat-textarea" placeholder="输入消息..." rows="1" style="resize: none;"></textarea>
                
                <!-- 图片预览区 -->
                <div th:id="${imagePreviewId}" class="mt-2 mb-2"></div>
                
                <!-- 底部工具栏：模型选择 + 按钮 -->
                <div class="input-toolbar d-flex justify-content-between align-items-center mt-2">
                    <!-- 左侧：模型选择器和深度思考按钮 -->
                    <div class="model-selector-container d-flex align-items-center flex-grow-0 me-3">
                        <div class="model-selector me-2">
                            <label th:for="${modelSelectId}" class="visually-hidden">选择模型</label>
                            <select th:id="${modelSelectId}" class="model-select-component selectpicker" data-style=""> 
                                <!-- 内容由JS填充 -->
                            </select>
                        </div>
                        <!-- 深度思考切换按钮 -->
                        <button class="deep-thinking-toggle active" 
                                th:data-deep-thinking-id="${chatId + '-deep-thinking'}"
                                data-bs-toggle="tooltip" 
                                data-bs-placement="top" 
                                title="深度思考"
                                aria-label="切换深度思考">
                            <i class="bi bi-lightbulb"></i>
                        </button>
                        <!-- 增强检索切换按钮（仅在知识库聊天中显示） -->
                        <button class="enhanced-retrieval-toggle active" 
                                th:id="${chatId + '-enhanced-retrieval'}"
                                data-bs-toggle="tooltip" 
                                data-bs-placement="top" 
                                aria-label="切换增强检索"
                                style="display: none;">
                            <i class="bi bi-search-heart"></i>
                        </button>
                    </div>
                    
                    <!-- 右侧：按钮组 -->
                    <div class="button-group d-flex align-items-center">
                        <button th:id="${uploadButtonId}" class="btn btn-icon" title="上传图片" data-bs-toggle="tooltip" data-bs-placement="top">
<!--                            <i class="bi bi-image"></i>-->
                        </button>
                        <input type="file" th:id="${imageUploadId}" accept="image/*" style="display: none;">
                        <button th:id="${sendButtonId}" class="btn btn-icon btn-primary" aria-label="发送消息" title="发送消息">
                            <i class="bi bi-send"></i>
                        </button>
                        <!-- 暂停按钮，初始隐藏 -->
                        <button th:id="${pauseButtonId}" class="btn btn-icon btn-danger" style="display: none;" aria-label="终止对话" title="终止对话">
                            <i class="bi bi-stop-fill"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- 聊天组件配置，用于保存知识库节点ID等额外参数 -->
    <input type="hidden" th:id="'chat-config-' + ${chatId}" class="chat-component-config" data-component-id="${chatId}">
</div>

</body>
</html>
