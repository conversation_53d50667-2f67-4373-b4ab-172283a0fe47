package com.zibbava.edgemind.cortex.controller.permission;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zibbava.edgemind.cortex.entity.Department;
import com.zibbava.edgemind.cortex.service.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/permission/departments") // 统一前缀
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;

    /**
     * 获取部门树
     */
    @GetMapping("/tree")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<List<Department>> getDepartmentTree() {
        List<Department> tree = departmentService.getDepartmentTree();
        return ResponseEntity.ok(tree);
    }
    
     /**
     * 获取部门列表 (扁平结构，可能用于选择上级部门)
     */
    @GetMapping
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<List<Department>> getAllDepartments() {
        List<Department> list = departmentService.list(); 
        return ResponseEntity.ok(list);
    }

    /**
     * 新增部门
     */
    @PostMapping
    @SaCheckPermission("dept:manage:create")
    public ResponseEntity<Void> addDepartment(@RequestBody Department department) {
        // TODO: 校验 deptCode 是否唯一, parentId 是否有效
        departmentService.save(department);
        return ResponseEntity.ok().build();
    }

    /**
     * 修改部门
     */
    @PutMapping("/{id}")
    @SaCheckPermission("dept:manage:update")
    public ResponseEntity<Void> updateDepartment(@PathVariable Long id, @RequestBody Department department) {
        department.setId(id);
        // TODO: 校验 deptCode 是否唯一 (如果允许修改), parentId 不能是自己或自己的子部门
        departmentService.updateById(department);
        return ResponseEntity.ok().build();
    }

    /**
     * 删除部门
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission("dept:manage:delete")
    public ResponseEntity<Void> deleteDepartment(@PathVariable Long id) {
        // TODO: 检查是否有子部门，是否有用户关联，是否允许删除
        departmentService.removeById(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取单个部门详情 (包含负责人信息等)
     */
    @GetMapping("/{id}")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<Department> getDepartmentById(@PathVariable Long id) {
        Department department = departmentService.getById(id);
        if (department != null) {
            // TODO: 加载负责人 User 信息和上级部门名称
            // User manager = userService.getById(department.getManagerId());
            // department.setManager(manager);
            // if (department.getParentId() != null && department.getParentId() != 0) {
            //     Department parent = departmentService.getById(department.getParentId());
            //     department.setParentName(parent != null ? parent.getDeptName() : null);
            // }
            return ResponseEntity.ok(department);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
} 