<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zibbava</groupId>
		<artifactId>edgemind</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>edgemind-cortex</artifactId>
	<name>edgemind-cortex</name>
	<description>edgemind-cortex</description>
	<url/>
	<licenses>
		<license/>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>
	<properties>
		<java.version>17</java.version>
		<langchain4j.version>1.0.0-beta5</langchain4j.version>
		<langchain4j.core.version>1.0.0</langchain4j.core.version>
	</properties>
	<dependencies>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>

		<!-- Apache HTTP Client - 用于流式请求处理 -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.14</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- Spring Boot DevTools 用于实现热重载功能 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.29</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j</artifactId>
			<version>${langchain4j.core.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-core</artifactId>
			<version>${langchain4j.core.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-open-ai</artifactId>
			<version>${langchain4j.core.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-ollama</artifactId>
			<version>${langchain4j.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-embeddings</artifactId>
			<version>${langchain4j.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-nomic</artifactId>
			<version>${langchain4j.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-milvus</artifactId>
			<version>${langchain4j.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-embedding-store-filter-parser-sql</artifactId>
			<version>${langchain4j.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-document-parser-apache-tika</artifactId>
			<version>${langchain4j.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-easy-rag</artifactId>
			<version>${langchain4j.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-reactor</artifactId>
			<version>${langchain4j.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/dev.langchain4j/langchain4j-mcp -->
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-mcp</artifactId>
			<version>${langchain4j.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-chroma-ollama</artifactId>
			<version>${langchain4j.core.version}</version>
		</dependency>
		<!-- Apache POI for Word document processing -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>5.4.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.4.1</version>
		</dependency>

		<!-- Jieba 中文分词器 - 用于自定义BM25实现 -->
		<dependency>
			<groupId>com.huaban</groupId>
			<artifactId>jieba-analysis</artifactId>
			<version>1.0.2</version>
		</dependency>

		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
			<version>4.0.2</version>
		</dependency>

		<!-- Sa-Token 权限认证 (Spring Boot 3) -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-spring-boot3-starter</artifactId>
			<version>1.42.0</version>
		</dependency>

		<!-- Sa-Token 整合 Redis (使用 jackson 序列化方式) -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-redis-jackson</artifactId>
			<version>1.42.0</version>
		</dependency>

		<!-- Spring Data Redis -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<!-- Lombok for convenience -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- MyBatis Plus -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-spring-boot3-starter</artifactId>
			<version>3.5.11</version> <!-- 使用较新的稳定版本 -->
		</dependency>

		<!-- MySQL Connector for MySQL 8 -->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<scope>runtime</scope> <!-- 必须是 runtime scope -->
		</dependency>

		<!-- 验证框架 - 使用Jakarta Validation API (Spring Boot 3兼容) -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.onlyoffice</groupId>
			<artifactId>docs-integration-sdk</artifactId>
			<version>1.4.0</version>
		</dependency>
		<dependency>
			<groupId>io.github.mymonstercat</groupId>
			<artifactId>rapidocr</artifactId>
			<version>0.0.7</version>
		</dependency>
		<dependency>
			<groupId>io.github.mymonstercat</groupId>
			<artifactId>rapidocr-onnx-platform</artifactId>
			<version>0.0.7</version>
		</dependency>
		<dependency>
			<groupId>io.github.mymonstercat</groupId>
			<artifactId>rapidocr-onnx-linux-x86_64</artifactId>
			<version>1.2.2</version>
		</dependency>

		<!-- OSHI 硬件信息获取库 -->
		<dependency>
			<groupId>com.github.oshi</groupId>
			<artifactId>oshi-core</artifactId>
			<version>6.8.1</version>
		</dependency>

		<!-- 加密解密库 -->
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.16.1</version>
		</dependency>

		<!-- Testcontainers for integration testing -->
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>junit-jupiter</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>milvus</artifactId>
			<version>1.20.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>testcontainers</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- Weaviate Java SDK -->
		<dependency>
			<groupId>io.weaviate</groupId>
			<artifactId>client</artifactId>
			<version>5.2.1</version>
		</dependency>

		<!-- Jasypt for configuration encryption -->
		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>3.0.5</version>
		</dependency>
	</dependencies>

	<build>
		<finalName>${project.name}-${project.version}</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.3.1</version> <!-- 或者使用最新版本 -->
				<executions>
					<execution>
						<id>copy-jre-for-launch4j</id>
						<phase>prepare-package</phase> <!-- 确保在 launch4j 运行前复制 JRE -->
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/runtime-jre</outputDirectory>
							<resources>
								<resource>
									<!-- 这是你项目根目录下的 JRE 文件夹 -->
									<directory>${project.basedir}/runtime-jre</directory>
									<filtering>false</filtering> <!-- 不要对二进制 JRE 文件进行过滤 -->
								</resource>
							</resources>
						</configuration>
					</execution>
					<execution>
						<id>copy-integrity-properties</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<!-- 将licence模块生成的校验文件复制到cortex模块的classes目录中，以便打包进BOOT-INF/classes -->
							<outputDirectory>${project.build.outputDirectory}</outputDirectory>
							<resources>
								<resource>
									<directory>${project.basedir}/../edgemind-licence/target/classes</directory>
									<includes>
										<include>integrity.properties</include>
									</includes>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludeDevtools>false</excludeDevtools>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.akathist.maven.plugins.launch4j</groupId>
				<artifactId>launch4j-maven-plugin</artifactId>
				<version>2.6.0</version>
				<executions>
					<execution>
						<id>l4j-clui</id>
						<phase>package</phase>
						<goals>
							<goal>launch4j</goal>
						</goals>
						<configuration>
							<headerType>gui</headerType> <!-- 对于 Spring Boot 应用，控制台通常是必要的 -->
							<jar>${project.build.directory}/${project.build.finalName}.jar</jar>
							<outfile>${project.build.directory}/端智AI助手.exe</outfile>
							<errTitle>${project.name} Error</errTitle> <!-- 发生错误时错误对话框的标题 -->
							<!-- 指定应用程序图标 -->
							<icon>src/main/resources/static/images/ai-assistant-logo.ico</icon>
							<jre>
								<!--
                                 重要: 你需要将 JDK 17 的 JRE 放在项目根目录下的 "runtime-jre" 文件夹中。
                                 例如: 项目根目录/runtime-jre/bin/java.exe
                                 如果 runtime-jre 目录不存在或路径不正确，打包会失败或 exe 无法运行。
                                -->
								<path>./runtime-jre</path> <!-- 指向包含 JRE 的文件夹, 例如项目根目录下的 'runtime-jre' -->
								<minVersion>17</minVersion> <!-- 指定最低 JRE 版本 -->
								<!-- 你可以根据你的应用需求调整堆大小 -->
								<initialHeapSize>256</initialHeapSize> <!-- 初始堆大小 (MB) -->
								<maxHeapSize>1024</maxHeapSize>   <!-- 最大堆大小 (MB) -->
								<!-- 如果你的 JRE 包含 JVM 参数文件 (jvm.cfg), launch4j 默认会使用它 -->
								<!-- 你也可以在这里直接添加 JVM 选项 -->
								<!-- <opts>
                                    <opt>-Dsome.property=value</opt>
                                </opts> -->
							</jre>
							<versionInfo>
								<fileVersion>*******</fileVersion> <!-- EXE 文件版本 -->
								<txtFileVersion>${project.version}</txtFileVersion> <!-- EXE 文件版本文本 -->
								<fileDescription>${project.description}</fileDescription> <!-- 文件描述 -->
								<copyright>深圳市青蜂信息咨询有限公司</copyright> <!-- 请替换为你的公司名 -->
								<productVersion>*******</productVersion> <!-- 产品版本 -->
								<txtProductVersion>${project.version}</txtProductVersion> <!-- 产品版本文本 -->
								<productName>${project.name}</productName> <!-- 产品名称 -->
								<internalName>${project.artifactId}</internalName>
								<originalFilename>端智AI助手.exe</originalFilename>
							</versionInfo>
							<!-- 可选: 防止应用程序多实例运行 -->
							<singleInstance>
								<mutexName>${project.groupId}.${project.artifactId}</mutexName>
								<windowTitle>${project.name}</windowTitle>
							</singleInstance>
							<classPath>
								<!-- Spring Boot fat jars 使用其内置的启动器 -->
								<mainClass>org.springframework.boot.loader.launch.JarLauncher</mainClass>
								<addDependencies>false</addDependencies>
								<preCp/>
							</classPath>
							<!-- 确保工作目录是 exe 所在的目录 -->
							<chdir>.</chdir>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>3.2.0</version>
				<executions>
					<execution>
						<id>generate-integrity-hashes</id>
						<phase>process-classes</phase>
						<goals>
							<goal>java</goal>
						</goals>
						<configuration>
							<mainClass>com.zibbava.edgemind.licence.integrity.HashGenerator</mainClass>
							<arguments>
								<!-- 参数1: cortex模块的classes目录 -->
								<argument>${project.build.outputDirectory}</argument>
								<!-- 参数2: licence模块的classes目录，用于存放生成的properties文件 -->
								<argument>${project.basedir}/../edgemind-licence/target/classes</argument>
							</arguments>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
