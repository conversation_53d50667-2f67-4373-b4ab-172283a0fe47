package com.zibbava.edgemind.cortex.controller.permission;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.cortex.annotation.OperationLog;
import com.zibbava.edgemind.cortex.entity.User;
import com.zibbava.edgemind.cortex.service.DepartmentService;
import com.zibbava.edgemind.cortex.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/permission/users") // 统一前缀
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private DepartmentService departmentService;

    /**
     * 分页查询用户列表
     */
    @GetMapping
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<IPage<User>> getUserPage(@RequestParam(defaultValue = "1") int current,
                                                   @RequestParam(defaultValue = "10") int size,
                                                   // 将查询参数封装到 Map 中
                                                   @RequestParam(required = false) Map<String, Object> queryParams) {
        Page<User> page = new Page<>(current, size);
        IPage<User> userPage = userService.getUserPage(page, queryParams);
        // TODO: 返回的 User 对象需要包含角色和部门信息
        return ResponseEntity.ok(userPage);
    }

    /**
     * 新增用户
     * (注意: 注册通常在 AuthController 中，这里是管理员创建用户)
     */
    @PostMapping
    @SaCheckPermission("user:manage:create")
    @OperationLog(module = "用户管理", operationType = "新增", description = "新增用户: {args}")
    public ResponseEntity<Void> addUser(@RequestBody User user) {
        // DTO 转换或直接使用 User 对象 (需要包含 password, roleIds 等)
        // TODO: 校验用户名是否重复、密码复杂度、角色/部门是否存在等
        userService.register(
                user.getUsername(),
                user.getPassword(), // 前端应传递明文密码
                user.getNickname(),
                user.getEmail(),
                user.getPhone(),
                user.getDeptId(),
                user.getRoleIds(),
                user.getRemark()
        );
        return ResponseEntity.ok().build();
    }

    /**
     * 修改用户基本信息
     */
    @PutMapping("/{id}")
    @SaCheckPermission("user:manage:update")
    @OperationLog(module = "用户管理", operationType = "修改", description = "修改用户ID: " + "{id}")
    public ResponseEntity<Void> updateUserProfile(@PathVariable Long id, @RequestBody User user) {
        user.setId(id);
        // TODO: 校验逻辑
        userService.updateUserProfile(user); // Service 层只更新允许的字段
        return ResponseEntity.ok().build();
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission("user:manage:delete")
    @OperationLog(module = "用户管理", operationType = "删除", description = "删除用户ID: " + "{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        // TODO: 检查是否是删除自己或超级管理员
        userService.deleteUser(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取用户拥有的角色ID列表
     */
    @GetMapping("/{userId}/roles")
    @SaCheckPermission("user:manage:assign_role")
    public ResponseEntity<List<Long>> getUserRoleIds(@PathVariable Long userId) {
        List<Long> roleIds = userService.getRoleIdsByUserId(userId);
        return ResponseEntity.ok(roleIds);
    }

    /**
     * 给用户分配角色
     */
    @PutMapping("/{userId}/roles")
    @SaCheckPermission("user:manage:assign_role")
    public ResponseEntity<Void> assignRolesToUser(@PathVariable Long userId, @RequestBody List<Long> roleIds) {
        userService.assignRolesToUser(userId, roleIds);
        return ResponseEntity.ok().build();
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/{userId}/status")
    @SaCheckPermission("user:manage:update") // 或更具体的 'user:manage:update_status'
    public ResponseEntity<Void> updateUserStatus(@PathVariable Long userId, @RequestBody Map<String, Integer> payload) {
        Integer status = payload.get("status");
        // TODO: 校验 status 值是否合法 (0 或 1)
         if (status == null || (status != 0 && status != 1)) {
             return ResponseEntity.badRequest().build();
         }
        userService.updateUserStatus(userId, status);
        return ResponseEntity.ok().build();
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/{userId}/reset-password")
    @SaCheckPermission("user:manage:reset_password")
    public ResponseEntity<Void> resetPassword(@PathVariable Long userId, @RequestBody Map<String, String> payload) {
        String newPassword = payload.get("newPassword");
        // TODO: 校验密码复杂度
         if (newPassword == null || newPassword.isBlank()) {
            return ResponseEntity.badRequest().build();
         }
        userService.resetPassword(userId, newPassword);
        return ResponseEntity.ok().build();
    }

     /**
     * 获取单个用户详情 (如果需要)
     */
    @GetMapping("/{id}")
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        User user = userService.getById(id);
        if (user != null) {
            // 移除密码等敏感信息
            user.setPassword(null); 
            // TODO: 加载用户的角色和部门信息
            user.setRoleIds(userService.getRoleIdsByUserId(id));
            if(user.getDeptId() != null) {
                 user.setDepartment(departmentService.getById(user.getDeptId()));
            }
            return ResponseEntity.ok(user);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
} 