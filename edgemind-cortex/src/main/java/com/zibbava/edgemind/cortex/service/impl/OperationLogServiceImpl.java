package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zibbava.edgemind.cortex.entity.OperationLog;
import com.zibbava.edgemind.cortex.mapper.OperationLogMapper;
import com.zibbava.edgemind.cortex.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements OperationLogService {

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Override
    @Transactional
    public void recordLog(Long userId, String username, String module, String operationType,
                         String description, String method, String requestUrl, String requestParams,
                         String responseResult, Integer status, String errorMessage,
                         String ipAddress, String userAgent, Long executionTime) {
        try {
            OperationLog log = new OperationLog();
            log.setUserId(userId);
            log.setUsername(username);
            log.setModule(module);
            log.setOperationType(operationType);
            log.setDescription(description);
            log.setMethod(method);
            log.setRequestUrl(requestUrl);
            log.setRequestParams(requestParams);
            log.setResponseResult(responseResult);
            log.setStatus(status);
            log.setErrorMessage(errorMessage);
            log.setIpAddress(ipAddress);
            log.setUserAgent(userAgent);
            log.setExecutionTime(executionTime);
            log.setOperationTime(LocalDateTime.now());
            
            this.save(log);
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }

    @Override
    public void recordLog(Long userId, String username, String module, String operationType,
                         String description, Integer status) {
        recordLog(userId, username, module, operationType, description, null, null, null,
                 null, status, null, null, null, null);
    }

    @Override
    public IPage<OperationLog> getLogPage(Page<OperationLog> page, Map<String, Object> queryParams) {
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        
        if (queryParams != null) {
            // 用户名查询
            if (queryParams.containsKey("username") && StringUtils.hasText((String) queryParams.get("username"))) {
                queryWrapper.like(OperationLog::getUsername, queryParams.get("username"));
            }
            
            // 模块查询
            if (queryParams.containsKey("module") && StringUtils.hasText((String) queryParams.get("module"))) {
                queryWrapper.eq(OperationLog::getModule, queryParams.get("module"));
            }
            
            // 操作类型查询
            if (queryParams.containsKey("operationType") && StringUtils.hasText((String) queryParams.get("operationType"))) {
                queryWrapper.eq(OperationLog::getOperationType, queryParams.get("operationType"));
            }
            
            // 状态查询
            if (queryParams.containsKey("status") && queryParams.get("status") != null) {
                queryWrapper.eq(OperationLog::getStatus, queryParams.get("status"));
            }
            
            // 时间范围查询
            if (queryParams.containsKey("startTime") && queryParams.get("startTime") != null) {
                queryWrapper.ge(OperationLog::getOperationTime, queryParams.get("startTime"));
            }
            if (queryParams.containsKey("endTime") && queryParams.get("endTime") != null) {
                queryWrapper.le(OperationLog::getOperationTime, queryParams.get("endTime"));
            }
            
            // IP地址查询
            if (queryParams.containsKey("ipAddress") && StringUtils.hasText((String) queryParams.get("ipAddress"))) {
                queryWrapper.like(OperationLog::getIpAddress, queryParams.get("ipAddress"));
            }
        }
        
        queryWrapper.orderByDesc(OperationLog::getOperationTime);
        return operationLogMapper.selectLogPageWithUser(page, queryWrapper);
    }

    @Override
    public Map<String, Object> getStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return operationLogMapper.getStatistics(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getOperationFrequency(int hours) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        return operationLogMapper.getOperationFrequency(startTime);
    }

    @Override
    public List<Map<String, Object>> getUserOperationRanking(int days, int limit) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        return operationLogMapper.getUserOperationRanking(startTime, limit);
    }

    @Override
    public List<Map<String, Object>> getModuleStatistics(int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        return operationLogMapper.getModuleStatistics(startTime);
    }

    @Override
    @Transactional
    public int cleanupExpiredLogs(int days) {
        LocalDateTime beforeTime = LocalDateTime.now().minusDays(days);
        return operationLogMapper.cleanupLogs(beforeTime);
    }

    @Override
    public List<OperationLog> getUserRecentOperations(Long userId, int limit) {
        return operationLogMapper.getUserRecentOperations(userId, limit);
    }

    @Override
    public List<OperationLog> getFailedOperations(int days, int limit) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        return operationLogMapper.getFailedOperations(startTime, limit);
    }

    @Override
    public List<OperationLog> exportLogs(Map<String, Object> queryParams) {
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        
        if (queryParams != null) {
            // 应用查询条件（与分页查询相同的逻辑）
            if (queryParams.containsKey("username") && StringUtils.hasText((String) queryParams.get("username"))) {
                queryWrapper.like(OperationLog::getUsername, queryParams.get("username"));
            }
            if (queryParams.containsKey("module") && StringUtils.hasText((String) queryParams.get("module"))) {
                queryWrapper.eq(OperationLog::getModule, queryParams.get("module"));
            }
            if (queryParams.containsKey("operationType") && StringUtils.hasText((String) queryParams.get("operationType"))) {
                queryWrapper.eq(OperationLog::getOperationType, queryParams.get("operationType"));
            }
            if (queryParams.containsKey("status") && queryParams.get("status") != null) {
                queryWrapper.eq(OperationLog::getStatus, queryParams.get("status"));
            }
            if (queryParams.containsKey("startTime") && queryParams.get("startTime") != null) {
                queryWrapper.ge(OperationLog::getOperationTime, queryParams.get("startTime"));
            }
            if (queryParams.containsKey("endTime") && queryParams.get("endTime") != null) {
                queryWrapper.le(OperationLog::getOperationTime, queryParams.get("endTime"));
            }
        }
        
        queryWrapper.orderByDesc(OperationLog::getOperationTime);
        return this.list(queryWrapper);
    }

    @Override
    @Transactional
    public void batchDeleteLogs(List<Long> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            this.removeByIds(ids);
        }
    }

    @Override
    public Map<String, Object> getTodayStatistics() {
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1);
        return getStatistics(startOfDay, endOfDay);
    }

    @Override
    public List<Map<String, Object>> getOperationTrend(int days) {
        List<Map<String, Object>> trend = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = days - 1; i >= 0; i--) {
            LocalDateTime startOfDay = now.minusDays(i).withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endOfDay = startOfDay.plusDays(1);
            
            Map<String, Object> dayStats = getStatistics(startOfDay, endOfDay);
            dayStats.put("date", startOfDay.toLocalDate().toString());
            trend.add(dayStats);
        }
        
        return trend;
    }
}
