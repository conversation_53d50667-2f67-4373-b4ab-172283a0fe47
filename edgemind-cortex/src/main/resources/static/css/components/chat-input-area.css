/* src/main/resources/static/css/chat-input-area.css */

/* 聊天输入区域独立样式 */
.chat-input-area {
    padding: 1rem 0; /* 修改左右内边距为0，留给.chat-input-content控制 */
    border-top: none; /* 移除上方的直线 */
    background-color: #ffffff; /* 改为白色背景 */
}

.chat-input-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    /* 下面的样式已在chat.html中定义，这里保留原有的布局属性 */
    /* max-width: 900px; */
    /* margin: 0 auto; */
    /* padding: 0 32px; */
}

.model-selector-external {
    margin-bottom: 0.5rem;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 0.5rem;
}

.input-wrapper textarea {
    flex: 1;
    resize: none;
    border-radius: 6px;
    max-height: 150px;
    overflow-y: auto;
}

.input-wrapper button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    min-width: 40px;
    height: 40px;
}

.input-wrapper button i {
    font-size: 1.2rem;
    display: inline-block;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 1;
}

#image-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.image-preview {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #dee2e6;
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-image {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 576px) {
    .chat-input-area {
        padding: 0.75rem 0; /* 保持左右内边距为0 */
    }

    .chat-input-content {
        padding: 0 16px; /* 在小屏幕上减小左右内边距 */
    }

    .input-wrapper textarea {
        font-size: 0.9rem;
    }
}