package com.zibbava.edgemind.cortex.store;

import com.zibbava.edgemind.cortex.demo.util.StopWordsLoader;
import dev.langchain4j.data.document.Metadata;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.HashMap;
import java.util.Map;

/**
 * WeaviateEmbeddingStore 使用示例
 * 
 * 这个示例展示了如何在实际项目中使用WeaviateEmbeddingStore：
 * 1. 添加文档到向量数据库
 * 2. 执行语义搜索
 * 3. 执行混合搜索（向量+BM25）
 * 4. 批量操作
 * 5. 停用词优化
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
        "weaviate.enabled=true",
        "weaviate.host=localhost",
        "weaviate.port=8090",
        "weaviate.className=TestDocument",
        "weaviate.vectorDimension=1024",
        "weaviate.batchSize=50",
        "weaviate.connectionTimeout=30000",
        "weaviate.retryAttempts=3",
        "weaviate.hybridSearch.enabled=true",
        "weaviate.hybridSearch.alpha=0.7",
        "weaviate.search.maxResults=5",
        "weaviate.search.minScore=0.7"
})
public class WeaviateEmbeddingStoreUsageExample {

    @Autowired(required = false)
    private WeaviateEmbeddingStore weaviateStore;

    @Autowired(required = false)
    private EmbeddingModel embeddingModel;

    @Test
    void demonstrateBasicUsage() {
        if (weaviateStore == null || embeddingModel == null) {
            log.warn("⚠️ Weaviate或EmbeddingModel未配置，跳过使用示例");
            return;
        }

        log.info("=== WeaviateEmbeddingStore 使用示例 ===");

        // 1. 准备测试数据
        String[] documents = {
                "Java是一种面向对象的编程语言，具有跨平台特性",
                "Python是一种简洁易学的编程语言，广泛用于数据科学",
                "JavaScript是Web开发的核心语言，支持前端和后端开发",
                "机器学习是人工智能的重要分支，用于数据分析和预测",
                "深度学习使用神经网络模型，在图像识别领域表现出色"
        };

        // 2. 添加文档到向量数据库
        log.info("📝 添加文档到向量数据库...");
        for (int i = 0; i < documents.length; i++) {
            String text = documents[i];
            
            // 创建元数据
            Map<String, Object> metadataMap = new HashMap<>();
            metadataMap.put("node_id", "doc_" + (i + 1));
            metadataMap.put("space_id", "demo_space");
            metadataMap.put("file_name", "demo_doc_" + (i + 1) + ".txt");
            metadataMap.put("user_id", "demo_user");
            metadataMap.put("mime_type", "text/plain");
            metadataMap.put("title", "示例文档 " + (i + 1));
            metadataMap.put("content", text);
            
            Metadata metadata = Metadata.from(metadataMap);
            TextSegment segment = TextSegment.from(text, metadata);
            Embedding embedding = embeddingModel.embed(text).content();
            
            String docId = weaviateStore.add(embedding, segment);
            log.info("✅ 文档 {} 添加成功，ID: {}", i + 1, docId);
        }

        // 等待索引完成
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 3. 执行语义搜索
        log.info("\n🔍 执行语义搜索...");
        String queryText = "编程语言开发";
        Embedding queryEmbedding = embeddingModel.embed(queryText).content();
        
        EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                .queryEmbedding(queryEmbedding)
                .maxResults(3)
                .minScore(0.0)
                .build();
        
        EmbeddingSearchResult<TextSegment> searchResult = weaviateStore.search(searchRequest);
        log.info("📊 语义搜索结果 (查询: '{}'):", queryText);
        for (EmbeddingMatch<TextSegment> match : searchResult.matches()) {
            log.info("- 评分: {:.4f}, 文本: {}", 
                    match.score(), 
                    match.embedded().text().substring(0, Math.min(30, match.embedded().text().length())) + "...");
        }

        // 4. 执行混合搜索
        log.info("\n🔄 执行混合搜索 (向量 + BM25)...");
        EmbeddingSearchResult<TextSegment> hybridResult = weaviateStore.hybridSearch(
                "机器学习人工智能", queryEmbedding, 3, 0.0, null);
        
        log.info("📊 混合搜索结果 (查询: '机器学习人工智能'):");
        for (EmbeddingMatch<TextSegment> match : hybridResult.matches()) {
            log.info("- 评分: {:.4f}, 文本: {}", 
                    match.score(), 
                    match.embedded().text().substring(0, Math.min(30, match.embedded().text().length())) + "...");
        }

        // 5. 展示停用词配置
        log.info("\n📚 停用词配置信息:");
        StopWordsLoader.StopWordsInfo info = StopWordsLoader.getStopWordsInfo();
        log.info("- 总数量: {} 个", info.getTotalCount());
        log.info("- 来源文件: chinese_stopwords.txt");
        log.info("- 优化效果: 提高中文搜索质量，过滤无意义词汇");

        // 6. 展示配置信息
        log.info("\n⚙️ Weaviate配置信息:");
        log.info("- 类名称: TestDocument");
        log.info("- 向量维度: 1024");
        log.info("- 批处理大小: 50");
        log.info("- 混合搜索: 启用 (alpha=0.7)");
        log.info("- 停用词: 从文件加载中文停用词");

        log.info("\n✅ WeaviateEmbeddingStore 使用示例完成！");
    }

    @Test
    void demonstrateBatchOperations() {
        if (weaviateStore == null || embeddingModel == null) {
            log.warn("⚠️ Weaviate或EmbeddingModel未配置，跳过批量操作示例");
            return;
        }

        log.info("=== 批量操作示例 ===");

        // 准备批量数据
        String[] batchTexts = {
                "Spring Boot是Java开发框架，简化了企业级应用开发",
                "React是前端JavaScript库，用于构建用户界面",
                "Docker是容器化技术，简化了应用部署和管理",
                "Kubernetes是容器编排平台，管理大规模容器应用",
                "Redis是内存数据库，提供高性能的数据存储和缓存"
        };

        // 批量添加
        log.info("📦 执行批量添加操作...");
        for (String text : batchTexts) {
            Embedding embedding = embeddingModel.embed(text).content();
            TextSegment segment = TextSegment.from(text);
            weaviateStore.add(embedding, segment);
        }
        log.info("✅ 批量添加完成，共添加 {} 个文档", batchTexts.length);

        // 等待索引
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 批量搜索
        log.info("\n🔍 执行批量搜索...");
        String[] queries = {"开发框架", "前端技术", "容器技术"};
        
        for (String query : queries) {
            Embedding queryEmbedding = embeddingModel.embed(query).content();
            EmbeddingSearchRequest request = EmbeddingSearchRequest.builder()
                    .queryEmbedding(queryEmbedding)
                    .maxResults(2)
                    .minScore(0.0)
                    .build();
            
            EmbeddingSearchResult<TextSegment> result = weaviateStore.search(request);
            log.info("📊 查询 '{}' 的结果:", query);
            for (EmbeddingMatch<TextSegment> match : result.matches()) {
                log.info("  - 评分: {:.4f}, 文本: {}", 
                        match.score(), 
                        match.embedded().text().substring(0, Math.min(25, match.embedded().text().length())) + "...");
            }
        }

        log.info("\n✅ 批量操作示例完成！");
    }
} 