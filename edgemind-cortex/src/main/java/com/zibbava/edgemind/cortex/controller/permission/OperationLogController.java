package com.zibbava.edgemind.cortex.controller.permission;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.cortex.entity.OperationLog;
import com.zibbava.edgemind.cortex.service.OperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/permission/operation-logs")
public class OperationLogController {

    @Autowired
    private OperationLogService operationLogService;

    /**
     * 分页查询操作日志
     */
    @GetMapping
    @SaCheckPermission("log:operation:list")
    public ResponseEntity<IPage<OperationLog>> getLogPage(@RequestParam(defaultValue = "1") int current,
                                                          @RequestParam(defaultValue = "10") int size,
                                                          @RequestParam(required = false) Map<String, Object> queryParams) {
        Page<OperationLog> page = new Page<>(current, size);
        IPage<OperationLog> logPage = operationLogService.getLogPage(page, queryParams);
        return ResponseEntity.ok(logPage);
    }

    /**
     * 获取操作日志统计信息
     */
    @GetMapping("/statistics")
    @SaCheckPermission("log:operation:statistics")
    public ResponseEntity<Map<String, Object>> getStatistics(@RequestParam(required = false) String startTime,
                                                             @RequestParam(required = false) String endTime) {
        LocalDateTime start = startTime != null ? LocalDateTime.parse(startTime) : LocalDateTime.now().minusDays(7);
        LocalDateTime end = endTime != null ? LocalDateTime.parse(endTime) : LocalDateTime.now();
        
        Map<String, Object> statistics = operationLogService.getStatistics(start, end);
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取今日操作统计
     */
    @GetMapping("/today-statistics")
    @SaCheckPermission("log:operation:statistics")
    public ResponseEntity<Map<String, Object>> getTodayStatistics() {
        Map<String, Object> statistics = operationLogService.getTodayStatistics();
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取操作频率统计
     */
    @GetMapping("/frequency")
    @SaCheckPermission("log:operation:statistics")
    public ResponseEntity<List<Map<String, Object>>> getOperationFrequency(@RequestParam(defaultValue = "24") int hours) {
        List<Map<String, Object>> frequency = operationLogService.getOperationFrequency(hours);
        return ResponseEntity.ok(frequency);
    }

    /**
     * 获取用户操作排行
     */
    @GetMapping("/user-ranking")
    @SaCheckPermission("log:operation:statistics")
    public ResponseEntity<List<Map<String, Object>>> getUserOperationRanking(@RequestParam(defaultValue = "7") int days,
                                                                             @RequestParam(defaultValue = "10") int limit) {
        List<Map<String, Object>> ranking = operationLogService.getUserOperationRanking(days, limit);
        return ResponseEntity.ok(ranking);
    }

    /**
     * 获取模块操作统计
     */
    @GetMapping("/module-statistics")
    @SaCheckPermission("log:operation:statistics")
    public ResponseEntity<List<Map<String, Object>>> getModuleStatistics(@RequestParam(defaultValue = "7") int days) {
        List<Map<String, Object>> statistics = operationLogService.getModuleStatistics(days);
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取操作趋势数据
     */
    @GetMapping("/trend")
    @SaCheckPermission("log:operation:statistics")
    public ResponseEntity<List<Map<String, Object>>> getOperationTrend(@RequestParam(defaultValue = "7") int days) {
        List<Map<String, Object>> trend = operationLogService.getOperationTrend(days);
        return ResponseEntity.ok(trend);
    }

    /**
     * 获取用户最近操作记录
     */
    @GetMapping("/user/{userId}/recent")
    @SaCheckPermission("log:operation:list")
    public ResponseEntity<List<OperationLog>> getUserRecentOperations(@PathVariable Long userId,
                                                                      @RequestParam(defaultValue = "10") int limit) {
        List<OperationLog> operations = operationLogService.getUserRecentOperations(userId, limit);
        return ResponseEntity.ok(operations);
    }

    /**
     * 获取失败操作记录
     */
    @GetMapping("/failed")
    @SaCheckPermission("log:operation:list")
    public ResponseEntity<List<OperationLog>> getFailedOperations(@RequestParam(defaultValue = "7") int days,
                                                                  @RequestParam(defaultValue = "50") int limit) {
        List<OperationLog> operations = operationLogService.getFailedOperations(days, limit);
        return ResponseEntity.ok(operations);
    }

    /**
     * 导出操作日志
     */
    @GetMapping("/export")
    @SaCheckPermission("log:operation:export")
    public ResponseEntity<List<OperationLog>> exportLogs(@RequestParam(required = false) Map<String, Object> queryParams) {
        List<OperationLog> logs = operationLogService.exportLogs(queryParams);
        return ResponseEntity.ok(logs);
    }

    /**
     * 批量删除操作日志
     */
    @DeleteMapping("/batch")
    @SaCheckPermission("log:operation:delete")
    public ResponseEntity<Void> batchDeleteLogs(@RequestBody List<Long> ids) {
        operationLogService.batchDeleteLogs(ids);
        return ResponseEntity.ok().build();
    }

    /**
     * 清理过期日志
     */
    @DeleteMapping("/cleanup")
    @SaCheckPermission("log:operation:delete")
    public ResponseEntity<Map<String, Object>> cleanupExpiredLogs(@RequestParam(defaultValue = "90") int days) {
        int deletedCount = operationLogService.cleanupExpiredLogs(days);
        Map<String, Object> result = Map.of("deletedCount", deletedCount);
        return ResponseEntity.ok(result);
    }

    /**
     * 删除单个操作日志
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission("log:operation:delete")
    public ResponseEntity<Void> deleteLog(@PathVariable Long id) {
        operationLogService.removeById(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取操作日志详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission("log:operation:list")
    public ResponseEntity<OperationLog> getLogById(@PathVariable Long id) {
        OperationLog log = operationLogService.getById(id);
        if (log != null) {
            return ResponseEntity.ok(log);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
}
