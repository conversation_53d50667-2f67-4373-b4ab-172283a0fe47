package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.dto.LicenseCheckResult;
import com.zibbava.edgemind.cortex.dto.LicenseStatusDTO;

/**
 * 许可证服务接口
 */
public interface LicenseService {

    /**
     * 获取当前硬件指纹
     * @return 硬件指纹字符串
     */
    String getHardwareFingerprint();

    /**
     * 获取详细的硬件信息（用于调试）
     * @return 硬件信息字符串
     */
    String getDetailedHardwareInfo();

    /**
     * 验证许可证
     * @param licenseKey 许可证密钥
     * @return 验证结果，true表示验证通过
     */
    boolean verifyLicense(String licenseKey);

    /**
     * 激活许可证
     * @param licenseKey 许可证密钥
     * @return 激活结果，true表示激活成功
     */
    boolean activateLicense(String licenseKey);

    /**
     * 获取许可证状态
     * @return 许可证状态信息
     */
    LicenseStatusDTO getLicenseStatus();

    /**
     * 检查系统是否已授权
     * @return 授权状态，true表示已授权
     */
    boolean isLicensed();

    /**
     * 检查系统授权状态并返回详细信息
     * @return 包含授权状态和详细信息的对象
     */
    LicenseCheckResult checkLicenseStatus();

    /**
     * 清除授权状态缓存
     */
    void clearCache();

}
