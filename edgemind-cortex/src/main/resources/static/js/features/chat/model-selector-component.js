/**
 * Model Selector Component
 * Populates a select element with available models fetched from the API.
 * Supports localStorage to remember user's model selection across page refreshes.
 * @param {string} selectElementId - The ID of the <select> element.
 */

// 存储键名常量
const STORAGE_KEY = 'selected-model';

/**
 * 从localStorage获取上次选择的模型
 * @returns {string|null} 上次选择的模型值，如果没有则返回null
 */
function getStoredModelSelection() {
    try {
        return localStorage.getItem(STORAGE_KEY);
    } catch (error) {
        console.warn('[ModelSelector] 无法访问localStorage:', error);
        return null;
    }
}

/**
 * 保存模型选择到localStorage
 * @param {string} modelValue - 要保存的模型值
 */
function saveModelSelection(modelValue) {
    try {
        localStorage.setItem(STORAGE_KEY, modelValue);
    } catch (error) {
        console.warn('[ModelSelector] 无法保存到localStorage:', error);
    }
}

/**
 * 设置模型选择监听器，当用户改变选择时自动保存
 * @param {HTMLSelectElement} selectElement - select元素
 */
function setupModelSelectionListener(selectElement) {
    selectElement.addEventListener('change', function() {
        const selectedValue = this.value;
        if (selectedValue) {
            saveModelSelection(selectedValue);
        }
    });
    
    // 如果使用bootstrap-select，也监听其特定事件
    if (typeof $ !== 'undefined' && $.fn.selectpicker) {
        $(selectElement).on('changed.bs.select', function() {
            const selectedValue = this.value;
            if (selectedValue) {
                saveModelSelection(selectedValue);
            }
        });
    }
}

async function initialize(selectElementId) {
    const modelSelectElement = document.getElementById(selectElementId);

    if (!modelSelectElement) {
        console.error(`[ModelSelector] Element with ID '${selectElementId}' not found.`);
        return;
    }

    // Add a specific class for CSS targeting if needed
    modelSelectElement.classList.add('model-select-component');

    // Keep select empty and disabled initially
    modelSelectElement.innerHTML = ''; 
    modelSelectElement.disabled = true;

    try {
        const response = await fetch('/wkg/api/models');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const availableModels = await response.json();

        modelSelectElement.innerHTML = ''; // Clear loading state

        if (!availableModels || availableModels.length === 0) {
            modelSelectElement.innerHTML = '<option value="">无可用模型</option>';
            modelSelectElement.disabled = true;
            console.warn("[ModelSelector] No available models found.");
            return;
        }

        // 获取上次选择的模型
        const storedSelection = getStoredModelSelection();
        let selectedIndex = 0; // 默认选择第一个
        let foundStoredModel = false;

        availableModels.forEach((model, index) => {
            const option = document.createElement('option');
            option.value = model.value; 
            option.textContent = model.name;
            modelSelectElement.appendChild(option);
            
            // 检查是否匹配存储的选择
            if (storedSelection && model.value === storedSelection) {
                selectedIndex = index;
                foundStoredModel = true;
            }
        });

        if (modelSelectElement.options.length > 0) {
            // 设置选中项：优先使用存储的选择，否则使用第一个
            modelSelectElement.selectedIndex = selectedIndex;
            modelSelectElement.disabled = false;
            
            if (!foundStoredModel && storedSelection) {
                // 当存储的模型不可用时，清除localStorage中的选择并保存新的默认选择
                const defaultModel = availableModels[0].value;
                saveModelSelection(defaultModel);
            }

            // 设置选择变化监听器
            setupModelSelectionListener(modelSelectElement);

            // Initialize or refresh bootstrap-select ONLY after successful population
            if (typeof $ !== 'undefined' && $.fn.selectpicker) {
                // Check if it's already initialized
                if ($(modelSelectElement).data('selectpicker')) {
                    $(modelSelectElement).selectpicker('refresh');
                } else {
                    $(modelSelectElement).selectpicker(); 
                }
            } else {
                console.warn("[ModelSelector] jQuery or $.fn.selectpicker not found. Skipping bootstrap-select initialization.");
            }
        }

    } catch (error) {
        console.error("[ModelSelector] Failed to fetch or populate models:", error);
        modelSelectElement.innerHTML = '<option value="">加载失败</option>';
        modelSelectElement.disabled = true;
    }
}

// Export the initialize function
export { initialize };