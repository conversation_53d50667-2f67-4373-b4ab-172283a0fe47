package com.zibbava.edgemind.cortex.interceptor;

import com.zibbava.edgemind.cortex.dto.LicenseCheckResult;
import com.zibbava.edgemind.cortex.service.LicenseService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;
import java.util.List;

/**
 * 许可证检查拦截器
 * 用于在请求处理前检查系统是否已授权
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class LicenseCheckInterceptor implements HandlerInterceptor {

    @Value("${run.env}")
    private String runEnv;


    private final LicenseService licenseService;

    // 允许未授权访问的路径
    private final List<String> allowedPaths = Arrays.asList(
            "/wkg/license", // 授权页面
            "/wkg/license/content", // 授权页面内容
            "/wkg/api/license", // 授权相关API
            "/wkg/auth", // 认证相关API
            "/wkg/static", // 静态资源
            "/wkg/css", // 静态资源
            "/wkg/js", // 静态资源
            "/wkg/fonts", // 静态资源
            "/error", // 错误页面
            "/favicon.ico" // 网站图标
    );

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (runEnv.equals("demo")) {
            return true;
        }

        String requestPath = request.getRequestURI();

        // 检查是否是允许未授权访问的路径
        for (String allowedPath : allowedPaths) {
            if (requestPath.startsWith(allowedPath)) {
                return true;
            }
        }

        try {
            // 使用新的检查方法，避免重复查询数据库
            LicenseCheckResult checkResult = licenseService.checkLicenseStatus();

            if (!checkResult.isLicensed()) {
                // 根据失败原因重定向到相应页面
                String failReason = checkResult.getFailReason();

                if ("expired".equals(failReason)) {
                    log.warn("授权验证失败: 许可证已过期");
                    response.sendRedirect("/wkg/license/content?error=expired");
                } else if ("fingerprint".equals(failReason)) {
                    log.warn("授权验证失败: 硬件指纹不匹配");
                    response.sendRedirect("/wkg/license/content?error=fingerprint");
                } else if ("error".equals(failReason)) {
                    log.warn("授权验证失败: 系统错误");
                    response.sendRedirect("/wkg/license/content?error=system");
                } else {
                    // 未激活或其他原因
                    log.warn("系统未授权，重定向到授权页面");
                    response.sendRedirect("/wkg/license/content");
                }
                return false;
            }

            return true;
        } catch (Exception e) {
            // 如果检查授权状态时出错，记录错误但不允许访问
            log.error("检查授权状态时出错: {}", e.getMessage(), e);
            response.sendRedirect("/wkg/license/content?error=system");
            return false;
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 请求处理后的操作（如果需要）
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 请求完成后的操作（如果需要）
    }
}
