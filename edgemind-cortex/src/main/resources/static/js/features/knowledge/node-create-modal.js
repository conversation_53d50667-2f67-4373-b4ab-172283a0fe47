/**
 * 新建节点模态框交互逻辑
 */

// 全局变量
// DOM元素
// 注意：这些变量在knowledge_base.js中已经声明，这里使用而不重新声明
let nodeTypeButtons;

// Dropzone实例
let myDropzone = null;
let dropzoneInitialized = false;

function initDropzone() {
    // 如果已经初始化，则返回
    if (dropzoneInitialized && myDropzone) {
        myDropzone.removeAllFiles(true);
        return myDropzone;
    }

    // 检查元素是否存在
    const dropzoneElement = document.getElementById('documentDropzone');
    if (!dropzoneElement) {
        console.error('找不到Dropzone元素');
        return null;
    }

    // 初始化Dropzone
    try {
        myDropzone = new Dropzone("#documentDropzone", {
            url: "/wkg/api/knowledgebase/fake-upload", // 伪路径，实际不会使用这个路径上传
            paramName: "file",
            maxFilesize: 30, // MB
            acceptedFiles: ".pdf,.docx,.doc,.md,.txt,.html,.htm,.rtf,.odt,.xml,.mht,.xps,.djvu,.xlsx,.xls,.csv,.ods,.tsv,.pptx,.ppt,.odp",
            autoProcessQueue: false, // 禁止自动上传，非常重要
            uploadMultiple: false,
            maxFiles: 1,
            addRemoveLinks: false, // 关闭默认的删除链接，使用自定义按钮
            dictDefaultMessage: "拖放文件到此处或点击上传",
            dictFallbackMessage: "您的浏览器不支持拖放文件上传。",
            dictFileTooBig: "文件太大 ({{filesize}}MB)。最大支持: {{maxFilesize}}MB。",
            dictInvalidFileType: "不支持此类型的文件。",
            dictResponseError: "服务器返回 {{statusCode}} 错误。",
            dictCancelUpload: "取消上传",
            dictUploadCanceled: "上传已取消。",
            dictRemoveFile: "移除文件",
            dictMaxFilesExceeded: "您不能上传更多文件。",
            renameFile: function(file) {
                // 确保文件名没有危险字符
                return file.name.replace(/[^a-zA-Z0-9.\-]/g, '_');
            },
            previewTemplate: `
                <div class="dz-preview dz-file-preview">
                    <div class="file-card">
                        <div class="file-card-header">
                            <div class="file-icon">
                                <i class="bi bi-file-earmark-pdf"></i>
                            </div>
                            <div class="file-info">
                                <div class="file-name" data-dz-name></div>
                                <div class="file-size" data-dz-size></div>
                            </div>
                            <button type="button" class="file-remove-btn" data-dz-remove>
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                    <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>
                    <div class="dz-error-message"><span data-dz-errormessage></span></div>
                    <div class="dz-success-mark"><i class="bi bi-check-circle"></i></div>
                    <div class="dz-error-mark"><i class="bi bi-x-circle"></i></div>
                </div>
            `,

            init: function() {
                // 显示错误提示函数
                const showErrorAlert = function(message) {
                    console.log('Global showErrorAlert called with message:', message);
                    const alertEl = document.getElementById('uploadErrorAlert');
                    const messageEl = document.getElementById('uploadErrorMessage');

                    if (alertEl && messageEl) {
                        // 设置错误信息
                        messageEl.textContent = message;
                        // 强制显示错误提示
                        alertEl.classList.remove('d-none');
                        alertEl.style.display = 'block';
                        alertEl.style.setProperty('display', 'block', 'important');
                        console.log('Global error alert displayed successfully');
                        console.log('Alert element classes:', alertEl.className);
                        console.log('Alert element style:', alertEl.style.display);
                    } else {
                        console.log('Global error alert elements not found:', { alertEl, messageEl });
                    }
                };

                // 隐藏错误提示函数
                const hideErrorAlert = function() {
                    const alertEl = document.getElementById('uploadErrorAlert');
                    if (alertEl) {
                        alertEl.classList.remove('d-block');
                        alertEl.classList.add('d-none');
                    }
                };

                // 存储当前实例的引用
                const dropzone = this;

                // 当用户点击上传区域时，隐藏错误提示
                this.on("clickzone", function() {
                    hideErrorAlert();
                });

                // 当文件被添加时
                this.on("addedfile", function(file) {
                    // 隐藏之前的错误提示
                    hideErrorAlert();

                    // 检查文件大小（30MB = 30 * 1024 * 1024 bytes）
                    const maxSizeBytes = 30 * 1024 * 1024;
                    console.log(`File size check: ${file.name} size is ${file.size} bytes, max allowed is ${maxSizeBytes} bytes`);
                    
                    if (file.size > maxSizeBytes) {
                        console.log('File size exceeds limit, removing file and showing error');
                        // 移除文件
                        this.removeFile(file);
                        
                        // 计算文件大小（MB）
                        const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
                        
                        // 显示错误提示
                        showErrorAlert(`文件 "${file.name}" 大小为 ${fileSizeMB}MB，超过了最大限制 30MB。请选择较小的文件。`);
                        return;
                    }
                    
                    console.log('File size is within limit, proceeding...');

                    // 检查当前文件数量
                    if (this.files.length > 1) {
                        // 移除最后添加的文件
                        this.removeFile(file);

                        // 显示错误提示
                        showErrorAlert('只能上传一个文件，请先移除现有文件再上传新文件');
                        return;
                    }

                    // 设置节点名称为文件名（包含扩展名）
                    const createNodeNameInput = document.getElementById('createNodeNameInput');
                    if (createNodeNameInput && file.name) {
                        // 使用完整文件名（包含扩展名）作为节点名称
                        createNodeNameInput.value = file.name;
                    }

                    // 设置文件图标类型
                    const fileExtension = file.name.split('.').pop().toLowerCase();
                    const iconElement = file.previewElement.querySelector('.file-icon i');
                    if (iconElement) {
                        if (fileExtension === 'pdf') {
                            iconElement.className = 'bi bi-file-earmark-pdf';
                        } else if (['doc', 'docx', 'odt', 'rtf'].includes(fileExtension)) {
                            iconElement.className = 'bi bi-file-earmark-word';
                        } else if (fileExtension === 'md') {
                            iconElement.className = 'bi bi-markdown';
                        } else if (['txt', 'html', 'htm', 'xml', 'mht'].includes(fileExtension)) {
                            iconElement.className = 'bi bi-file-earmark-text';
                        } else if (['xls', 'xlsx', 'ods', 'csv', 'tsv'].includes(fileExtension)) {
                            iconElement.className = 'bi bi-file-earmark-spreadsheet';
                        } else if (['ppt', 'pptx', 'odp'].includes(fileExtension)) {
                            iconElement.className = 'bi bi-file-earmark-slides';
                        } else if (['xps', 'djvu'].includes(fileExtension)) {
                            iconElement.className = 'bi bi-file-earmark-richtext';
                        } else {
                            iconElement.className = 'bi bi-file-earmark';
                        }
                    }

                    // 如果有文件输入框，则清空它，避免重复上传
                    if (fileUploadInput) {
                        fileUploadInput.value = '';
                    }
                });



                // 错误处理
                this.on("error", function(file, errorMessage) {
                    showErrorAlert(errorMessage);
                    this.removeFile(file);
                });

                // 文件被移除时隐藏错误信息
                this.on("removedfile", function(file) {
                    // 隐藏错误提示
                    hideErrorAlert();

                    // 清空文件输入框
                    if (fileUploadInput) {
                        fileUploadInput.value = '';
                    }
                });

                // 禁止自动上传
                this.on("processing", function() {
                    this.options.autoProcessQueue = false;
                    return false; // 阻止处理
                });

                // 禁止上传完成后的处理
                this.on("complete", function() {
                    // 不做任何处理
                });
            }
        });

        // 标记为已初始化
        dropzoneInitialized = true;
        return myDropzone;
    } catch (error) {
        console.error('初始化Dropzone失败:', error);
        return null;
    }
}

// 初始化模态框
function initModal() {
    // 获取DOM元素
    nodeTypeButtons = document.querySelectorAll('.node-type-selector .btn');
    const folderView = document.getElementById('folderView');
    const fileView = document.getElementById('fileView');
    // 使用全局变量，不重新声明
    // createNodeTypeSelect、createNodeForm和fileUploadInput在knowledge_base.js中已经声明

    // 类型选择按钮点击事件
    nodeTypeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // 移除所有按钮的active类
            nodeTypeButtons.forEach(btn => btn.classList.remove('active'));

            // 添加当前按钮的active类
            this.classList.add('active');

            // 获取选择的类型
            const selectedType = this.getAttribute('data-type');

            // 更新隐藏的类型选择字段
            if (createNodeTypeSelect) {
                createNodeTypeSelect.value = selectedType;
            }

            // 切换视图
            const folderView = document.getElementById('folderView');
            const fileView = document.getElementById('fileView');

            if (selectedType === 'FOLDER') {
                if (folderView) folderView.style.display = 'block';
                if (fileView) fileView.style.display = 'none';
            } else {
                if (folderView) folderView.style.display = 'none';
                if (fileView) fileView.style.display = 'block';

                // 如果Dropzone还没有初始化，则初始化
                if (!dropzoneInitialized) {
                    myDropzone = initDropzone();
                    dropzoneInitialized = true;
                }
            }
        });
    });

    // 表单提交前处理
    if (createNodeForm) {
        createNodeForm.addEventListener('submit', function(event) {
            event.preventDefault();

            // 获取表单数据
            const formData = new FormData(this);
            const nodeType = createNodeTypeSelect.value;

            // 如果是文件类型，检查是否有文件
            if (nodeType === 'FILE') {
                // 如果Dropzone还没有初始化，则初始化
                if (!dropzoneInitialized && !myDropzone) {
                    myDropzone = initDropzone();
                    dropzoneInitialized = true;
                }

                const hasDropzoneFile = myDropzone && myDropzone.files && myDropzone.files.length > 0;
                const hasInputFile = fileUploadInput && fileUploadInput.files && fileUploadInput.files.length > 0;

                // 如果没有文件，显示错误
                if (!hasDropzoneFile && !hasInputFile) {
                    // 获取错误提示区域元素
                    const alertEl = document.getElementById('uploadErrorAlert');
                    const messageEl = document.getElementById('uploadErrorMessage');

                    if (alertEl && messageEl) {
                     // 设置错误信息
                     messageEl.textContent = '请选择要上传的文件';
                     // 强制显示错误提示
                     alertEl.classList.remove('d-none');
                     alertEl.style.display = 'block';
                     alertEl.style.setProperty('display', 'block', 'important');
                     console.log('No file upload error alert displayed successfully');
                 }
                    return;
                }

                // 如果有Dropzone文件但没有input文件，将Dropzone文件添加到input
                if (hasDropzoneFile && !hasInputFile && fileUploadInput) {
                    // 创建一个新的File对象
                    const file = myDropzone.files[0];

                    // 创建一个DataTransfer对象
                    const dataTransfer = new DataTransfer();
                    dataTransfer.items.add(file);

                    // 设置fileUploadInput的files属性
                    fileUploadInput.files = dataTransfer.files;
    
                }

                // 如果有input文件但没有Dropzone文件，不需要处理，直接使用input文件
                // 如果两者都有，优先使用input文件，忽略Dropzone文件
            }

            // 禁用Dropzone的自动上传功能，确保不会重复上传
            if (myDropzone) {
                myDropzone.options.autoProcessQueue = false;
            }

            // 添加调试日志
    

            // 调用原有的提交处理函数
            if (typeof handleCreateNodeSubmit === 'function') {
                // 添加标记，防止重复提交
                if (window.isSubmitting) {
        
                    return;
                }

                window.isSubmitting = true;
        

                try {
                    handleCreateNodeSubmit(event);
                } finally {
                    // 延迟重置标记，确保异步操作完成
                    setTimeout(() => {
                        window.isSubmitting = false;
                
                    }, 1000);
                }
            } else {
                console.error('handleCreateNodeSubmit函数未定义');

                // 获取错误提示区域元素
                const alertEl = document.getElementById('uploadErrorAlert');
                const messageEl = document.getElementById('uploadErrorMessage');

                if (alertEl && messageEl) {
                    // 设置错误信息
                    messageEl.textContent = '提交处理函数未定义，请联系管理员';
                    // 强制显示错误提示
                    alertEl.classList.remove('d-none');
                    alertEl.style.display = 'block';
                    alertEl.style.setProperty('display', 'block', 'important');
                    console.log('Submit handler error alert displayed');
                }
            }
        });
    }
}

// 在文档加载完成后运行
document.addEventListener('DOMContentLoaded', function() {
    // 监听模态框显示事件
    const nodeCreateModal = document.getElementById('nodeCreateModal');
    if (nodeCreateModal) {
        // 模态框显示事件
        nodeCreateModal.addEventListener('show.bs.modal', function() {
            // 初始化模态框（如果还没有初始化）
            if (!nodeTypeButtons) {
                initModal();
            }

            // 重置表单
            if (createNodeForm) {
                createNodeForm.reset();
            }

            // 重置Dropzone
            if (myDropzone) {
                myDropzone.removeAllFiles(true);
            }

            // 默认选择文件夹类型
            nodeTypeButtons.forEach(btn => {
                if (btn.getAttribute('data-type') === 'FOLDER') {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });

            // 显示文件夹视图
            const folderView = document.getElementById('folderView');
            const fileView = document.getElementById('fileView');
            if (folderView) folderView.style.display = 'block';
            if (fileView) fileView.style.display = 'none';

            // 设置默认类型为文件夹
            if (createNodeTypeSelect) {
                createNodeTypeSelect.value = 'FOLDER';
            }
        });

        // 模态框隐藏事件
        nodeCreateModal.addEventListener('hidden.bs.modal', function() {
            // 清理Dropzone
            if (myDropzone) {
                myDropzone.removeAllFiles(true);
            }
        });
    }
});
